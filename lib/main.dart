import 'package:flutter/material.dart';
import 'package:startwell/models/meal_model.dart';
import 'package:startwell/screens/cart_screen.dart';
import 'package:startwell/screens/login_screen.dart';
import 'package:startwell/screens/signup_screen.dart';
import 'package:startwell/screens/dashboard_screen.dart';
import 'package:startwell/screens/forgot_password_screen.dart';
import 'package:startwell/screens/meal_details_screen.dart';
// Removed meal_plan_screen import as it's no longer needed
import 'package:startwell/screens/main_screen.dart';
import 'package:startwell/screens/profile_settings_screen.dart';
import 'package:startwell/services/cart_storage_service.dart';
import 'package:startwell/services/meal_selection_manager.dart';
import 'package:startwell/services/student_profile_service.dart';
import 'package:startwell/services/user_profile_service.dart';
import 'app/routes/app_pages.dart';
import 'app/routes/app_routes.dart';
import 'package:startwell/themes/app_theme.dart';
import 'package:startwell/widgets/home/<USER>';
import 'dart:developer';
import 'package:get/get.dart';
import 'package:startwell/services/phonepe_official_service.dart';
import 'package:startwell/services/api/auth_service.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Project structure was refactored - consolidated theme directories and removed empty folders
  log("🚀 Starting StartWell app with refactored project structure");

  // Initialize authentication service first
  bool isAuthenticated = false;
  try {
    isAuthenticated = await AuthService.initialize();
    if (isAuthenticated) {
      log("✅ User authentication restored from storage");
    } else {
      log("❌ No valid authentication found - user needs to log in");
    }
  } catch (e) {
    log("⚠️ Error initializing authentication service: $e");
  }

  // Initialize the student profile service
  final studentProfileService = StudentProfileService();
  await studentProfileService.loadStudentProfiles();

  // Initialize the user profile service
  final userProfileService = UserProfileService();
  final userProfile = await userProfileService.getCurrentProfile();
  if (userProfile == null) {
    // Create a sample profile for demo purposes
    await userProfileService.createSampleProfile();
    log("✅ Created sample user profile");
  }

  // Initialize PhonePe Official Package
  try {
    final isInitialized = await PhonePeOfficialService.initialize(
      isUAT: true, // Set to false for production
    );
    if (isInitialized) {
      log("✅ PhonePe Official Package initialized successfully");
    } else {
      log("❌ PhonePe Official Package initialization failed");
    }
  } catch (e) {
    log("⚠️ Error initializing PhonePe Official Package: $e");
  }

  runApp(MyApp(isAuthenticated: isAuthenticated));
}

class MyApp extends StatelessWidget {
  final bool isAuthenticated;
  
  const MyApp({super.key, required this.isAuthenticated});

  @override
  Widget build(BuildContext context) {
    return GetMaterialApp(
      title: 'StartWell',
      // Route to dashboard if authenticated, otherwise login
      initialRoute: isAuthenticated ? Routes.DASHBOARD : Routes.LOGIN,
      getPages: AppPages.routes,
      debugShowCheckedModeBanner: false,
      theme: AppTheme.getTheme(),
      onGenerateRoute: (settings) {
        if (settings.name == '/') {
          // Redirect root path to login screen instead of using tabIndex for MainScreen
          return MaterialPageRoute(
            builder: (context) => const LoginScreen(),
          );
        }
        return null;
      },
    );
  }
}
