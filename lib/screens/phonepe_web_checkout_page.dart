import 'package:flutter/material.dart';
import 'package:phone_pe_pg/phone_pe_pg.dart';
import 'package:startwell/services/phonepe_official_service.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:get/get.dart';

class PhonePeWebCheckoutPage extends StatefulWidget {
  final String orderId;
  final double amount;
  final String customerName;
  final String customerPhone;

  const PhonePeWebCheckoutPage({
    Key? key,
    required this.orderId,
    required this.amount,
    required this.customerName,
    required this.customerPhone,
  }) : super(key: key);

  @override
  State<PhonePeWebCheckoutPage> createState() => _PhonePeWebCheckoutPageState();
}

class _PhonePeWebCheckoutPageState extends State<PhonePeWebCheckoutPage> {
  bool _isLoading = true;
  String _status = 'Initializing PhonePe Payment Gateway...';

  @override
  void initState() {
    super.initState();
    _initiateWebPayment();
  }

  Future<void> _initiateWebPayment() async {
    try {
      setState(() {
        _status = 'Creating payment request...';
      });

      // Create payment request for web checkout
      final paymentRequest = PaymentRequest(
        merchantId: PhonePeOfficialService.merchantId,
        merchantTransactionId: widget.orderId,
        merchantUserId: 'USER_${DateTime.now().millisecondsSinceEpoch}',
        amount: widget.amount,
        mobileNumber: widget.customerPhone,
        callbackUrl: 'https://webhook.site/callback-url', // Replace with your callback URL
        redirectUrl: 'https://webhook.site/redirect-url', // Replace with your redirect URL
        redirectMode: 'POST',
        paymentInstrument: PayPagePaymentInstrument(), // Web checkout instrument
      );

      setState(() {
        _status = 'Opening PhonePe Payment Gateway...';
      });

      // Get PhonePePg instance
      final phonePePg = PhonePePg(
        isUAT: true,
        saltKey: PhonePeOfficialService.saltKey,
        saltIndex: PhonePeOfficialService.saltIndex.toString(),
      );

      // Navigate to PhonePe web payment page
      final result = await Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => phonePePg.startPayPageTransaction(
            paymentRequest: paymentRequest,
            onPaymentComplete: (paymentStatusResponse, error) {
              _handlePaymentComplete(paymentStatusResponse, error);
            },
            appBar: AppBar(
              title: Text(
                'PhonePe Payment Gateway',
                style: GoogleFonts.poppins(),
              ),
              backgroundColor: Colors.purple,
              foregroundColor: Colors.white,
            ),
          ),
        ),
      );

    } catch (e) {
      setState(() {
        _isLoading = false;
        _status = 'Failed to initialize payment: $e';
      });
    }
  }

  void _handlePaymentComplete(dynamic paymentStatusResponse, dynamic error) {
    if (error != null) {
      // Payment failed
      Get.back(result: {
        'status': 'FAILED',
        'message': 'Payment failed: $error',
        'orderId': widget.orderId,
      });
    } else if (paymentStatusResponse != null) {
      // Payment completed (success or pending)
      Get.back(result: {
        'status': 'SUCCESS',
        'message': 'Payment completed successfully',
        'orderId': widget.orderId,
        'response': paymentStatusResponse,
      });
    } else {
      // Unknown result
      Get.back(result: {
        'status': 'UNKNOWN',
        'message': 'Payment result unknown',
        'orderId': widget.orderId,
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'PhonePe Payment',
          style: GoogleFonts.poppins(),
        ),
        backgroundColor: Colors.purple,
        foregroundColor: Colors.white,
      ),
      body: _isLoading 
        ? Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                                 Icon(Icons.payment, size: 64, color: Colors.purple),
                SizedBox(height: 24),
                CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.purple),
                ),
                SizedBox(height: 16),
                Text(
                  _status,
                  style: GoogleFonts.poppins(fontSize: 16),
                  textAlign: TextAlign.center,
                ),
                SizedBox(height: 24),
                Card(
                  margin: EdgeInsets.symmetric(horizontal: 32),
                  child: Padding(
                    padding: EdgeInsets.all(16),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          'Payment Details',
                          style: GoogleFonts.poppins(
                            fontSize: 18,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        Divider(),
                        _buildDetailRow('Amount', '₹${widget.amount.toStringAsFixed(2)}'),
                        _buildDetailRow('Order ID', widget.orderId),
                        _buildDetailRow('Customer', widget.customerName),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          )
        : Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.error, size: 64, color: Colors.red),
                SizedBox(height: 16),
                Text(
                  'Payment Initialization Failed',
                  style: GoogleFonts.poppins(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                SizedBox(height: 8),
                Text(
                  _status,
                  style: GoogleFonts.poppins(),
                  textAlign: TextAlign.center,
                ),
                SizedBox(height: 24),
                ElevatedButton(
                  onPressed: () => Navigator.pop(context),
                  child: Text('Go Back'),
                ),
              ],
            ),
          ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: GoogleFonts.poppins(color: Colors.grey[600]),
          ),
          Text(
            value,
            style: GoogleFonts.poppins(fontWeight: FontWeight.w500),
          ),
        ],
      ),
    );
  }
} 