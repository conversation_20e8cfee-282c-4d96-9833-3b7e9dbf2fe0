import 'dart:async';
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:startwell/models/user_profile.dart';
import 'package:startwell/services/event_bus_service.dart';
import 'package:startwell/themes/app_theme.dart';
import 'package:startwell/widgets/profile_avatar.dart';
import 'package:startwell/widgets/subscription/upcoming_meals_tab.dart';
import 'package:startwell/widgets/subscription/cancelled_meals_tab.dart';
import 'package:startwell/widgets/common/global_app_bar.dart';
import 'package:startwell/app/routes/app_routes.dart';

class MySubscriptionScreen extends StatefulWidget {
  final String? selectedStudentId;
  final DateTime? startDate;
  final DateTime? endDate;
  final int defaultTabIndex;
  final UserProfile? userProfile;

  const MySubscriptionScreen({
    Key? key,
    this.selectedStudentId,
    this.startDate,
    this.endDate,
    this.defaultTabIndex = 0,
    this.userProfile,
  }) : super(key: key);

  @override
  State<MySubscriptionScreen> createState() => _MySubscriptionScreenState();

  // Static method to find the nearest MySubscriptionScreen state
  static _MySubscriptionScreenState? of(BuildContext context) {
    return context.findAncestorStateOfType<_MySubscriptionScreenState>();
  }
}

class _MySubscriptionScreenState extends State<MySubscriptionScreen>
    with SingleTickerProviderStateMixin {
  // State variable to hold the currently relevant student ID
  String? _currentlySelectedStudentId;
  late TabController _tabController;
  // Remove the GlobalKey field
  // final GlobalKey<CancelledMealsTabState> _cancelledMealsTabKey =
  //     GlobalKey<CancelledMealsTabState>();
  
  // Add a flag to trigger refresh
  bool _refreshCancelledMeals = false;

  @override
  void initState() {
    super.initState();
    // Initialize the state variable with the widget's initial value
    _currentlySelectedStudentId = widget.selectedStudentId;

    // Initialize tab controller
    _tabController = TabController(
      length: 2,
      vsync: this,
      initialIndex: widget.defaultTabIndex,
    );

    // Add listener to handle tab changes
    _tabController.addListener(_handleTabChange);

    log(
      "MySubscriptionScreen initState - _currentlySelectedStudentId set to: ${_currentlySelectedStudentId ?? 'null'}",
    );
    log("MySubscriptionScreen startDate: ${widget.startDate}");
    log("MySubscriptionScreen endDate: ${widget.endDate}");
    log("MySubscriptionScreen defaultTabIndex: ${widget.defaultTabIndex}");
  }

  @override
  void dispose() {
    _tabController.removeListener(_handleTabChange);
    _tabController.dispose();
    super.dispose();
  }

  // Handle tab changes
  void _handleTabChange() {
    if (_tabController.indexIsChanging) {
      log("[cancelled_meal_data_flow] Tab changed to: ${_tabController.index}");

      // If switched to Cancelled Meals tab (index 1), refresh the data
      if (_tabController.index == 1) {
        log(
          "[cancelled_meal_data_flow] Switched to Cancelled Meals tab, triggering refresh flag",
        );
        setState(() {
          _refreshCancelledMeals = true;
        });
      }
    }
  }

  // Public method to change tabs programmatically
  void switchToTab(int index) {
    if (index >= 0 && index < _tabController.length) {
      log(
        "[cancelled_meal_data_flow] Programmatically switching to tab: $index",
      );
      _tabController.animateTo(index);

      // Force refresh immediately when switching to Cancelled tab
      if (index == 1) {
        // Delay to allow animation to complete before refresh
        Future.delayed(const Duration(milliseconds: 500), () {
          log("[cancelled_meal_data_flow] Forced refresh after tab switch");
          // The refresh logic is now handled by the _refreshCancelledMeals flag
          // and the CancelledMealsTab widget's refresh callback.
          // No direct key access needed here.
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // Create a local key for CancelledMealsTab if you still need to access its state, otherwise remove the key
    // final cancelledMealsTabKey = GlobalKey<CancelledMealsTabState>();
    return WillPopScope(
      onWillPop: () async {
        Navigator.of(context)
            .pushNamedAndRemoveUntil(Routes.main, (route) => false);
        return false;
      },
      child: Scaffold(
        backgroundColor: AppTheme.white,
        appBar: GlobalAppBar(
          title: 'My Subscription',
          showBackButton: true,
          hideMenuAndCart: true,
          onBackPressed: () => Navigator.of(context)
              .pushNamedAndRemoveUntil(Routes.main, (route) => false),
          userProfile: widget.userProfile,
        ),
        body: Container(
          decoration: BoxDecoration(
            color: Colors.white,
            boxShadow: [
              BoxShadow(
                color: AppTheme.deepPurple.withOpacity(0.05),
                blurRadius: 8,
                offset: const Offset(0, -2),
              ),
            ],
          ),
          child: Column(
            children: [
              Container(
                decoration: BoxDecoration(
                  color: Colors.white,
                ),
                child: TabBar(
                  controller: _tabController,
                  labelColor: AppTheme.purple,
                  unselectedLabelColor: AppTheme.textMedium,
                  indicatorColor: AppTheme.purple,
                  indicatorWeight: 3,
                  labelStyle: GoogleFonts.poppins(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                  ),
                  unselectedLabelStyle: GoogleFonts.poppins(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                  dividerColor: Colors.transparent,
                  tabs: const [
                    Tab(text: 'Upcoming Meals'),
                    Tab(text: 'Cancelled Meals'),
                  ],
                ),
              ),
              Expanded(
                child: TabBarView(
                  controller: _tabController,
                  children: [
                    // Upcoming Meals Tab
                    UpcomingMealsTab(
                      selectedStudentId: _currentlySelectedStudentId,
                      startDate: widget.startDate,
                      endDate: widget.endDate,
                    ),
                    // Cancelled Meals Tab
                    CancelledMealsTab(
                      studentId: _currentlySelectedStudentId,
                      // Add a callback to trigger refresh
                      refresh: _refreshCancelledMeals,
                      onRefreshed: () {
                        setState(() {
                          _refreshCancelledMeals = false;
                        });
                      },
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
