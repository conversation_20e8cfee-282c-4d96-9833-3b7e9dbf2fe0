import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:startwell/models/student_model.dart';
import 'package:startwell/themes/app_theme.dart';
import 'package:startwell/widgets/common/school_selector_dropdown.dart';
import 'package:startwell/services/dropdown_service.dart';
import 'package:startwell/services/api/student_profile_api_service.dart';
import 'package:startwell/services/api/api_response.dart';

class StudentProfileFormScreen extends StatefulWidget {
  final Student? student;
  final Function(Map<String, dynamic>) onSave;
  final VoidCallback onRefresh;

  const StudentProfileFormScreen({
    Key? key,
    this.student,
    required this.onSave,
    required this.onRefresh,
  }) : super(key: key);

  @override
  State<StudentProfileFormScreen> createState() => _StudentProfileFormScreenState();
}

class _StudentProfileFormScreenState extends State<StudentProfileFormScreen> {
  // Form controllers
  final _schoolNameController = TextEditingController();
  final _studentNameController = TextEditingController();
  final _classController = TextEditingController();
  final _divisionController = TextEditingController();
  final _floorController = TextEditingController();
  final _allergiesController = TextEditingController();

  // Form key for validation
  final _formKey = GlobalKey<FormState>();

  // For editing an existing student
  bool _isEditing = false;
  String? selectedSchool;
  bool _isSaving = false;

  // Dropdown state
  List<Map<String, String>> _classOptions = [];
  List<Map<String, String>> _divisionOptions = [];
  List<Map<String, String>> _floorOptions = [];
  String? _selectedClassValue;
  String? _selectedDivisionValue;
  String? _selectedFloorValue;
  bool _isDropdownLoading = false;
  String? _dropdownError;

  @override
  void initState() {
    super.initState();
    _initializeForm();
    _fetchDropdowns();
  }

  void _initializeForm() {
    _isEditing = widget.student != null;
    
    if (_isEditing && widget.student != null) {
      // Pre-fill the form for editing
      selectedSchool = widget.student!.schoolName;
      _schoolNameController.text = widget.student!.schoolName;
      _studentNameController.text = widget.student!.name;
      _classController.text = widget.student!.className;
      _divisionController.text = widget.student!.division;
      _floorController.text = widget.student!.floor;
      _allergiesController.text = widget.student!.allergies;
      // Prefill dropdowns
      _selectedClassValue = widget.student!.className;
      _selectedDivisionValue = widget.student!.division;
      _selectedFloorValue = widget.student!.floor;
    } else {
      // Clear the form for a new student
      selectedSchool = null;
      _schoolNameController.clear();
      _studentNameController.clear();
      _classController.clear();
      _divisionController.clear();
      _floorController.clear();
      _allergiesController.clear();
      _selectedClassValue = null;
      _selectedDivisionValue = null;
      _selectedFloorValue = null;
    }
  }

  Future<void> _fetchDropdowns() async {
    setState(() {
      _isDropdownLoading = true;
      _dropdownError = null;
    });
    try {
      final data = await DropdownService.fetchDropdowns();
      setState(() {
        _classOptions = data['classes'] ?? [];
        _divisionOptions = data['divisions'] ?? [];
        _floorOptions = data['floors'] ?? [];
        _isDropdownLoading = false;
      });
    } catch (e) {
      setState(() {
        _dropdownError = e.toString();
        _isDropdownLoading = false;
      });
    }
  }

  @override
  void dispose() {
    _schoolNameController.dispose();
    _studentNameController.dispose();
    _classController.dispose();
    _divisionController.dispose();
    _floorController.dispose();
    _allergiesController.dispose();
    super.dispose();
  }

  void _handleSave() async {
    if (_formKey.currentState!.validate()) {
      setState(() {
        _isSaving = true;
      });

      try {
        final formData = {
          'isEditing': _isEditing,
          'studentId': widget.student != null && widget.student!.id != null ? widget.student!.id.toString() : '',
          'schoolName': selectedSchool ?? '',
          'studentName': _studentNameController.text,
          'class': _selectedClassValue ?? '',
          'division': _selectedDivisionValue ?? '',
          'floor': _selectedFloorValue ?? '',
          'allergies': _allergiesController.text,
        };
        print('formData: $formData');

        // Call the new API service
        final response = await StudentProfileApiService.saveStudentProfile(profileData: formData);
        if (response.success) {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Student profile saved successfully!', style: GoogleFonts.poppins()),
                backgroundColor: Colors.green,
              ),
            );
            widget.onSave(formData);
            Navigator.of(context).pop();
          }
        } else {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Failed to save profile: ${response.message}', style: GoogleFonts.poppins()),
                backgroundColor: Colors.red,
              ),
            );
          }
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Error: ${e.toString()}', style: GoogleFonts.poppins()),
              backgroundColor: Colors.red,
            ),
          );
        }
      } finally {
        if (mounted) {
          setState(() {
            _isSaving = false;
          });
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          _isEditing ? 'Edit Student Profile' : 'Create Student Profile',
          style: GoogleFonts.poppins(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: Colors.white,
          ),
        ),
        flexibleSpace: Container(
          decoration: BoxDecoration(
            gradient: AppTheme.purpleToDeepPurple,
          ),
        ),
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: SafeArea(
        child: _isDropdownLoading
            ? Center(child: CircularProgressIndicator())
            : _dropdownError != null
                ? Center(child: Text('Failed to load dropdowns: \n$_dropdownError!'))
                : SingleChildScrollView(
                    padding: const EdgeInsets.all(20),
                    child: Form(
                      key: _formKey,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // School Name Dropdown
                          SchoolSelectorDropdown(
                            selectedSchool: selectedSchool,
                            onSchoolSelected: (value) {
                              print('School selected in form: $value');
                              setState(() {
                                selectedSchool = value;
                                _schoolNameController.text = value ?? '';
                              });
                              print('selectedSchool after setState in form: $selectedSchool');
                            },
                            showRefreshButton: true,
                            onRefresh: widget.onRefresh,
                          ),
                          // Hidden text field for form validation
                          if (selectedSchool == null)
                            TextFormField(
                              enabled: false,
                              decoration: const InputDecoration(
                                border: InputBorder.none,
                              ),
                              validator: (value) {
                                if (selectedSchool == null || selectedSchool!.isEmpty) {
                                  return 'Please select a school';
                                }
                                return null;
                              },
                            ),
                          const SizedBox(height: 16),

                          // Student Name
                          _buildFormField(
                            controller: _studentNameController,
                            labelText: 'Student Name',
                            icon: Icons.person,
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'Please enter student name';
                              }
                              return null;
                            },
                          ),
                          const SizedBox(height: 16),
                          // Class Dropdown
                          CustomSelectorDropdown(
                            label: 'Class',
                            value: _selectedClassValue,
                            options: _classOptions,
                            icon: Icons.class_,
                            iconBgColor: AppTheme.purple,
                            hintText: 'Select class',
                            onChanged: (val) => setState(() => _selectedClassValue = val),
                          ),
                          // Division Dropdown
                          CustomSelectorDropdown(
                            label: 'Division',
                            value: _selectedDivisionValue,
                            options: _divisionOptions,
                            icon: Icons.view_column,
                            iconBgColor: AppTheme.purple,
                            hintText: 'Select division',
                            onChanged: (val) => setState(() => _selectedDivisionValue = val),
                          ),
                          // Floor Dropdown
                          CustomSelectorDropdown(
                            label: 'Floor',
                            value: _selectedFloorValue,
                            options: _floorOptions,
                            icon: Icons.layers,
                            iconBgColor: AppTheme.purple,
                            hintText: 'Select floor',
                            onChanged: (val) => setState(() => _selectedFloorValue = val),
                          ),
                          const SizedBox(height: 16),

                          // Medical Allergies (optional)
                          _buildFormField(
                            controller: _allergiesController,
                            labelText: 'Medical Allergies (Optional)',
                            icon: Icons.healing,
                            isOptional: true,
                          ),
                          const SizedBox(height: 32),
                          
                          // Gradient Create/Update button below the form
                          SizedBox(
                            width: double.infinity,
                            child: Container(
                              decoration: BoxDecoration(
                                gradient: AppTheme.purpleToDeepPurple,
                                borderRadius: BorderRadius.circular(50),
                                boxShadow: [
                                  BoxShadow(
                                    color: AppTheme.deepPurple.withOpacity(0.12),
                                    blurRadius: 8,
                                    offset: Offset(0, 2),
                                  ),
                                ],
                              ),
                              child: Material(
                                color: Colors.transparent,
                                child: InkWell(
                                  borderRadius: BorderRadius.circular(50),
                                  onTap: _isSaving ? null : _handleSave,
                                  child: Padding(
                                    padding: const EdgeInsets.symmetric(vertical: 16),
                                    child: Center(
                                      child: _isSaving
                                          ? SizedBox(
                                              width: 20,
                                              height: 20,
                                              child: CircularProgressIndicator(
                                                strokeWidth: 2,
                                                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                                              ),
                                            )
                                          : Text(
                                              _isEditing ? 'Update' : 'Create',
                                              style: GoogleFonts.poppins(
                                                fontSize: 16,
                                                fontWeight: FontWeight.w600,
                                                color: Colors.white,
                                                letterSpacing: 0.5,
                                              ),
                                            ),
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
      ),
    );
  }

  // Helper method to build form fields with consistent styling
  Widget _buildFormField({
    required TextEditingController controller,
    required String labelText,
    required IconData icon,
    int maxLines = 1,
    bool isOptional = false,
    String? Function(String?)? validator,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: AppTheme.deepPurple.withOpacity(0.04),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TextFormField(
        controller: controller,
        decoration: InputDecoration(
          labelText: labelText,
          labelStyle: GoogleFonts.poppins(
            color: AppTheme.textMedium,
            fontSize: 14,
          ),
          prefixIcon: Icon(
            icon,
            color: AppTheme.purple,
            size: 20,
          ),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(color: Colors.grey.shade200, width: 1),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(color: Colors.grey.shade200, width: 1),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(color: AppTheme.purple, width: 1.5),
          ),
          contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 16),
          floatingLabelBehavior: FloatingLabelBehavior.auto,
          suffixIcon: isOptional
              ? Padding(
                  padding: const EdgeInsets.only(right: 12.0),
                  child: Text(
                    'Optional',
                    style: GoogleFonts.poppins(
                      fontSize: 12,
                      color: Colors.grey.shade500,
                      fontStyle: FontStyle.italic,
                    ),
                  ),
                )
              : null,
          suffixIconConstraints: BoxConstraints(minWidth: 0, minHeight: 0),
        ),
        maxLines: maxLines,
        validator: validator,
        style: GoogleFonts.poppins(fontSize: 14, color: AppTheme.textDark),
      ),
    );
  }
} 

class CustomSelectorDropdown extends StatelessWidget {
  final String label;
  final String? value;
  final List<Map<String, String>> options;
  final IconData icon;
  final Color iconBgColor;
  final String hintText;
  final ValueChanged<String?> onChanged;

  const CustomSelectorDropdown({
    Key? key,
    required this.label,
    required this.value,
    required this.options,
    required this.icon,
    required this.iconBgColor,
    required this.hintText,
    required this.onChanged,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return InkWell(
      borderRadius: BorderRadius.circular(16),
      onTap: () async {
        final selected = await showModalBottomSheet<String>(
          context: context,
          backgroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.vertical(top: Radius.circular(24)),
          ),
          builder: (context) {
            return SafeArea(
              child: SizedBox(
                height: MediaQuery.of(context).size.height * 0.6, // 60% of screen height
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Padding(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      child: Text(
                        label,
                        style: TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
                      ),
                    ),
                    Expanded(
                      child: GridView.builder(
                        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                          crossAxisCount: 2,
                          mainAxisSpacing: 8,
                          crossAxisSpacing: 8,
                          childAspectRatio: 2.8,
                        ),
                        itemCount: options.length,
                        itemBuilder: (context, idx) {
                          final opt = options[idx];
                          final isSelected = value == opt['value'];
                          return GestureDetector(
                            onTap: () => Navigator.pop(context, opt['value']),
                            child: Container(
                              decoration: BoxDecoration(
                                color: isSelected ? Theme.of(context).primaryColor.withOpacity(0.12) : Colors.grey[100],
                                borderRadius: BorderRadius.circular(12),
                                border: Border.all(
                                  color: isSelected ? Theme.of(context).primaryColor : Colors.grey.shade300,
                                  width: isSelected ? 2 : 1,
                                ),
                              ),
                              padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 8),
                              child: Center(
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Flexible(
                                      child: Text(
                                        opt['label'] ?? '',
                                        style: TextStyle(
                                          fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                                          color: isSelected ? Theme.of(context).primaryColor : Colors.black87,
                                        ),
                                        overflow: TextOverflow.ellipsis,
                                      ),
                                    ),
                                    if (isSelected) ...[
                                      const SizedBox(width: 6),
                                      Icon(Icons.check, color: Theme.of(context).primaryColor, size: 18),
                                    ]
                                  ],
                                ),
                              ),
                            ),
                          );
                        },
                      ),
                    ),
                    SizedBox(height: 16),
                  ],
                ),
              ),
            );
          },
        );
        if (selected != null) onChanged(selected);
      },
      child: Container(
        margin: const EdgeInsets.only(bottom: 16),
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 18),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withOpacity(0.08),
              blurRadius: 8,
              offset: Offset(0, 2),
            ),
          ],
          border: Border.all(color: Colors.grey.shade200),
        ),
        child: Row(
          children: [
            Container(
              decoration: BoxDecoration(
                color: iconBgColor,
                borderRadius: BorderRadius.circular(10),
              ),
              padding: const EdgeInsets.all(8),
              child: Icon(icon, color: Colors.white, size: 22),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Text(
                value != null && value!.isNotEmpty
                    ? options.firstWhere((o) => o['value'] == value, orElse: () => {'label': ''})['label'] ?? ''
                    : hintText,
                style: TextStyle(
                  fontSize: 16,
                  color: value != null && value!.isNotEmpty ? Colors.black : Colors.grey[500],
                  fontWeight: FontWeight.w500,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            const SizedBox(width: 8),
            Icon(Icons.keyboard_arrow_down_rounded, color: Colors.deepPurple, size: 28),
          ],
        ),
      ),
    );
  }
} 