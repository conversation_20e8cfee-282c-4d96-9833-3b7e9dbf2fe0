import 'package:flutter/material.dart';
import 'package:startwell/services/working_days_service.dart';
import 'package:startwell/widgets/calendar/working_days_calendar.dart';
import 'package:startwell/themes/app_theme.dart';
import 'dart:developer' as dev;

/// Demo screen to test the working days API integration
class WorkingDaysDemoScreen extends StatefulWidget {
  const WorkingDaysDemoScreen({Key? key}) : super(key: key);

  @override
  State<WorkingDaysDemoScreen> createState() => _WorkingDaysDemoScreenState();
}

class _WorkingDaysDemoScreenState extends State<WorkingDaysDemoScreen> {
  String _selectedMealType = 'lunch';
  List<bool> _selectedWeekdays = [true, true, true, true, true, false, false];
  DateTime? _selectedDate;
  
  final List<String> _mealTypes = ['breakfast', 'lunch', 'dinner'];
  final List<String> _weekdayNames = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Working Days API Demo'),
        backgroundColor: AppTheme.purple,
        foregroundColor: Colors.white,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Meal Type Selection
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Select Meal Type',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 12),
                    Wrap(
                      spacing: 8,
                      children: _mealTypes.map((mealType) {
                        return ChoiceChip(
                          label: Text(mealType.toUpperCase()),
                          selected: _selectedMealType == mealType,
                          onSelected: (selected) {
                            if (selected) {
                              setState(() {
                                _selectedMealType = mealType;
                              });
                            }
                          },
                          selectedColor: AppTheme.purple.withOpacity(0.2),
                          labelStyle: TextStyle(
                            color: _selectedMealType == mealType 
                                ? AppTheme.purple 
                                : Colors.grey.shade700,
                          ),
                        );
                      }).toList(),
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Custom Days Selection
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Custom Days (Optional)',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 12),
                    Wrap(
                      spacing: 8,
                      children: List.generate(7, (index) {
                        return FilterChip(
                          label: Text(_weekdayNames[index]),
                          selected: _selectedWeekdays[index],
                          onSelected: (selected) {
                            setState(() {
                              _selectedWeekdays[index] = selected;
                            });
                          },
                          selectedColor: AppTheme.purple.withOpacity(0.2),
                          labelStyle: TextStyle(
                            color: _selectedWeekdays[index] 
                                ? AppTheme.purple 
                                : Colors.grey.shade700,
                          ),
                        );
                      }),
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // API Test Buttons
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'API Test Actions',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 12),
                    Row(
                      children: [
                        Expanded(
                          child: ElevatedButton(
                            onPressed: _testCurrentMonth,
                            style: ElevatedButton.styleFrom(
                              backgroundColor: AppTheme.purple,
                              foregroundColor: Colors.white,
                            ),
                            child: const Text('Test Current Month'),
                          ),
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: ElevatedButton(
                            onPressed: _testWithCustomDays,
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.orange,
                              foregroundColor: Colors.white,
                            ),
                            child: const Text('Test Custom Days'),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Calendar Widget
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Working Days Calendar',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 12),
                    WorkingDaysCalendar(
                      mealType: _selectedMealType,
                      customDays: _getSelectedCustomDays(),
                      onDateSelected: (date) {
                        setState(() {
                          _selectedDate = date;
                        });
                        dev.log('📅 Selected date: $date');
                      },
                    ),
                  ],
                ),
              ),
            ),
            
            // Selected Date Display
            if (_selectedDate != null)
              Card(
                color: Colors.green.shade50,
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Row(
                    children: [
                      Icon(Icons.check_circle, color: Colors.green.shade600),
                      const SizedBox(width: 8),
                      Text(
                        'Selected Date: ${_selectedDate!.day}/${_selectedDate!.month}/${_selectedDate!.year}',
                        style: TextStyle(
                          color: Colors.green.shade700,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
  
  /// Get selected custom days as list of integers
  List<int>? _getSelectedCustomDays() {
    final customDays = WorkingDaysService.convertSelectedWeekdaysToCustomDays(_selectedWeekdays);
    return customDays.isEmpty ? null : customDays;
  }
  
  /// Test current month API call
  Future<void> _testCurrentMonth() async {
    try {
      dev.log('🧪 Testing current month API call...');
      
      final response = await WorkingDaysService.getCurrentMonthWorkingDays(
        mealType: _selectedMealType,
      );
      
      dev.log('✅ Current month test successful: ${response.enabledDates.length} dates');
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('✅ Success: ${response.enabledDates.length} enabled dates loaded'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      dev.log('❌ Current month test failed: $e');
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('❌ Error: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
  
  /// Test custom days API call
  Future<void> _testWithCustomDays() async {
    try {
      final customDays = _getSelectedCustomDays();
      dev.log('🧪 Testing custom days API call with days: $customDays');
      
      final response = await WorkingDaysService.getCurrentMonthWorkingDays(
        mealType: _selectedMealType,
        customDays: customDays,
      );
      
      dev.log('✅ Custom days test successful: ${response.enabledDates.length} dates');
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('✅ Custom days success: ${response.enabledDates.length} enabled dates'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      dev.log('❌ Custom days test failed: $e');
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('❌ Custom days error: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
