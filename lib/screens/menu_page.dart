import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:startwell/themes/app_theme.dart';
import 'package:startwell/widgets/home/<USER>';
import 'package:startwell/widgets/common/global_app_bar.dart';

class MenuPage extends StatelessWidget {
  final bool isDialogMode;
  const MenuPage({Key? key, this.isDialogMode = false}) : super(key: key);

  // Static method to show the menu as a dialog
  static void showAsDialog(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final isSmallScreen = screenSize.width < 360;
    final isTablet = screenSize.width >= 768;

    showDialog<void>(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return Dialog(
          insetPadding: EdgeInsets.symmetric(
            horizontal: isSmallScreen
                ? 8
                : isTablet
                    ? 24
                    : 16,
            vertical: isSmallScreen ? 16 : 24,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          child: Container(
            width: double.maxFinite,
            constraints: BoxConstraints(
              maxHeight: screenSize.height * 0.85,
              maxWidth: isTablet ? 800 : double.infinity,
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Header
                Container(
                  padding: EdgeInsets.symmetric(
                    vertical: isSmallScreen ? 12 : 16,
                    horizontal: isSmallScreen ? 16 : 20,
                  ),
                  decoration: const BoxDecoration(
                    gradient: AppTheme.purpleToDeepPurple,
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(16),
                      topRight: Radius.circular(16),
                    ),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.restaurant_menu,
                        color: Colors.white,
                        size: isSmallScreen ? 20 : 24,
                      ),
                      SizedBox(width: isSmallScreen ? 8 : 12),
                      Expanded(
                        child: Text(
                          'Weekly Menu',
                          style: GoogleFonts.poppins(
                            fontSize: isSmallScreen
                                ? 16
                                : isTablet
                                    ? 20
                                    : 18,
                            fontWeight: FontWeight.w600,
                            color: Colors.white,
                          ),
                        ),
                      ),
                      IconButton(
                        onPressed: () => Navigator.of(context).pop(),
                        icon: Icon(
                          Icons.close,
                          color: Colors.white,
                          size: isSmallScreen ? 20 : 24,
                        ),
                        padding: EdgeInsets.zero,
                        constraints: const BoxConstraints(),
                      ),
                    ],
                  ),
                ),

                // Content
                Expanded(
                  child: MenuPage(isDialogMode: true),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final isSmallScreen = screenSize.width < 360;
    final isTablet = screenSize.width >= 768;

    final orange = AppTheme.orange;
    final purple = AppTheme.purple;
    final peach = AppTheme.lightOrange;
    final white = AppTheme.white;
    final days = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri'];
    // Hardcoded menu data for 21st to 25th April
    final breakfastMenu = [
      [
        _MenuItem('Poha', isMealOfDay: true),
        _MenuItem('Upma'),
        _MenuItem('NEW'),
      ],
      [
        _MenuItem('Idli Sambhar'),
        _MenuItem('Pancakes', isMealOfDay: true),
        _MenuItem('Fruit Bowl'),
      ],
      [
        _MenuItem('Aloo Paratha', isMealOfDay: true),
        _MenuItem('Croissant'),
        _MenuItem('Yogurt'),
      ],
      [
        _MenuItem('Dhokla'),
        _MenuItem('Waffles', isMealOfDay: true),
        _MenuItem('NEW'),
      ],
      [
        _MenuItem('Thepla', isMealOfDay: true),
        _MenuItem('Bagel'),
        _MenuItem('Granola'),
      ],
    ];
    final lunchMenu = [
      [
        _MenuItem('Dal Rice', isMealOfDay: true),
        _MenuItem('Pasta'),
        _MenuItem('Salad'),
      ],
      [
        _MenuItem('Rajma Chawal'),
        _MenuItem('Pizza', isMealOfDay: true),
        _MenuItem('Soup'),
      ],
      [
        _MenuItem('Paneer Curry', isMealOfDay: true),
        _MenuItem('Burger'),
        _MenuItem('Coleslaw'),
      ],
      [
        _MenuItem('Chole Bhature'),
        _MenuItem('Tacos', isMealOfDay: true),
        _MenuItem('Nachos'),
      ],
      [
        _MenuItem('Veg Biryani', isMealOfDay: true),
        _MenuItem('Quesadilla'),
        _MenuItem('Fruit Cup'),
      ],
    ];

    // Build the content widget
    Widget contentWidget = SingleChildScrollView(
      padding: EdgeInsets.symmetric(
        horizontal: isSmallScreen
            ? 12
            : isTablet
                ? 24
                : 20,
        vertical: isSmallScreen
            ? 16
            : isTablet
                ? 28
                : 24,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Date display
          Container(
            margin: EdgeInsets.only(bottom: isSmallScreen ? 16 : 24),
            padding: EdgeInsets.symmetric(
              horizontal: isSmallScreen ? 12 : 16,
              vertical: isSmallScreen ? 8 : 10,
            ),
            decoration: BoxDecoration(
              color: AppTheme.purple.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: AppTheme.purple.withOpacity(0.2),
                width: 1.5,
              ),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.calendar_today_rounded,
                  size: isSmallScreen
                      ? 16
                      : isTablet
                          ? 22
                          : 20,
                  color: AppTheme.purple,
                ),
                SizedBox(width: isSmallScreen ? 8 : 10),
                Text(
                  '21st to 25th April',
                  style: GoogleFonts.poppins(
                    fontSize: isSmallScreen
                        ? 12
                        : isTablet
                            ? 16
                            : 14,
                    fontWeight: FontWeight.w600,
                    color: AppTheme.purple,
                  ),
                ),
              ],
            ),
          ),

          // Breakfast Section
          Container(
            margin: const EdgeInsets.only(bottom: 32),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Section header
                Container(
                  margin: const EdgeInsets.only(bottom: 16),
                  decoration: BoxDecoration(
                    border: Border(
                      left: BorderSide(
                        color: AppTheme.purple,
                        width: 4,
                      ),
                    ),
                  ),
                  padding: const EdgeInsets.only(left: 12),
                  child: Row(
                    children: [
                      Icon(
                        Icons.ramen_dining,
                        color: Colors.pink,
                        size: 24,
                      ),
                      const SizedBox(width: 12),
                      Text(
                        'Breakfast',
                        style: GoogleFonts.poppins(
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                          color: AppTheme.textDark,
                          letterSpacing: 0.3,
                        ),
                      ),
                    ],
                  ),
                ),
                _EnhancedMenuTable(
                  days: days,
                  sectionTitles: const [
                    'Indian Breakfast',
                    'International Breakfast',
                    'Side',
                  ],
                  menu: breakfastMenu,
                  highlightColor: orange,
                ),
              ],
            ),
          ),

          // Lunch Section
          Container(
            margin: const EdgeInsets.only(bottom: 32),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Section header
                Container(
                  margin: const EdgeInsets.only(bottom: 16),
                  decoration: BoxDecoration(
                    border: Border(
                      left: BorderSide(
                        color: AppTheme.purple,
                        width: 4,
                      ),
                    ),
                  ),
                  padding: const EdgeInsets.only(left: 12),
                  child: Row(
                    children: [
                      Icon(
                        Icons.flatware_rounded,
                        color: Colors.green,
                        size: 24,
                      ),
                      const SizedBox(width: 12),
                      Text(
                        'Lunch',
                        style: GoogleFonts.poppins(
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                          color: AppTheme.textDark,
                          letterSpacing: 0.3,
                        ),
                      ),
                    ],
                  ),
                ),
                _EnhancedMenuTable(
                  days: days,
                  sectionTitles: const [
                    'Indian Lunch',
                    'International Lunch',
                    'Side',
                  ],
                  menu: lunchMenu,
                  highlightColor: orange,
                ),
              ],
            ),
          ),

          // Jain Menu Section
          Container(
            margin: const EdgeInsets.only(bottom: 32),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Section header
                Container(
                  margin: const EdgeInsets.only(bottom: 16),
                  decoration: BoxDecoration(
                    border: Border(
                      left: BorderSide(
                        color: AppTheme.purple,
                        width: 4,
                      ),
                    ),
                  ),
                  padding: const EdgeInsets.only(left: 12),
                  child: Row(
                    children: [
                      Icon(
                        Icons.spa_outlined,
                        color: Colors.deepPurple,
                        size: 24,
                      ),
                      const SizedBox(width: 12),
                      Text(
                        'Jain Menu',
                        style: GoogleFonts.poppins(
                          fontSize: 20,
                          fontWeight: FontWeight.w600,
                          color: AppTheme.textDark,
                          letterSpacing: 0.3,
                        ),
                      ),
                    ],
                  ),
                ),
                _EnhancedMenuTable(
                  days: days,
                  sectionTitles: const [
                    'Jain Breakfast',
                    'Jain Lunch',
                    'Side',
                  ],
                  menu: [
                    [
                      _MenuItem('Jain Poha', isMealOfDay: true),
                      _MenuItem('Jain Dal Rice'),
                      _MenuItem('Fruit'),
                    ],
                    [
                      _MenuItem('Idli'),
                      _MenuItem('Jain Rajma', isMealOfDay: true),
                      _MenuItem('Salad'),
                    ],
                    [
                      _MenuItem('Plain Paratha', isMealOfDay: true),
                      _MenuItem('Jain Paneer'),
                      _MenuItem('Yogurt'),
                    ],
                    [
                      _MenuItem('Jain Dhokla'),
                      _MenuItem('Jain Chole', isMealOfDay: true),
                      _MenuItem('Fruit Salad'),
                    ],
                    [
                      _MenuItem('Thepla', isMealOfDay: true),
                      _MenuItem('Jain Biryani'),
                      _MenuItem('Fresh Juice'),
                    ],
                  ],
                  highlightColor: AppTheme.deepPurple,
                ),
              ],
            ),
          ),
        ],
      ),
    );

    // If in dialog mode, just return the content widget
    if (isDialogMode) {
      return contentWidget;
    }

    // Otherwise, return the full page
    return Scaffold(
      appBar: GlobalAppBar(
        title: 'Weekly Menu',
        showBackButton: true,
      ),
      backgroundColor: AppTheme.white,
      body: contentWidget,
    );
  }
}

// Enhanced Menu Table (with fixed first column)
class _EnhancedMenuTable extends StatelessWidget {
  final List<String> days;
  final List<String> sectionTitles;
  final List<List<_MenuItem>> menu;
  final Color highlightColor;
  const _EnhancedMenuTable({
    required this.days,
    required this.sectionTitles,
    required this.menu,
    required this.highlightColor,
  });

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final isSmallScreen = screenSize.width < 360;
    final isTablet = screenSize.width >= 768;

    final cellTextStyle = GoogleFonts.poppins(
      fontSize: isSmallScreen
          ? 10
          : isTablet
              ? 14
              : 12,
      color: AppTheme.textDark,
      height: 1.3,
    );
    final headerTextStyle = GoogleFonts.poppins(
      fontWeight: FontWeight.bold,
      color: AppTheme.purple,
      fontSize: isSmallScreen
          ? 11
          : isTablet
              ? 15
              : 13,
    );

    // Calculate responsive dimensions
    final dayColumnWidth = isSmallScreen
        ? 60.0
        : isTablet
            ? 100.0
            : 80.0;
    final sectionWidth = isSmallScreen
        ? 140.0
        : isTablet
            ? 200.0
            : 160.0;
    final rowHeight = isSmallScreen
        ? 80.0
        : isTablet
            ? 120.0
            : 100.0;
    final headerHeight = isSmallScreen
        ? 45.0
        : isTablet
            ? 60.0
            : 53.0;

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // Fixed Day Column
          Container(
            width: dayColumnWidth,
            decoration: BoxDecoration(
              border: Border(
                right: BorderSide(color: Colors.grey.shade200),
              ),
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(12),
                bottomLeft: Radius.circular(12),
              ),
            ),
            child: Column(
              children: [
                // Day header
                Container(
                  height: headerHeight,
                  decoration: BoxDecoration(
                    color: AppTheme.purple.withOpacity(0.05),
                    border: Border(
                      bottom: BorderSide(color: Colors.grey.shade200),
                    ),
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(12),
                    ),
                  ),
                  alignment: Alignment.center,
                  child: Text(
                    'Day',
                    style: headerTextStyle,
                    textAlign: TextAlign.center,
                  ),
                ),
                // Days list
                ...List.generate(days.length, (dayIndex) {
                  return Container(
                    height: rowHeight,
                    decoration: BoxDecoration(
                      border: Border(
                        bottom: BorderSide(
                          color: Colors.grey.shade200,
                          width: dayIndex < days.length - 1 ? 1 : 0,
                        ),
                      ),
                    ),
                    alignment: Alignment.center,
                    child: Text(
                      days[dayIndex],
                      style:
                          cellTextStyle.copyWith(fontWeight: FontWeight.w600),
                      textAlign: TextAlign.center,
                    ),
                  );
                }),
              ],
            ),
          ),

          // Scrollable Menu Types
          Expanded(
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Container(
                width: sectionTitles.length * sectionWidth,
                child: Column(
                  children: [
                    // Menu types header
                    Container(
                      height: headerHeight,
                      decoration: BoxDecoration(
                        color: AppTheme.purple.withOpacity(0.05),
                        border: Border(
                          bottom: BorderSide(color: Colors.grey.shade200),
                        ),
                        borderRadius: BorderRadius.only(
                          topRight: Radius.circular(12),
                        ),
                      ),
                      child: Row(
                        children: sectionTitles
                            .map((title) => Container(
                                  width: sectionWidth,
                                  padding: EdgeInsets.symmetric(
                                    vertical: isSmallScreen ? 12 : 16,
                                    horizontal: isSmallScreen ? 6 : 8,
                                  ),
                                  alignment: Alignment.center,
                                  child: Text(
                                    title,
                                    style: headerTextStyle,
                                    textAlign: TextAlign.center,
                                    maxLines: 2,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ))
                            .toList(),
                      ),
                    ),

                    // Menu items grid
                    ...List.generate(days.length, (dayIndex) {
                      return Container(
                        height: rowHeight,
                        decoration: BoxDecoration(
                          border: Border(
                            bottom: BorderSide(
                              color: Colors.grey.shade200,
                              width: dayIndex < days.length - 1 ? 1 : 0,
                            ),
                          ),
                        ),
                        child: Row(
                          children: List.generate(sectionTitles.length,
                              (sectionIndex) {
                            final item = menu[dayIndex][sectionIndex];
                            return Container(
                              width: sectionWidth,
                              padding: EdgeInsets.symmetric(
                                vertical: isSmallScreen ? 8 : 12,
                                horizontal: isSmallScreen ? 6 : 8,
                              ),
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Text(
                                    item.name,
                                    style: cellTextStyle.copyWith(
                                      fontWeight: item.isMealOfDay
                                          ? FontWeight.bold
                                          : FontWeight.normal,
                                      color: item.isMealOfDay
                                          ? highlightColor
                                          : AppTheme.textDark,
                                    ),
                                    textAlign: TextAlign.center,
                                    maxLines: 2,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                  if (item.isMealOfDay) ...[
                                    SizedBox(height: isSmallScreen ? 2 : 4),
                                    Container(
                                      padding: EdgeInsets.symmetric(
                                        horizontal: isSmallScreen ? 6 : 8,
                                        vertical: isSmallScreen ? 1 : 2,
                                      ),
                                      decoration: BoxDecoration(
                                        color: highlightColor,
                                        borderRadius: BorderRadius.circular(12),
                                      ),
                                      child: Text(
                                        'Meal of the Day',
                                        style: GoogleFonts.poppins(
                                          color: Colors.white,
                                          fontWeight: FontWeight.bold,
                                          fontSize: isSmallScreen
                                              ? 8
                                              : isTablet
                                                  ? 12
                                                  : 10,
                                        ),
                                      ),
                                    ),
                                  ],
                                ],
                              ),
                            );
                          }),
                        ),
                      );
                    }),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class _MenuItem {
  final String name;
  final bool isMealOfDay;
  _MenuItem(this.name, {this.isMealOfDay = false});
}
