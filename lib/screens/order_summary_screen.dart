import 'dart:async';
import 'dart:convert';
import 'dart:developer';
import 'package:get/get.dart';

import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../utils/meal_constants.dart';
import 'package:intl/intl.dart';
import 'package:startwell/models/meal_model.dart';
import 'package:startwell/models/student_model.dart';
import 'package:startwell/services/student_profile_service.dart';
import 'package:startwell/utils/meal_plan_validator.dart';
import 'package:startwell/themes/app_theme.dart';
import 'package:startwell/widgets/common/info_banner.dart';
import 'package:startwell/screens/payment_method_screen.dart';
import 'package:startwell/widgets/common/veg_icon.dart';
import 'package:startwell/widgets/common/gradient_app_bar.dart';
import 'package:startwell/widgets/common/gradient_button.dart';
import 'package:startwell/utils/pre_order_date_calculator.dart';
import 'package:flutter/services.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'package:startwell/utils/meal_names.dart';
import 'package:startwell/services/promo_code_service.dart';
import 'package:startwell/models/promo_code_response.dart';
import 'package:lottie/lottie.dart';

// Using GetX's built-in capitalize method instead of custom extension

class OrderSummaryScreen extends StatefulWidget {
  final String planType;
  final bool isCustomPlan;
  final List<bool> selectedWeekdays;
  final DateTime startDate;
  final DateTime endDate;
  final List<DateTime> mealDates;
  final double totalAmount;
  final List<Meal> selectedMeals;
  final bool isExpressOrder;
  final Student? selectedStudent;
  final String? mealType;
  final DateTime? breakfastPreOrderDate;
  final DateTime? lunchPreOrderDate;
  final bool isPreOrder;
  final String? selectedPlanType;
  final String? deliveryMode;

  // Add specific delivery modes for breakfast and lunch
  final String? breakfastDeliveryMode;
  final String? lunchDeliveryMode;

  // Breakfast specific data
  final DateTime? breakfastStartDate;
  final DateTime? breakfastEndDate;
  final List<DateTime>? breakfastMealDates;
  final List<Meal>? breakfastSelectedMeals;
  final double? breakfastAmount;
  final String? breakfastPlanType;
  final List<bool>? breakfastSelectedWeekdays;

  // Lunch specific data
  final DateTime? lunchStartDate;
  final DateTime? lunchEndDate;
  final List<DateTime>? lunchMealDates;
  final List<Meal>? lunchSelectedMeals;
  final double? lunchAmount;
  final String? lunchPlanType;
  final List<bool>? lunchSelectedWeekdays;

  final String? promoCode;
  final double? promoDiscount;

  // Add pre-order start and end dates
  final DateTime? preOrderStartDate;
  final DateTime? preOrderEndDate;

  const OrderSummaryScreen({
    Key? key,
    required this.planType,
    required this.isCustomPlan,
    required this.selectedWeekdays,
    required this.startDate,
    required this.endDate,
    required this.mealDates,
    required this.totalAmount,
    required this.selectedMeals,
    required this.isExpressOrder,
    this.selectedStudent,
    this.mealType,
    this.breakfastPreOrderDate,
    this.lunchPreOrderDate,
    this.isPreOrder = false,
    this.selectedPlanType,
    this.deliveryMode,
    // Add specific delivery modes for breakfast and lunch
    this.breakfastDeliveryMode,
    this.lunchDeliveryMode,
    // New parameters for specific breakfast and lunch data
    this.breakfastStartDate,
    this.breakfastEndDate,
    this.breakfastMealDates,
    this.breakfastSelectedMeals,
    this.breakfastAmount,
    this.breakfastPlanType,
    this.breakfastSelectedWeekdays,
    this.lunchStartDate,
    this.lunchEndDate,
    this.lunchMealDates,
    this.lunchSelectedMeals,
    this.lunchAmount,
    this.lunchPlanType,
    this.lunchSelectedWeekdays,
    this.promoCode,
    this.promoDiscount,
    // Add pre-order start and end dates
    this.preOrderStartDate,
    this.preOrderEndDate,
  }) : super(key: key);

  @override
  State<OrderSummaryScreen> createState() => _OrderSummaryScreenState();
}

class _OrderSummaryScreenState extends State<OrderSummaryScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _slideAnimation;

  // Flag to track whether both meal types are selected
  bool _hasBothMealTypes = false;

  // Promo code related state
  final TextEditingController _promoController = TextEditingController();
  String? _appliedPromoCode;
  bool _isValidatingPromo = false;
  bool _isPromoValid = false;
  String _promoErrorMessage = '';
  double _promoDiscount = 0.0;
  final double _gstPercentage = 0.05; // 5% GST
  final double _deliveryCharges = 0.0; // Free delivery for now

  // Define valid promo codes
  final Map<String, double> _validPromoCodes = {
    "WELCOME10": 0.10, // 10% discount
    "SUMMER20": 0.20, // 20% discount
    "STARTWELL25": 0.25, // 25% discount
  };

  final PromoCodeService _promoCodeService = PromoCodeService();
  PromoCodeResponse? _promoCodeResponse;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: const Interval(0.0, 0.7, curve: Curves.easeOut),
      ),
    );

    _slideAnimation = Tween<double>(begin: 50.0, end: 0.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: const Interval(0.0, 0.8, curve: Curves.easeOutCubic),
      ),
    );

    // Initialize promo code state if passed from a previous screen
    if (widget.promoCode != null && widget.promoDiscount != null) {
      _isPromoValid = true;
      _appliedPromoCode = widget.promoCode;
      _promoDiscount = widget.promoDiscount!;
    }

    // Determine if both meal types are selected
    _hasBothMealTypes = (widget.mealType == 'both') ||
        (widget.breakfastPreOrderDate != null &&
            widget.lunchPreOrderDate != null);

    _animationController.forward();
  }

  @override
  void dispose() {
    _promoController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  // Get a formatted string of selected weekdays
  String _getSelectedWeekdaysText() {
    final List<String> weekdayNames = [
      'Monday',
      'Tuesday',
      'Wednesday',
      'Thursday',
      'Friday'
    ];

    List<String> selectedDays = [];
    for (int i = 0; i < widget.selectedWeekdays.length; i++) {
      if (widget.selectedWeekdays[i]) {
        selectedDays.add(weekdayNames[i]);
      }
    }

    if (selectedDays.isEmpty) {
      return "None";
    } else if (selectedDays.length == 5) {
      return "All Weekdays";
    } else {
      return selectedDays.join(", ");
    }
  }

  bool _isNavigating = false; // Add navigation guard

  // Navigate to Payment Methods screen
  Future<void> _navigateToPaymentMethods(BuildContext context, String planType) async {
    if (_isNavigating) return; // Prevent multiple navigations
    _isNavigating = true;
    
    // Auto-reset navigation flag after 10 seconds as safety measure
    Timer(Duration(seconds: 10), () {
      if (_isNavigating) {
        log('[AUTO-RESET] Navigation flag reset after timeout in order_summary_screen');
        _isNavigating = false;
      }
    });
    
    print("Navigating to payment screen for $planType...");
    log("endDate: ${widget.endDate}");
    log("startDate: ${widget.startDate}");

    // Create a default student if none is selected
    final student = widget.selectedStudent ??
        Student(
          id: 'default_${DateTime.now().millisecondsSinceEpoch}',
          name: 'Guest Student',
          schoolName: 'Not Specified',
          className: 'Not Specified',
          division: 'Not Specified',
          floor: 'Not Specified',
          allergies: '',
          grade: 'Not Specified',
          section: 'Not Specified',
          profileImageUrl: '',
        );

    // Log student information for debugging
    log("[DEBUG] Selected student details:");
    log("  - ID: ${student.id}");
    log("  - Name: ${student.name}");
    log("  - School: ${student.schoolName}");
    log("  - Class: ${student.className}");
    log("  - Is Pre-order: ${widget.isPreOrder}");
    if (widget.isPreOrder) {
      log("  - Pre-order Start Date: ${widget.preOrderStartDate}");
      log("  - Pre-order End Date: ${widget.preOrderEndDate}");
    }

    // Store order summary data in SharedPreferences for each plan
    final prefs = await SharedPreferences.getInstance();
    if (_hasBothMealTypes) {
      // Store breakfast
      if (widget.breakfastStartDate != null &&
          widget.breakfastEndDate != null) {
        final breakfastData = {
          'studentId': student.id,
          'startDate': widget.breakfastStartDate!.toIso8601String(),
          'endDate': widget.breakfastEndDate!.toIso8601String(),
          'deliveryMode': widget.breakfastDeliveryMode ?? 'Mon to Fri',
          'isPreOrder': widget.isPreOrder,
          'preOrderStartDate': widget.preOrderStartDate?.toIso8601String(),
          'preOrderEndDate': widget.preOrderEndDate?.toIso8601String(),
        };
        await prefs.setString(
          'order_summary_${student.id}_breakfast-${student.id}',
          jsonEncode(breakfastData),
        );
        log("[DEBUG] Stored breakfast data: ${jsonEncode(breakfastData)}");

        // Store student profile for upcoming meals tab
        await prefs.setString(
          'student_profile_${student.id}',
          jsonEncode(student.toJson()),
        );
        log("[DEBUG] Stored student profile for ID: ${student.id}");

        // Store student ID in recently active students list
        List<String> recentStudentIds = [];
        final recentStudentsKey = 'recently_active_students';
        if (prefs.containsKey(recentStudentsKey)) {
          final recentStudentsJson = prefs.getString(recentStudentsKey) ?? '[]';
          recentStudentIds = List<String>.from(jsonDecode(recentStudentsJson));
        }
        if (!recentStudentIds.contains(student.id)) {
          recentStudentIds.insert(0, student.id);
          await prefs.setString(
              recentStudentsKey, jsonEncode(recentStudentIds));
          log("[DEBUG] Added student to recent list: ${student.id}");
        }
      }
      // Store lunch
      if (widget.lunchStartDate != null && widget.lunchEndDate != null) {
        final lunchData = {
          'studentId': student.id,
          'startDate': widget.lunchStartDate!.toIso8601String(),
          'endDate': widget.lunchEndDate!.toIso8601String(),
          'deliveryMode': widget.lunchDeliveryMode ?? 'Mon to Fri',
          'isPreOrder': widget.isPreOrder,
          'preOrderStartDate': widget.preOrderStartDate?.toIso8601String(),
          'preOrderEndDate': widget.preOrderEndDate?.toIso8601String(),
        };
        await prefs.setString(
          'order_summary_${student.id}_lunch-${student.id}',
          jsonEncode(lunchData),
        );
        log("[DEBUG] Stored lunch data: ${jsonEncode(lunchData)}");

        // Store student profile for upcoming meals tab if not already stored
        if (!prefs.containsKey('student_profile_${student.id}')) {
          await prefs.setString(
            'student_profile_${student.id}',
            jsonEncode(student.toJson()),
          );
          log("[DEBUG] Stored student profile for ID: ${student.id}");
        }
      }
    } else {
      // Single plan
      final planData = {
        'studentId': student.id,
        'startDate': widget.startDate.toIso8601String(),
        'endDate': widget.endDate.toIso8601String(),
        'deliveryMode': widget.deliveryMode ?? 'Mon to Fri',
        'isPreOrder': widget.isPreOrder,
        'preOrderStartDate': widget.preOrderStartDate?.toIso8601String(),
        'preOrderEndDate': widget.preOrderEndDate?.toIso8601String(),
      };
      await prefs.setString(
        'order_summary_${student.id}_${planType}-${student.id}',
        jsonEncode(planData),
      );
      log("[DEBUG] Stored single plan data: ${jsonEncode(planData)}");

      // Store student profile for upcoming meals tab
      await prefs.setString(
        'student_profile_${student.id}',
        jsonEncode(student.toJson()),
      );
      log("[DEBUG] Stored student profile for ID: ${student.id}");

      // Store student ID in recently active students list
      List<String> recentStudentIds = [];
      final recentStudentsKey = 'recently_active_students';
      if (prefs.containsKey(recentStudentsKey)) {
        final recentStudentsJson = prefs.getString(recentStudentsKey) ?? '[]';
        recentStudentIds = List<String>.from(jsonDecode(recentStudentsJson));
      }
      if (!recentStudentIds.contains(student.id)) {
        recentStudentIds.insert(0, student.id);
        await prefs.setString(recentStudentsKey, jsonEncode(recentStudentIds));
        log("[DEBUG] Added student to recent list: ${student.id}");
      }
    }

    // Store the last selected student ID
    await prefs.setString('last_selected_student_id', student.id);
    log("[DEBUG] Stored last selected student ID: ${student.id}");

    // Calculate final amount after promo discount, GST, and delivery charges
    double finalAmount = widget.totalAmount;
    if (_promoDiscount > 0) {
      finalAmount -= _promoDiscount;
    }

    finalAmount += finalAmount * _gstPercentage; // Add GST
    finalAmount += _deliveryCharges; // Add delivery charges

    try {
      // Ensure widget is still mounted before navigation
      if (!mounted) {
        log("[DEBUG] Widget not mounted, aborting navigation");
        return;
      }

      log("[DEBUG] Navigating to payment screen with student ID: ${student.id}");
      
      await Get.to(() => PaymentMethodScreen(
        planType: planType,
        isCustomPlan: widget.isCustomPlan,
        selectedWeekdays: widget.selectedWeekdays,
        startDate: widget.startDate,
        endDate: widget.endDate,
        mealDates: widget.mealDates,
        totalAmount: finalAmount,
        selectedMeals: widget.selectedMeals,
        isExpressOrder: widget.isExpressOrder,
        selectedStudent: student,
        mealType: widget.mealType,
        breakfastPreOrderDate: widget.breakfastPreOrderDate,
        lunchPreOrderDate: widget.lunchPreOrderDate,
        isPreOrder: widget.isPreOrder,
        selectedPlanType: widget.selectedPlanType,
        deliveryMode: widget.deliveryMode,
        promoCode: _appliedPromoCode,
        promoDiscount: _promoDiscount,
        breakfastDeliveryMode: widget.breakfastDeliveryMode,
        lunchDeliveryMode: widget.lunchDeliveryMode,
        preOrderStartDate: widget.preOrderStartDate,
        preOrderEndDate: widget.preOrderEndDate,
        breakfastSelectedWeekdays: widget.breakfastSelectedWeekdays,
        lunchSelectedWeekdays: widget.lunchSelectedWeekdays,
      ));
      
      log("[DEBUG] Successfully returned from payment screen");
    } catch (e) {
      log("[ERROR] Navigation to payment screen failed: $e");
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to open payment screen. Please try again.'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      // Always reset navigation flag
      _isNavigating = false;
      log("[DEBUG] Navigation flag reset");
    }
  }

  @override
  Widget build(BuildContext context) {
    final hasDiscount = widget.planType == 'Quarterly' ||
        widget.planType == 'Half-Yearly' ||
        widget.planType == 'Annual';

    return Scaffold(
      appBar: GradientAppBar(
        titleText: 'Order Summary',
      ),
      backgroundColor: AppTheme.offWhite,
      body: Column(
        children: [
          // Gradient top decoration
          Container(
            height: 4,
            decoration: const BoxDecoration(
              gradient: AppTheme.purpleToDeepPurple,
            ),
          ),
          // Main scrollable content
          Expanded(
            child: AnimatedBuilder(
              animation: _animationController,
              builder: (context, child) {
                return Opacity(
                  opacity: _fadeAnimation.value,
                  child: Transform.translate(
                    offset: Offset(0, _slideAnimation.value),
                    child: child,
                  ),
                );
              },
              child: SingleChildScrollView(
                physics: const BouncingScrollPhysics(),
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Order status banner removed

                      const SizedBox(height: 16),

                      // Header Text
                      // Container(
                      //   margin: const EdgeInsets.only(bottom: 4, left: 4),
                      //   child: Row(
                      //     children: [
                      //       Container(
                      //         height: 20,
                      //         width: 4,
                      //         decoration: BoxDecoration(
                      //           gradient: AppTheme.purpleToDeepPurple,
                      //           borderRadius: BorderRadius.circular(2),
                      //         ),
                      //       ),
                      //       const SizedBox(width: 8),
                      //       Text(
                      //         "Order Details",
                      //         style: GoogleFonts.poppins(
                      //           fontSize: 20,
                      //           fontWeight: FontWeight.w600,
                      //           color: AppTheme.textDark,
                      //         ),
                      //       ),
                      //     ],
                      //   ),
                      // ),

                      const SizedBox(height: 0),

                      // Student details card - HIDDEN
                      if (false && widget.selectedStudent != null) ...[
                        _buildStudentDetailsCard(),
                        const SizedBox(height: 16),
                      ],

                      // Subscription plan or Express delivery details
                      _buildPlanSection(),

                      //const SizedBox(height: 0),

                      // Meal Plan Section (if it still exists) - HIDDEN
                      if (false)
                        _buildCardSection(
                          title: 'Meal Plan',
                          icon: Icons.restaurant_menu_rounded,
                          children: [
                            _buildMealPlanSection(),
                          ],
                        ),

                      // Single Day Plan Info Banner removed
                      if (widget.planType == 'Single Day')
                        const SizedBox(height: 16),

                      // Student information section - HIDDEN
                      if (false) _buildStudentInfoSection(),

                      // Add a small spacing after hiding the Student Information section
                      const SizedBox(height: 0),

                      // Payment summary section
                      if (false) // Hide Payment Details section as it's moved to Payment Method screen
                        _buildCardSection(
                          title: 'Payment Details',
                          icon: Icons.receipt_long_rounded,
                          children: [
                            Container(
                              padding: const EdgeInsets.all(16),
                              decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.circular(16),
                                border: Border.all(
                                  color: AppTheme.purple.withOpacity(0.15),
                                  width: 1,
                                ),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.grey.withOpacity(0.07),
                                    blurRadius: 6,
                                    offset: const Offset(0, 2),
                                  ),
                                ],
                              ),
                              child: Column(
                                children: [
                                  _buildStudentInfoRow(
                                    icon: Icons.flatware_rounded,
                                    label: 'Meal Price',
                                    value:
                                        '₹${(widget.totalAmount / widget.mealDates.length).toStringAsFixed(0)} per meal',
                                  ),
                                  _buildStudentInfoRow(
                                    icon: Icons.list_alt_rounded,
                                    label: 'Number of Meals',
                                    value: '${widget.mealDates.length}',
                                  ),

                                  // Divider
                                  Padding(
                                    padding: const EdgeInsets.symmetric(
                                        vertical: 12),
                                    child: Divider(
                                      color: Colors.grey.shade200,
                                      height: 1,
                                    ),
                                  ),

                                  if (hasDiscount)
                                    _buildStudentInfoRow(
                                      icon: Icons.shopping_cart_outlined,
                                      label: 'Subtotal',
                                      value:
                                          '₹${(widget.totalAmount * 1.25).toStringAsFixed(0)}',
                                      valueStyle: GoogleFonts.poppins(
                                        fontSize: 14,
                                        decoration: TextDecoration.lineThrough,
                                        color: AppTheme.textMedium,
                                      ),
                                    ),
                                  if (hasDiscount)
                                    _buildStudentInfoRow(
                                      icon: Icons.discount_rounded,
                                      label:
                                          'Discount (${(0.25 * 100).toInt()}%)',
                                      value:
                                          '-₹${((widget.totalAmount * 1.25) - widget.totalAmount).toStringAsFixed(0)}',
                                      valueStyle: GoogleFonts.poppins(
                                        fontSize: 14,
                                        fontWeight: FontWeight.w500,
                                        color: AppTheme.success,
                                      ),
                                      isAlert: false,
                                      iconColor: AppTheme.success,
                                      backgroundColor:
                                          AppTheme.success.withOpacity(0.1),
                                    ),
                                  _buildStudentInfoRow(
                                    icon: Icons.currency_rupee,
                                    label: 'Total Amount',
                                    value:
                                        '₹${widget.totalAmount.toStringAsFixed(0)}',
                                    valueStyle: GoogleFonts.poppins(
                                      fontSize: 18,
                                      fontWeight: FontWeight.bold,
                                      foreground: Paint()
                                        ..shader = LinearGradient(
                                          colors: [
                                            AppTheme.purple,
                                            AppTheme.deepPurple,
                                          ],
                                          begin: Alignment.topLeft,
                                          end: Alignment.bottomRight,
                                        ).createShader(const Rect.fromLTWH(
                                            0.0, 0.0, 200.0, 70.0)),
                                    ),
                                    isLast: true,
                                    backgroundColor:
                                        AppTheme.purple.withOpacity(0.05),
                                    iconColor: AppTheme.purple,
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),

                      const SizedBox(height: 0),

                      // Promo Code Section - Added below order details
                      _buildPromoCodeSection(),

                      const SizedBox(height: 16),

                      // Payment button - REMOVED
                      // Hero(
                      //   tag: 'paymentButton',
                      //   child: Container(
                      //     margin: const EdgeInsets.symmetric(vertical: 24),
                      //     width: double.infinity,
                      //     height: 60,
                      //     decoration: BoxDecoration(
                      //       borderRadius: BorderRadius.circular(50),
                      //       gradient: AppTheme.purpleToDeepPurple,
                      //       boxShadow: [
                      //         BoxShadow(
                      //           color: AppTheme.deepPurple.withOpacity(0.25),
                      //           blurRadius: 15,
                      //           offset: const Offset(0, 6),
                      //           spreadRadius: 0,
                      //         ),
                      //       ],
                      //     ),
                      //     child: Material(
                      //       color: Colors.transparent,
                      //       child: InkWell(
                      //         borderRadius: BorderRadius.circular(18),
                      //         onTap: () {
                      //           // Button logic removed
                      //         },
                      //         child: Center(
                      //           child: Text(
                      //             'Continue to Payment',
                      //             style: GoogleFonts.poppins(
                      //               fontSize: 16,
                      //               fontWeight: FontWeight.w600,
                      //               color: Colors.white,
                      //             ),
                      //           ),
                      //         ),
                      //       ),
                      //     ),
                      //   ),
                      // ),

                      // Order Information Section - HIDDEN
                      if (false)
                        _buildSectionWithTitle(
                          context: context,
                          title: 'Order Information',
                          icon: Icons.restaurant_menu,
                          withoutPadding: false,
                          children: [
                            _buildOrderInformationRow(
                              'Plan Type',
                              widget.planType +
                                  (widget.isCustomPlan ? ' (Custom)' : ''),
                            ),
                            _buildOrderInformationRow(
                              'Meal Type',
                              widget.mealType?.capitalize ?? 'Not Specified',
                            ),
                            // Display delivery mode for breakfast and lunch if both are selected
                            if (_hasBothMealTypes) ...[
                              _buildOrderInformationRow(
                                'Breakfast Delivery Days',
                                widget.breakfastDeliveryMode ?? 'Not Specified',
                              ),
                              _buildOrderInformationRow(
                                'Lunch Delivery Days',
                                widget.lunchDeliveryMode ?? 'Not Specified',
                              ),
                            ] else ...[
                              // Display general delivery mode for single plans
                              _buildOrderInformationRow(
                                'Delivery Days',
                                widget.deliveryMode ?? 'Not Specified',
                              ),
                            ],
                            _buildOrderInformationRow(
                              'Selected Days',
                              widget.isCustomPlan
                                  ? _getSelectedWeekdaysText()
                                  : 'Monday to Friday',
                            ),
                            _buildOrderInformationRow(
                              'Start Date',
                              DateFormat('dd MMM yyyy')
                                  .format(widget.startDate),
                            ),
                            _buildOrderInformationRow(
                              'End Date',
                              DateFormat('dd MMM yyyy').format(widget.endDate),
                            ),
                            _buildOrderInformationRow(
                              'Total Meals',
                              widget.mealDates.length.toString(),
                            ),

                            // Add Pre-order Information
                            if (widget.isPreOrder) ...[
                              const SizedBox(height: 8),
                              const Divider(),
                              const SizedBox(height: 8),
                              Padding(
                                padding: const EdgeInsets.only(bottom: 8.0),
                                child: Text(
                                  'Pre-order Information',
                                  style: GoogleFonts.poppins(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w600,
                                    color: AppTheme.textDark,
                                  ),
                                ),
                              ),
                              if (widget.breakfastPreOrderDate != null) ...[
                                _buildOrderInformationRow(
                                  'Breakfast Pre-order Start',
                                  DateFormat('dd MMM yyyy')
                                      .format(widget.breakfastPreOrderDate!),
                                ),
                                if (widget.breakfastEndDate != null)
                                  _buildOrderInformationRow(
                                    'Breakfast Pre-order End',
                                    DateFormat('dd MMM yyyy')
                                        .format(widget.breakfastEndDate!),
                                  ),
                              ],
                              if (widget.lunchPreOrderDate != null) ...[
                                _buildOrderInformationRow(
                                  'Lunch Pre-order Start',
                                  DateFormat('dd MMM yyyy')
                                      .format(widget.lunchPreOrderDate!),
                                ),
                                if (widget.lunchEndDate != null)
                                  _buildOrderInformationRow(
                                    'Lunch Pre-order End',
                                    DateFormat('dd MMM yyyy')
                                        .format(widget.lunchEndDate!),
                                  ),
                              ],
                            ],
                          ],
                        ),
                    ],
                  ),
                ),
              ),
            ),
          ),
          // Fixed bottom button
          Container(
            padding: const EdgeInsets.all(16.0),
            decoration: BoxDecoration(
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 10,
                  offset: const Offset(0, -5),
                ),
              ],
            ),
            child: Hero(
              tag: 'paymentButton',
              child: Container(
                width: double.infinity,
                height: 60,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(50),
                  gradient: _isNavigating 
                    ? LinearGradient(colors: [Colors.grey.shade400, Colors.grey.shade500])
                    : AppTheme.purpleToDeepPurple,
                  boxShadow: _isNavigating 
                    ? []
                    : [
                        BoxShadow(
                          color: AppTheme.deepPurple.withOpacity(0.25),
                          blurRadius: 15,
                          offset: const Offset(0, 6),
                          spreadRadius: 0,
                        ),
                      ],
                ),
                child: Material(
                  color: Colors.transparent,
                  child: InkWell(
                    borderRadius: BorderRadius.circular(50),
                    onTap: _isNavigating ? null : () {
                      // Determine the meal plan type from the mealType parameter or from the selected meals
                      final String planType;
                      if (widget.mealType != null) {
                        planType = widget.mealType!;
                      } else if (widget.selectedMeals.isNotEmpty) {
                        if (widget.selectedMeals.first.categories.first ==
                            MealCategory.breakfast) {
                          planType = 'breakfast';
                        } else if (widget
                                .selectedMeals.first.categories.first ==
                            MealCategory.expressOneDay) {
                          planType = 'express';
                        } else {
                          planType = 'lunch';
                        }
                      } else {
                        planType =
                            'lunch'; // Default to lunch if no info available
                      }

                      // If there's no student selected, just navigate to payment (skip validation)
                      if (widget.selectedStudent == null) {
                        if (!_isNavigating) {
                          // Add small delay to ensure UI is ready
                          Future.delayed(Duration(milliseconds: 100), () {
                            if (mounted && !_isNavigating) {
                              _navigateToPaymentMethods(context, planType);
                            }
                          });
                        }
                        return;
                      }

                      // Validate the meal plan before proceeding
                      final String? validationError =
                          MealPlanValidator.validateMealPlan(
                              widget.selectedStudent!, planType);

                      if (validationError != null) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text(
                              validationError,
                              style: GoogleFonts.poppins(),
                            ),
                            backgroundColor: Colors.red,
                          ),
                        );
                        return;
                      }

                      // Proceed to payment method selection
                      if (!_isNavigating) {
                        // Add small delay to ensure UI is ready
                        Future.delayed(Duration(milliseconds: 100), () {
                          if (mounted && !_isNavigating) {
                            _navigateToPaymentMethods(context, planType);
                          }
                        });
                      }
                    },
                    child: Center(
                      child: _isNavigating 
                        ? Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              SizedBox(
                                width: 20,
                                height: 20,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                                ),
                              ),
                              SizedBox(width: 12),
                              Text(
                                'Opening Payment...',
                                style: GoogleFonts.poppins(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w600,
                                  color: Colors.white,
                                ),
                              ),
                            ],
                          )
                        : Text(
                            'Continue to Payment',
                            style: GoogleFonts.poppins(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              color: Colors.white,
                            ),
                          ),
                    ),
                  ),
                ),
              ),
            ),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text('Total Payable:', style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16)),
                Text('₹${((widget.totalAmount - (_promoDiscount > 0 ? _promoDiscount : 0)) * (1 + _gstPercentage) + _deliveryCharges).toStringAsFixed(2)}', style: TextStyle(fontWeight: FontWeight.bold, fontSize: 18, color: AppTheme.purple)),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Method to build student details card
  Widget _buildStudentDetailsCard() {
    // Get the student
    final student = widget.selectedStudent!;

    return Card(
      elevation: 1,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Section title with icon
            Row(
              children: [
                const Icon(
                  Icons.person,
                  size: 20,
                  color: AppTheme.purple,
                ),
                const SizedBox(width: 8),
                Text(
                  "Student Information",
                  style: GoogleFonts.poppins(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: AppTheme.textDark,
                  ),
                ),
              ],
            ),
            const Divider(height: 24),

            // Student Name
            _buildDetailRow(
              "Name",
              student.name,
              Icons.person_outline,
            ),

            // School
            _buildDetailRow(
              "School",
              student.schoolName,
              Icons.school_outlined,
            ),

            // Class and Division
            _buildDetailRow(
              "Class",
              "${student.className} - ${student.division}",
              Icons.class_outlined,
            ),

            // Floor
            _buildDetailRow(
              "Floor",
              student.floor,
              Icons.apartment_outlined,
            ),

            // Allergies (if any)
            if (student.allergies.isNotEmpty)
              _buildDetailRow(
                "Allergies",
                student.allergies,
                Icons.healing_outlined,
                color: Colors.orange,
              ),
          ],
        ),
      ),
    );
  }

  // Build the plan section - first try combined breakast/lunch display if both are present
  Widget _buildPlanSection() {
    // If we have both breakfast and lunch data, create a combined view
    if (widget.breakfastStartDate != null && widget.lunchStartDate != null) {
      return Card(
        margin: const EdgeInsets.only(bottom: 16),
        elevation: 1,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Section title with icon
              Row(
                children: [
                  const Icon(
                    Icons.calendar_today,
                    size: 20,
                    color: AppTheme.purple,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      "Subscription Plan",
                      style: GoogleFonts.poppins(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: AppTheme.textDark,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
              // Student Badge below title
              if (widget.selectedStudent != null) ...[
                const SizedBox(height: 8),
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                  decoration: BoxDecoration(
                    gradient: AppTheme.purpleToDeepPurple,
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        color: AppTheme.deepPurple.withOpacity(0.2),
                        blurRadius: 4,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Icon(
                        Icons.person_outline,
                        size: 14,
                        color: Colors.white,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        widget.selectedStudent!.name,
                        style: GoogleFonts.poppins(
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                          color: Colors.white,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
              ],
              Divider(height: 24, color: Colors.grey.withOpacity(0.2)),

              // Breakfast Plan
              _buildMealPlanDetailsSection(
                title: "Breakfast Plan",
                isPreOrder: widget.isPreOrder,
                preOrderDate: widget.breakfastPreOrderDate,
                icon: MealConstants.breakfastIcon,
                iconColor: MealConstants.breakfastIconColor,
                specificStartDate:
                    widget.isPreOrder && widget.breakfastPreOrderDate != null
                        ? widget.breakfastPreOrderDate
                        : widget.breakfastStartDate,
                specificEndDate:
                    widget.isPreOrder && widget.breakfastEndDate != null
                        ? widget.breakfastEndDate
                        : widget.breakfastEndDate,
                specificMealDates: widget.breakfastMealDates,
                specificAmount: widget.breakfastAmount,
                specificPlanType: widget.breakfastPlanType,
                specificDeliveryMode: widget.breakfastDeliveryMode,
                specificSelectedWeekdays: widget.breakfastSelectedWeekdays,
                hideMealTitle: true,
              ),

              const SizedBox(height: 24),
              Divider(height: 1, color: Colors.grey.withOpacity(0.2)),
              const SizedBox(height: 24),

              // Lunch Plan
              _buildMealPlanDetailsSection(
                title: "Lunch Plan",
                isPreOrder: widget.isPreOrder,
                preOrderDate: widget.lunchPreOrderDate,
                icon: MealConstants.lunchIcon,
                iconColor: MealConstants.lunchIconColor,
                specificStartDate:
                    widget.isPreOrder && widget.lunchPreOrderDate != null
                        ? widget.lunchPreOrderDate
                        : widget.lunchStartDate,
                specificEndDate:
                    widget.isPreOrder && widget.lunchEndDate != null
                        ? widget.lunchEndDate
                        : widget.lunchEndDate,
                specificMealDates: widget.lunchMealDates,
                specificAmount: widget.lunchAmount,
                specificPlanType: widget.lunchPlanType,
                specificDeliveryMode: widget.lunchDeliveryMode,
                specificSelectedWeekdays: widget.lunchSelectedWeekdays,
                hideMealTitle: true,
              ),

              // Total combined price
              Container(
                margin: const EdgeInsets.only(top: 24),
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: AppTheme.purple.withOpacity(0.05),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: AppTheme.purple.withOpacity(0.3),
                    width: 1,
                  ),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      "Total Price",
                      style: GoogleFonts.poppins(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: AppTheme.textDark,
                      ),
                    ),
                    Text(
                      "₹${widget.totalAmount.toStringAsFixed(0)}",
                      style: GoogleFonts.poppins(
                        fontSize: 18,
                        fontWeight: FontWeight.w700,
                        foreground: Paint()
                          ..shader = LinearGradient(
                            colors: [
                              AppTheme.purple,
                              AppTheme.deepPurple,
                            ],
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                          ).createShader(
                              const Rect.fromLTWH(0.0, 0.0, 200.0, 70.0)),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      );
    } else {
      // Original single plan display
      return Card(
        margin: const EdgeInsets.only(bottom: 16),
        elevation: 1,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Section title with icon
              Row(
                children: [
                  const Icon(
                    Icons.calendar_today,
                    size: 20,
                    color: AppTheme.purple,
                  ),
                  const SizedBox(width: 8, height: 26),
                  Text(
                    widget.isExpressOrder
                        ? "Express Delivery"
                        : "Subscription Plan",
                    style: GoogleFonts.poppins(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                      color: AppTheme.textDark,
                    ),
                  ),
                ],
              ),
              // Student Badge below title
              if (widget.selectedStudent != null) ...[
                const SizedBox(height: 8),
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        AppTheme.purple.withOpacity(0.15),
                        AppTheme.purple.withOpacity(0.15),
                      ],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: AppTheme.purple.withOpacity(0.3),
                      width: 1,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: AppTheme.deepPurple.withOpacity(0.1),
                        blurRadius: 4,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Icon(
                        Icons.person_outline,
                        size: 14,
                        color: AppTheme.purple,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        widget.selectedStudent!.name,
                        style: GoogleFonts.poppins(
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                          color: AppTheme.purple,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
              Divider(height: 32, color: Colors.grey.withOpacity(0.2)),

              // Get meal name
              _buildDetailRow(
                "Selected Meal",
                widget.selectedMeals.isNotEmpty
                    ? widget.selectedMeals.first.name
                    : widget.mealType == 'breakfast'
                        ? "Breakfast of the Day"
                        : "Lunch of the Day",
                Icons.restaurant_menu_outlined,
              ),

              // Delivery Mode - Show for all plans, not just custom plans
              _buildDetailRow(
                "Delivery Days",
                _getDeliveryModeForSinglePlan(),
                Icons.calendar_view_week_outlined,
              ),

              // Start and End Dates side by side
              if (!widget.isPreOrder)
                Row(
                  children: [
                    Expanded(
                      child: _buildDetailRow(
                        "Start Date",
                        DateFormat('d MMMM yyyy').format(
                          widget.startDate,
                        ),
                        Icons.event_available_outlined,
                      ),
                    ),
                    Expanded(
                      child: _buildDetailRow(
                        "End Date",
                        DateFormat('d MMMM yyyy').format(
                          widget.endDate,
                        ),
                        Icons.event_available_outlined,
                      ),
                    ),
                  ],
                ),

              // Pre-order dates
              if (widget.isPreOrder) ...[
                if (widget.breakfastPreOrderDate != null) ...[
                  _buildDetailRow(
                    "Pre-order From",
                    DateFormat('d MMMM yyyy')
                        .format(widget.breakfastPreOrderDate!),
                    Icons.event_available_outlined,
                    color: Colors.amber,
                  ),
                  if (widget.breakfastEndDate != null)
                    _buildDetailRow(
                      "Pre-order To",
                      DateFormat('d MMMM yyyy')
                          .format(widget.breakfastEndDate!),
                      Icons.event_available_outlined,
                      color: Colors.amber,
                    ),
                ] else if (widget.lunchPreOrderDate != null) ...[
                  _buildDetailRow(
                    "Pre-order From",
                    DateFormat('d MMMM yyyy').format(widget.lunchPreOrderDate!),
                    Icons.event_available_outlined,
                    color: AppTheme.purple,
                  ),
                  if (widget.lunchEndDate != null)
                    _buildDetailRow(
                      "Pre-order To",
                      DateFormat('d MMMM yyyy').format(widget.lunchEndDate!),
                      Icons.event_available_outlined,
                      color: AppTheme.purple,
                    ),
                ],
              ],

              // Total price
              Container(
                margin: const EdgeInsets.only(top: 16),
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: AppTheme.purple.withOpacity(0.05),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: AppTheme.purple.withOpacity(0.3),
                    width: 1,
                  ),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      "Total Price",
                      style: GoogleFonts.poppins(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: AppTheme.textDark,
                      ),
                    ),
                    Text(
                      "₹${widget.totalAmount.toStringAsFixed(0)}",
                      style: GoogleFonts.poppins(
                        fontSize: 18,
                        fontWeight: FontWeight.w700,
                        foreground: Paint()
                          ..shader = LinearGradient(
                            colors: [
                              AppTheme.purple,
                              AppTheme.deepPurple,
                            ],
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                          ).createShader(
                              const Rect.fromLTWH(0.0, 0.0, 200.0, 70.0)),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      );
    }
  }

  // Helper method to build individual meal plan sections
  Widget _buildMealPlanDetailsSection({
    required String title,
    required bool isPreOrder,
    required DateTime? preOrderDate,
    required IconData icon,
    required Color iconColor,
    DateTime? specificStartDate,
    DateTime? specificEndDate,
    List<DateTime>? specificMealDates,
    double? specificAmount,
    String? specificPlanType,
    String? specificDeliveryMode,
    List<bool>? specificSelectedWeekdays,
    bool hideMealTitle = false,
  }) {
    // Use pre-order dates if available in pre-order scenarios, otherwise use specific dates or generic ones
    final startDate = isPreOrder && preOrderDate != null
        ? preOrderDate
        : (specificStartDate ?? widget.startDate);
    final endDate = isPreOrder && specificEndDate != null
        ? specificEndDate
        : (widget.preOrderEndDate ?? widget.endDate);
    final mealDates = specificMealDates?.length ?? widget.mealDates.length;
    final totalAmount = specificAmount ?? widget.totalAmount;
    final planType =
        specificPlanType ?? widget.selectedPlanType ?? widget.planType;

    // Get meal name and determine which meal selection to use
    String mealName;
    final mealType = title == "Breakfast Plan" ? 'breakfast' : 'lunch';

    // First try to get the meal name from the specific selected meals
    if (title == "Breakfast Plan" &&
        widget.breakfastSelectedMeals != null &&
        widget.breakfastSelectedMeals!.isNotEmpty) {
      mealName = widget.breakfastSelectedMeals!.first.name;
    } else if (title == "Lunch Plan" &&
        widget.lunchSelectedMeals != null &&
        widget.lunchSelectedMeals!.isNotEmpty) {
      mealName = widget.lunchSelectedMeals!.first.name;
    } else if (widget.selectedMeals.isNotEmpty) {
      mealName = widget.selectedMeals.first.name;
    } else {
      // Fallback to default meal names
      mealName = mealType == 'breakfast'
          ? MealNames.internationalBreakfast
          : MealNames.internationalLunch;
    }

    // Normalize the meal name
    mealName = normalizeMealName(mealName, mealType);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Section header
        if (!hideMealTitle)
          Row(
            children: [
              Icon(
                icon,
                size: 20,
                color: iconColor,
              ),
              const SizedBox(width: 8),
              Text(
                title,
                style: GoogleFonts.poppins(
                  fontSize: 15,
                  fontWeight: FontWeight.w600,
                  color: AppTheme.textDark,
                ),
              ),
            ],
          ),
        const SizedBox(height: 12),

        // Strict meal image for this plan
        Row(
          children: [
            Image.asset(
              getMealImageAsset(mealName, mealType),
              width: 80,
              height: 80,
              fit: BoxFit.contain,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    mealName,
                    style: GoogleFonts.poppins(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: AppTheme.textDark,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          title == "Breakfast Plan"
                              ? MealConstants.breakfastIconColor
                                  .withOpacity(0.15)
                              : MealConstants.lunchIconColor.withOpacity(0.15),
                          title == "Breakfast Plan"
                              ? MealConstants.breakfastIconColor
                                  .withOpacity(0.15)
                              : MealConstants.lunchIconColor.withOpacity(0.15),
                        ],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: title == "Breakfast Plan"
                            ? MealConstants.breakfastIconColor.withOpacity(0.3)
                            : MealConstants.lunchIconColor.withOpacity(0.3),
                        width: 1,
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: (title == "Breakfast Plan"
                                  ? MealConstants.breakfastIconColor
                                  : MealConstants.lunchIconColor)
                              .withOpacity(0.1),
                          blurRadius: 4,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          title == "Breakfast Plan"
                              ? MealConstants.breakfastIcon
                              : MealConstants.lunchIcon,
                          size: 16,
                          color: title == "Breakfast Plan"
                              ? MealConstants.breakfastIconColor
                              : MealConstants.lunchIconColor,
                        ),
                        const SizedBox(width: 6),
                        Text(
                          title == "Breakfast Plan" ? "Breakfast" : "Lunch",
                          style: GoogleFonts.poppins(
                            fontSize: 13,
                            fontWeight: FontWeight.w600,
                            color: title == "Breakfast Plan"
                                ? MealConstants.breakfastIconColor
                                : MealConstants.lunchIconColor,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),

        // Delivery Mode - Show for all plans, not just custom plans
        _buildDetailRow(
          "Delivery Days",
          // Use the specific delivery mode passed, or calculate from specific weekdays if available
          // Fallback to general delivery mode if neither is provided
          specificDeliveryMode ??
              (specificSelectedWeekdays != null
                  ? _getDeliveryModeForWeekdays(specificSelectedWeekdays)
                  : widget.deliveryMode ?? 'Monday to Friday'),
          Icons.calendar_view_week_outlined,
          indent: true,
        ),

        // Start and End Dates side by side
        if (!isPreOrder)
          Padding(
            padding: EdgeInsets.only(
              bottom: 12,
              left: 8,
            ),
            child: Row(
              children: [
                // Start Date
                Expanded(
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Icon(
                        Icons.date_range_outlined,
                        size: 18,
                        color: Colors.purple,
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              "Start Date",
                              style: GoogleFonts.poppins(
                                fontSize: 13,
                                color: AppTheme.textMedium,
                              ),
                            ),
                            Text(
                              DateFormat('d MMM yyyy').format(startDate),
                              style: GoogleFonts.poppins(
                                fontSize: 14,
                                fontWeight: FontWeight.w500,
                                color: AppTheme.textDark,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),

                const SizedBox(width: 12),

                // End Date
                Expanded(
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Icon(
                        Icons.event_outlined,
                        size: 18,
                        color: Colors.purple,
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              "End Date",
                              style: GoogleFonts.poppins(
                                fontSize: 13,
                                color: AppTheme.textMedium,
                              ),
                            ),
                            Text(
                              DateFormat('d MMM yyyy').format(endDate),
                              style: GoogleFonts.poppins(
                                fontSize: 14,
                                fontWeight: FontWeight.w500,
                                color: AppTheme.textDark,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),

        // Pre-order start and end dates (if applicable)
        if (isPreOrder && widget.preOrderStartDate != null)
          _buildDetailRow(
            "Pre-order From",
            DateFormat('d MMMM yyyy').format(widget.preOrderStartDate!),
            Icons.event_available_outlined,
            indent: true,
            color: iconColor,
          ),

        if (isPreOrder && widget.preOrderEndDate != null)
          _buildDetailRow(
            "Pre-order To",
            DateFormat('d MMMM yyyy').format(widget.preOrderEndDate!),
            Icons.event_available_outlined,
            indent: true,
            color: iconColor,
          ),

        // Amount (if provided)
        if (specificAmount != null)
          _buildDetailRow(
            "Amount",
            "₹${specificAmount.toStringAsFixed(0)}",
            Icons.currency_rupee,
            indent: true,
            color: AppTheme.purple,
          ),

        // Pre-order date (if applicable)
        if (isPreOrder &&
            preOrderDate != null &&
            widget.preOrderStartDate == null &&
            widget.preOrderEndDate == null) ...[
          _buildDetailRow(
            "Start Pre-order Date",
            DateFormat('d MMMM yyyy').format(preOrderDate),
            Icons.event_available_outlined,
            indent: true,
            color: iconColor,
          ),
          if (specificEndDate != null)
            _buildDetailRow(
              "End Pre-order Date",
              DateFormat('d MMMM yyyy').format(specificEndDate),
              Icons.event_available_outlined,
              indent: true,
              color: iconColor,
            ),
        ],
      ],
    );
  }

  // Helper method to get delivery mode text for specific weekdays
  String _getDeliveryModeForWeekdays(List<bool>? selectedWeekdays) {
    if (selectedWeekdays == null) {
      return _getSelectedWeekdaysText();
    }

    return PreOrderDateCalculator.getDeliveryModeText(selectedWeekdays);
  }

  // Helper method to get delivery mode for single meal plans
  String _getDeliveryModeForSinglePlan() {
    // First try to use the specific delivery mode based on meal type
    if (widget.mealType == 'breakfast' &&
        widget.breakfastDeliveryMode != null) {
      return widget.breakfastDeliveryMode!;
    }
    if (widget.mealType == 'lunch' && widget.lunchDeliveryMode != null) {
      return widget.lunchDeliveryMode!;
    }

    // Then try the general delivery mode
    if (widget.deliveryMode != null) {
      return widget.deliveryMode!;
    }

    // Calculate from selected weekdays if available
    if (widget.selectedWeekdays.isNotEmpty) {
      return PreOrderDateCalculator.getDeliveryModeText(
          widget.selectedWeekdays);
    }

    // Default fallback
    return 'Monday to Friday';
  }

  // Helper method to build detail rows with optional indentation
  Widget _buildDetailRow(
    String label,
    String value,
    IconData icon, {
    Color color = AppTheme.purple,
    bool indent = false,
  }) {
    return Padding(
      padding: EdgeInsets.only(
        bottom: 12,
        left: indent ? 8 : 0,
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            icon,
            size: 18,
            color: Colors.purple,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: GoogleFonts.poppins(
                    fontSize: 13,
                    color: AppTheme.textMedium,
                  ),
                ),
                Text(
                  value,
                  style: GoogleFonts.poppins(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: AppTheme.textDark,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Color _getMealTypeColor(String mealType) {
    switch (mealType) {
      case 'breakfast':
        return MealConstants.breakfastIconColor;
      case 'express':
        return MealConstants.expressIconColor;
      case 'lunch':
      default:
        return MealConstants.lunchIconColor;
    }
  }

  IconData _getMealTypeIcon(String mealType) {
    switch (mealType) {
      case 'breakfast':
        return Icons.ramen_dining;
      case 'express':
        return Icons.local_shipping_rounded;
      case 'lunch':
      default:
        return Icons.flatware_rounded;
    }
  }

  String _getMealTypeDescription(String mealType) {
    switch (mealType) {
      case 'breakfast':
        return 'Healthy breakfast options delivered to your child at school.';
      case 'express':
        return 'Same-day lunch delivery with express service (additional fee applies).';
      case 'lunch':
      default:
        return 'Nutritious lunch delivered to your child during school lunch hours.';
    }
  }

  Widget _buildMealPlanTab(
      String title, bool isSelected, IconData icon, Color color) {
    return Expanded(
      child: Container(
        padding: const EdgeInsets.symmetric(
          vertical: 12,
        ),
        decoration: BoxDecoration(
          gradient: isSelected
              ? LinearGradient(
                  colors: [
                    color.withOpacity(0.1),
                    color.withOpacity(0.1),
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                )
              : null,
          color: isSelected ? null : Colors.transparent,
          borderRadius: BorderRadius.circular(50),
        ),
        child: Center(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                size: 16,
                color: isSelected ? color : AppTheme.textMedium,
              ),
              const SizedBox(width: 8),
              Text(
                title,
                style: GoogleFonts.poppins(
                  fontSize: 13,
                  fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                  color: isSelected ? color : AppTheme.textMedium,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // Restore the original _applyPromoCode logic
  Future<void> _applyPromoCode() async {
    setState(() {
      _isValidatingPromo = true;
      _promoErrorMessage = '';
    });
    try {
      final student = widget.selectedStudent;
      if (student == null) {
        setState(() {
          _promoErrorMessage = 'No student selected.';
          _isValidatingPromo = false;
        });
        return;
      }
      final orderId = 12345; // Replace with real order ID if you have one
      final customerId = int.tryParse(student.id) ?? 1543;
      final locationId = 16; // You can improve this logic as needed
      final planQuantities = [widget.selectedMeals.length];
      final cartQuantities = [widget.selectedMeals.length];
      final productCodes = widget.selectedMeals
          .map((m) => int.tryParse(m.id))
          .where((c) => c != null)
          .cast<int>()
          .toList();

      // Print cURL equivalent
      final curl = '''\ncurl -X 'POST' \\
  'http://192.168.1.167:8003/api/v2/quickserve/orders/apply-coupon' \\
  -H 'accept: application/json' \\
  -H 'Content-Type: application/json' \\
  -d '{\n  "promo_code": "${_promoController.text.trim()}",\n  "order_id": $orderId,\n  "order_amount": ${widget.totalAmount},\n  "product_codes": ${productCodes.map((e) => '"$e"').toList()},\n  "company_id": 8163,\n  "unit_id": 8163,\n  "customer_id": $customerId,\n  "location_id": $locationId,\n  "plan_quantity": [${planQuantities.map((q) => '{"quantity": $q}').join(', ')}],\n  "cart_items": [${cartQuantities.map((q) => '{"quantity": $q}').join(', ')}],\n  "plan_type": "${widget.planType}"\n}'\n''';
      print('PROMO API cURL:\n$curl');
      print('Current plan type: ${widget.planType}');
      print('Selected plan type: ${widget.selectedPlanType}');

      final response = await _promoCodeService.applyPromoCode(
        promoCode: _promoController.text.trim(),
        orderId: orderId,
        orderAmount: widget.totalAmount,
        productCodes: productCodes,
        companyId: 8163,
        unitId: 8163,
        customerId: customerId,
        locationId: locationId,
        planQuantities: planQuantities,
        cartQuantities: cartQuantities,
        planType: widget.planType, // Pass the plan type
      );
      print('PROMO API RESPONSE: success=[32m${response.success}[0m, message=${response.message}, discountAmount=${response.discountAmount}');
      setState(() {
        _promoCodeResponse = response;
        _isPromoValid = response.success;
        _appliedPromoCode = _promoController.text.trim();
        _promoDiscount = response.discountAmount ?? 0.0;
        _promoErrorMessage = response.success ? '' : (response.message ?? 'Invalid promo code');
      });

      // Show success message with confetti animation if promo applied successfully
      if (response.success) {
        _showConfettiSuccess();
      }
    } catch (e) {
      print('PROMO API ERROR: $e');
      
      // Extract error message from exception
      String errorMessage = 'Failed to apply promo code.';
      if (e.toString().contains('Exception: Failed to apply promo code:')) {
        try {
          // Extract JSON from the exception message
          String jsonStr = e.toString().split('Exception: Failed to apply promo code: ')[1];
          final Map<String, dynamic> errorData = json.decode(jsonStr);
          errorMessage = errorData['message'] ?? 'Failed to apply promo code.';
        } catch (parseError) {
          print('Error parsing exception message: $parseError');
        }
      }

      setState(() {
        _promoErrorMessage = errorMessage;
        _isPromoValid = false;
        _promoDiscount = 0.0;
      });

      // Show error message in snackbar as well
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            errorMessage,
            style: GoogleFonts.poppins(fontWeight: FontWeight.w500),
          ),
          backgroundColor: Colors.red,
          duration: Duration(seconds: 3),
        ),
      );
    } finally {
      setState(() {
        _isValidatingPromo = false;
      });
    }
  }

  // Calculate GST amount
  double get _gstAmount {
    double amountAfterDiscount = widget.totalAmount - _promoDiscount;
    return amountAfterDiscount * _gstPercentage;
  }

  // Calculate final amount after promo code discount, GST, and delivery charges
  double get _finalAmount {
    double amountAfterDiscount = widget.totalAmount - _promoDiscount;
    return amountAfterDiscount + _gstAmount + _deliveryCharges;
  }

  // Build promo code section
  Widget _buildPromoCodeSection() {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 1,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Section title with icon
            Row(
              children: [
                const Icon(
                  Icons.discount_outlined,
                  size: 20,
                  color: AppTheme.purple,
                ),
                const SizedBox(width: 8),
                Text(
                  "Promo Code",
                  style: GoogleFonts.poppins(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: AppTheme.textDark,
                  ),
                ),
              ],
            ),
            Divider(height: 24, color: Colors.grey.withOpacity(0.2)),

            // Promo code input field with container styling
            Row(
              children: [
                Expanded(
                  child: Container(
                    margin: const EdgeInsets.only(left: 8, right: 8),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: TextField(
                      controller: _promoController,
                      textCapitalization: TextCapitalization.characters,
                      decoration: InputDecoration(
                        hintText: "Enter Promo Code",
                        hintStyle: GoogleFonts.poppins(
                          fontSize: 14,
                          color: Colors.grey.shade500,
                        ),
                        border: InputBorder.none,
                        contentPadding:
                            const EdgeInsets.symmetric(horizontal: 12),
                        prefixIcon: const Icon(
                          Icons.discount_outlined,
                          size: 18,
                        ),
                        suffixIcon: _promoController.text.isNotEmpty
                            ? IconButton(
                                icon: const Icon(Icons.clear, size: 16),
                                onPressed: () {
                                  setState(() {
                                    _promoController.clear();
                                    _promoErrorMessage = '';
                                  });
                                },
                              )
                            : null,
                      ),
                      style: GoogleFonts.poppins(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                      onChanged: (value) {
                        if (_promoErrorMessage.isNotEmpty) {
                          setState(() {
                            _promoErrorMessage = '';
                          });
                        }
                      },
                    ),
                  ),
                ),
                Container(
                  margin: const EdgeInsets.only(right: 8),
                  height: 40,
                  decoration: BoxDecoration(
                    gradient: AppTheme.purpleToDeepPurple,
                    borderRadius: BorderRadius.circular(50),
                  ),
                  child: ElevatedButton(
                    onPressed: _isValidatingPromo ? null : _applyPromoCode,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.transparent,
                      shadowColor: Colors.transparent,
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(50),
                      ),
                      padding: const EdgeInsets.symmetric(
                          vertical: 12, horizontal: 14),
                      elevation: 0,
                    ),
                    child: _isValidatingPromo
                        ? SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor:
                                  AlwaysStoppedAnimation<Color>(Colors.white),
                            ),
                          )
                        : Text(
                            "Apply",
                            style: GoogleFonts.poppins(
                              fontSize: 12,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Applied promo code section
            if (_isPromoValid && _appliedPromoCode != null) ...[
              Container(
                height: 65,
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.green.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.green.withOpacity(0.3)),
                ),
                child: Row(
                  children: [
                    const Icon(
                      Icons.check_circle_outline,
                      color: Colors.green,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            "Promo applied: $_appliedPromoCode",
                            style: GoogleFonts.poppins(
                              fontWeight: FontWeight.w600,
                              color: Colors.green,
                            ),
                          ),
                          Text(
                            "You saved ₹${_promoDiscount.toStringAsFixed(0)}",
                            style: GoogleFonts.poppins(
                              fontSize: 13,
                              color: Colors.green,
                            ),
                          ),
                        ],
                      ),
                    ),
                    IconButton(
                      onPressed: _removePromoCode,
                      icon: const Icon(Icons.close, size: 18),
                      color: Colors.grey.shade700,
                      padding: EdgeInsets.zero,
                      constraints: const BoxConstraints(),
                    ),
                  ],
                ),
              ),
            ],

            if (_promoErrorMessage.isNotEmpty)
              Padding(
                padding: const EdgeInsets.all(8.0),
                child: Row(
                  children: [
                    Icon(
                      Icons.error_outline,
                      size: 14,
                      color: Colors.red.shade700,
                    ),
                    const SizedBox(width: 6),
                    Expanded(
                      child: Text(
                        _promoErrorMessage,
                        style: GoogleFonts.poppins(
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                          color: Colors.red.shade700,
                        ),
                      ),
                    ),
                  ],
                ),
              ),

            const SizedBox(height: 16),

            // Price summary
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: AppTheme.purple.withOpacity(0.05),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: AppTheme.purple.withOpacity(0.2),
                  width: 1,
                ),
              ),
              child: Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        "Subtotal",
                        style: GoogleFonts.poppins(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                          color: AppTheme.textDark,
                        ),
                      ),
                      Text(
                        "₹${widget.totalAmount.toStringAsFixed(0)}",
                        style: GoogleFonts.poppins(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                          color: AppTheme.textDark,
                        ),
                      ),
                    ],
                  ),

                  if (_promoDiscount > 0) ...[
                    const SizedBox(height: 8),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Row(
                          children: [
                            Text(
                              "Promo Discount ",
                              style: GoogleFonts.poppins(
                                fontSize: 14,
                                fontWeight: FontWeight.w500,
                                color: Colors.green,
                              ),
                            ),
                            Text(
                              "($_appliedPromoCode)",
                              style: GoogleFonts.poppins(
                                fontSize: 12,
                                fontWeight: FontWeight.w500,
                                color: Colors.green,
                                fontStyle: FontStyle.italic,
                              ),
                            ),
                          ],
                        ),
                        Text(
                          "-₹${_promoDiscount.toStringAsFixed(0)}",
                          style: GoogleFonts.poppins(
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                            color: Colors.green,
                          ),
                        ),
                      ],
                    ),
                  ],

                  const SizedBox(height: 8),

                  // GST
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        "GST (5%)",
                        style: GoogleFonts.poppins(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                          color: AppTheme.textMedium,
                        ),
                      ),
                      Text(
                        "+₹${_gstAmount.toStringAsFixed(0)}",
                        style: GoogleFonts.poppins(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                          color: AppTheme.textMedium,
                        ),
                      ),
                    ],
                  ),

                  // Delivery Charges
                  const SizedBox(height: 8),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        "Delivery Charges",
                        style: GoogleFonts.poppins(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                          color: AppTheme.textMedium,
                        ),
                      ),
                      Text(
                        _deliveryCharges > 0
                            ? "+₹${_deliveryCharges.toStringAsFixed(0)}"
                            : "FREE",
                        style: GoogleFonts.poppins(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                          color: _deliveryCharges > 0
                              ? AppTheme.textMedium
                              : Colors.green,
                        ),
                      ),
                    ],
                  ),

                  // const Divider(height: 16),
                  Divider(height: 24, color: Colors.grey.withOpacity(0.2)),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        "Payable Amount",
                        style: GoogleFonts.poppins(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: AppTheme.textDark,
                        ),
                      ),
                      Text(
                        "₹${_finalAmount.toStringAsFixed(0)}",
                        style: GoogleFonts.poppins(
                          fontSize: 18,
                          fontWeight: FontWeight.w700,
                          foreground: Paint()
                            ..shader = LinearGradient(
                              colors: [
                                AppTheme.purple,
                                AppTheme.deepPurple,
                              ],
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                            ).createShader(
                                const Rect.fromLTWH(0.0, 0.0, 200.0, 70.0)),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Build section with title
  Widget _buildSectionWithTitle({
    required BuildContext context,
    required String title,
    required IconData icon,
    required bool withoutPadding,
    required List<Widget> children,
  }) {
    return _buildCardSection(
      title: title,
      icon: icon,
      children: children,
    );
  }

  // Build order information row
  Widget _buildOrderInformationRow(String label, String value) {
    return _buildStudentInfoRow(
      icon: Icons.info_outline_rounded,
      label: label,
      value: value,
    );
  }

  // Build student info row with icon
  Widget _buildStudentInfoRow({
    required IconData icon,
    required String label,
    required String value,
    TextStyle? valueStyle,
    bool isAlert = false,
    bool isLast = false,
    Color? iconColor,
    Color? backgroundColor,
  }) {
    final iconColorValue =
        iconColor ?? (isAlert ? Colors.red.shade700 : AppTheme.purple);
    final backgroundColorValue = backgroundColor ??
        (isAlert ? Colors.red.shade50 : AppTheme.purple.withOpacity(0.08));

    return Padding(
      padding: EdgeInsets.only(bottom: isLast ? 0 : 16),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: backgroundColorValue,
              borderRadius: BorderRadius.circular(8),
              boxShadow: [
                BoxShadow(
                  color: iconColorValue.withOpacity(0.1),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Icon(
              icon,
              size: 18,
              color: iconColorValue,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: GoogleFonts.poppins(
                    fontSize: 14,
                    color: AppTheme.textMedium,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  value,
                  style: valueStyle ??
                      GoogleFonts.poppins(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        color: AppTheme.textDark,
                      ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Build enhanced meal card - Matching row style from Subscription Plan
  Widget _buildEnhancedMealCard(Meal meal) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        color: Colors.white,
        border: Border(
          bottom: BorderSide(
            color: Colors.grey.withOpacity(0.15),
            width: 1,
          ),
        ),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Meal image with radio-button style circle when selected
          Stack(
            alignment: Alignment.center,
            children: [
              Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: AppTheme.deepPurple.withOpacity(0.1),
                      blurRadius: 6,
                      offset: const Offset(0, 3),
                    ),
                  ],
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(12),
                  child: Container(
                    height: 90,
                    width: 90,
                    decoration: BoxDecoration(
                      color: Colors.white,
                    ),
                    child: meal.imageUrl.isNotEmpty
                        ? Image.asset(
                            meal.imageUrl,
                            fit: BoxFit.contain,
                            errorBuilder: (context, error, stackTrace) {
                              return _buildMealPlaceholder();
                            },
                          )
                        : _buildMealPlaceholder(),
                  ),
                ),
              ),
              // Selected indicator circle in upper left
            ],
          ),

          const SizedBox(width: 12),

          // Meal details
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Meal name with veg icon
                Row(
                  children: [
                    const VegIcon(),
                    const SizedBox(width: 6),
                    Expanded(
                      child: Text(
                        meal.name,
                        style: GoogleFonts.poppins(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: AppTheme.textDark,
                        ),
                      ),
                    ),
                    Positioned(
                      top: 0,
                      right: 0,
                      child: Container(
                        width: 20,
                        height: 20,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          gradient: LinearGradient(
                            colors: [Colors.green, Colors.green.withOpacity(1)],
                            begin: Alignment.topLeft,
                            end: Alignment.centerRight,
                          ),
                          // boxShadow: [
                          //   BoxShadow(
                          //     color: AppTheme.purple.withOpacity(0.3),
                          //     blurRadius: 4,
                          //     offset: const Offset(0, 1),
                          //   ),
                          // ],
                        ),
                        child: const Center(
                          child: Icon(
                            Icons.check,
                            color: Colors.white,
                            size: 12,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 4),

                // Meal type
                Row(
                  children: [
                    Icon(
                      Icons.restaurant,
                      size: 14,
                      color: AppTheme.purple,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      '${meal.categories.first.toString().split('.').last} Meal',
                      style: GoogleFonts.poppins(
                        fontSize: 13,
                        color: AppTheme.textMedium,
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 8),

                // Price tag - Matching subscription plan tag style
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
                  decoration: BoxDecoration(
                    gradient: AppTheme.purpleToDeepPurple,
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        color: AppTheme.deepPurple.withOpacity(0.2),
                        blurRadius: 4,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Text(
                    '₹${meal.price.toStringAsFixed(0)}',
                    style: GoogleFonts.poppins(
                      fontSize: 13,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Build meal placeholder
  Widget _buildMealPlaceholder() {
    return Container(
      height: 70,
      width: 70,
      color: Colors.grey[100],
      child: Icon(
        Icons.restaurant_rounded,
        size: 24,
        color: AppTheme.purple.withOpacity(0.7),
      ),
    );
  }

  // Meal Plan implementation - added from PaymentMethodScreen
  Widget _buildSelectedMealCard(String name, String imageUrl, String mealType) {
    // Determine if we should show express fee
    final bool isExpress = mealType == 'express';
    final double mealPrice = widget.totalAmount / widget.mealDates.length;
    final Color typeColor = _getMealTypeColor(mealType);

    // Use strict asset mapping for meal image
    return Card(
      color: Colors.white,
      margin: EdgeInsets.zero,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(
          color: typeColor.withOpacity(0.3),
          width: 1,
        ),
      ),
      elevation: 4,
      shadowColor: typeColor.withOpacity(0.1),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Strict meal image
          ClipRRect(
            borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
            child: Image.asset(
              getMealImageAsset(name, mealType),
              width: double.infinity,
              height: 160,
              fit: BoxFit.contain,
            ),
          ),
          // Meal details
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Meal name with veg icon
                Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Container(
                      padding: const EdgeInsets.all(1.0),
                      decoration: BoxDecoration(
                        border: Border.all(
                          color: Colors.green,
                          width: 1,
                        ),
                      ),
                      child: const Icon(
                        Icons.circle,
                        size: 10,
                        color: Colors.green,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        name,
                        style: GoogleFonts.poppins(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: AppTheme.textDark,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                // Price and details row
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Row(
                      children: [
                        Icon(
                          mealType == 'lunch'
                              ? Icons.flatware
                              : Icons.ramen_dining,
                          size: 18,
                          color: mealType == 'lunch'
                              ? MealConstants.lunchIconColor
                              : MealConstants.breakfastIconColor,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          mealType == 'breakfast'
                              ? 'Breakfast Meal'
                              : mealType == 'express'
                                  ? 'Express Lunch Meal'
                                  : 'Lunch Meal',
                          style: GoogleFonts.poppins(
                            fontSize: 13,
                            color: AppTheme.textMedium,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 12, vertical: 6),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                            Colors.purple.withOpacity(0.2),
                            Colors.purple.withOpacity(0.2),
                          ],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        '₹${mealPrice.toStringAsFixed(0)}',
                        style: GoogleFonts.poppins(
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                          color: AppTheme.purple,
                        ),
                      ),
                    ),
                  ],
                ),
                if (isExpress) ...[
                  const SizedBox(height: 12),
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.orange.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: Colors.orange.withOpacity(0.3),
                        width: 1,
                      ),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          Icons.access_time_filled_rounded,
                          size: 16,
                          color: Colors.orange.shade800,
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            'Express delivery includes priority handling for same-day orders',
                            style: GoogleFonts.poppins(
                              fontSize: 12,
                              color: Colors.orange.shade800,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Update the student info section to handle null student
  Widget _buildStudentInfoSection() {
    if (widget.selectedStudent == null) {
      return Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: AppTheme.purple.withOpacity(0.15),
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withOpacity(0.07),
              blurRadius: 6,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          children: [
            _buildStudentInfoRow(
              icon: Icons.info_outline_rounded,
              label: 'Student Profile',
              value: 'No student profile selected',
              isAlert: true,
            ),
          ],
        ),
      );
    }

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: AppTheme.purple.withOpacity(0.15),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.07),
            blurRadius: 6,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          _buildStudentInfoRow(
            icon: Icons.person_outline_rounded,
            label: 'Student Name',
            value: widget.selectedStudent!.name,
          ),
          _buildStudentInfoRow(
            icon: Icons.school_rounded,
            label: 'Class',
            value: widget.selectedStudent!.className,
          ),
          _buildStudentInfoRow(
            icon: Icons.book_rounded,
            label: 'Section',
            value: widget.selectedStudent!.section,
          ),
          _buildStudentInfoRow(
            icon: Icons.domain_rounded,
            label: 'Floor',
            value: widget.selectedStudent!.floor,
          ),
          if (widget.selectedStudent!.allergies.isNotEmpty)
            _buildStudentInfoRow(
              icon: Icons.healing_rounded,
              label: 'Allergies',
              value: widget.selectedStudent!.allergies,
              isAlert: true,
            ),
        ],
      ),
    );
  }

  // Build card section with title
  Widget _buildCardSection({
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 1,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Section title with icon
            Row(
              children: [
                Icon(
                  icon,
                  size: 20,
                  color: AppTheme.purple,
                ),
                const SizedBox(width: 8),
                Text(
                  title,
                  style: GoogleFonts.poppins(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: AppTheme.textDark,
                  ),
                ),
              ],
            ),
            Divider(height: 24, color: Colors.grey.withOpacity(0.2)),
            ...children,
          ],
        ),
      ),
    );
  }

  // Build normal meal plan section - this is for the _buildMealPlanSection() referenced on line 366
  Widget _buildMealPlanSection() {
    // Determine meal type - breakfast, lunch or express
    final String mealType = widget.mealType ??
        (widget.selectedMeals.isNotEmpty &&
                widget.selectedMeals.first.categories
                    .contains(MealCategory.breakfast)
            ? 'breakfast'
            : widget.isExpressOrder
                ? 'express'
                : 'lunch');

    final String mealName = widget.selectedMeals.isNotEmpty
        ? normalizeMealName(widget.selectedMeals.first.name, mealType)
        : (mealType == 'breakfast'
            ? MealNames.breakfastOfTheDay
            : mealType == 'express'
                ? MealNames.lunchOfTheDay
                : MealNames.lunchOfTheDay);
    return _buildSelectedMealCard(
      mealName,
      widget.selectedMeals.isNotEmpty &&
              widget.selectedMeals.first.imageUrl.isNotEmpty
          ? widget.selectedMeals.first.imageUrl
          : mealType == 'breakfast'
              ? 'assets/images/breakfast/breakfast of the day (most recommended).png'
              : 'assets/images/lunch/lunch of the day (most recommended).png',
      mealType,
    );
  }

  // Helper to strictly map meal names to allowed asset images
  String getMealImageAsset(String mealName, String mealType) {
    final name = mealName.trim().toLowerCase();
    if (mealType == 'breakfast') {
      if (name == 'breakfast of the day')
        return 'assets/images/breakfast/breakfast of the day (most recommended).png';
      if (name == 'indian breakfast')
        return 'assets/images/breakfast/Indian Breakfast.png';
      if (name == 'international breakfast')
        return 'assets/images/breakfast/International Breakfast.png';
      if (name == 'jain breakfast')
        return 'assets/images/breakfast/Jain Breakfast.png';
    } else if (mealType == 'lunch') {
      if (name == 'lunch of the day')
        return 'assets/images/lunch/lunch of the day (most recommended).png';
      if (name == 'indian lunch') return 'assets/images/lunch/Indian Lunch.png';
      if (name == 'international lunch')
        return 'assets/images/lunch/International Lunch.png';
      if (name == 'jain lunch') return 'assets/images/lunch/Jain Lunch.png';
    }
    // fallback
    return mealType == 'breakfast'
        ? 'assets/images/breakfast/breakfast of the day (most recommended).png'
        : 'assets/images/lunch/lunch of the day (most recommended).png';
  }

  void _removePromoCode() {
    setState(() {
      _isPromoValid = false;
      _appliedPromoCode = null;
      _promoDiscount = 0.0;
      _promoController.clear();
      _promoErrorMessage = '';
      _promoCodeResponse = null;
    });
  }

  // Show confetti animation for successful promo code application
  void _showConfettiSuccess() {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return Dialog(
          backgroundColor: Colors.transparent,
          child: Container(
            width: 300,
            height: 400,
            child: Stack(
              children: [
                // Confetti animation
                Positioned.fill(
                  child: Lottie.asset(
                    'assets/animations/confetti.json',
                    repeat: false,
                    animate: true,
                  ),
                ),
                // Success message
                Center(
                  child: Card(
                    elevation: 8,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(24.0),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.check_circle,
                            color: Colors.green,
                            size: 48,
                          ),
                          SizedBox(height: 16),
                          Text(
                            'Promo Applied!',
                            style: GoogleFonts.poppins(
                              fontSize: 20,
                              fontWeight: FontWeight.bold,
                              color: Colors.green,
                            ),
                          ),
                          SizedBox(height: 8),
                          Text(
                            'You saved ₹${_promoDiscount.toStringAsFixed(2)}',
                            style: GoogleFonts.poppins(
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          SizedBox(height: 16),
                          ElevatedButton(
                            onPressed: () => Navigator.of(context).pop(),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.green,
                              foregroundColor: Colors.white,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                            ),
                            child: Text('Great!'),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );

    // Auto-close after 4 seconds
    Future.delayed(Duration(seconds: 4), () {
      if (Navigator.of(context).canPop()) {
        Navigator.of(context).pop();
      }
    });
  }

  // Normalize meal name to match predefined meal types
  String normalizeMealName(String mealName, String mealType) {
    final name = mealName.trim().toLowerCase();
    
    if (mealType == 'breakfast') {
      if (name.contains('breakfast of the day') || name.contains('most recommended')) {
        return 'Breakfast of the Day';
      } else if (name.contains('indian')) {
        return 'Indian Breakfast';
      } else if (name.contains('international')) {
        return 'International Breakfast';
      } else if (name.contains('jain')) {
        return 'Jain Breakfast';
      }
      return 'Breakfast of the Day'; // Default fallback
    } else if (mealType == 'lunch' || mealType == 'express') {
      if (name.contains('lunch of the day') || name.contains('most recommended')) {
        return 'Lunch of the Day';
      } else if (name.contains('indian')) {
        return 'Indian Lunch';
      } else if (name.contains('international')) {
        return 'International Lunch';
      } else if (name.contains('jain')) {
        return 'Jain Lunch';
      }
      return 'Lunch of the Day'; // Default fallback
    }
    
    return mealName; // Return original if no match
  }

  // Format currency display
  String formatCurrency(double amount) {
    return '₹${amount.toStringAsFixed(0)}';
  }

  // Calculate discount percentage
  double calculateDiscountPercentage(double originalAmount, double discountAmount) {
    if (originalAmount <= 0) return 0.0;
    return (discountAmount / originalAmount) * 100;
  }

  // Validate promo code format
  bool isValidPromoCodeFormat(String code) {
    if (code.isEmpty) return false;
    // Allow alphanumeric characters only, 3-20 characters
    return RegExp(r'^[A-Z0-9]{3,20}$').hasMatch(code.toUpperCase());
  }

  // Format date for display
  String formatDate(DateTime date) {
    return DateFormat('dd MMM yyyy').format(date);
  }

  // Get meal type display name
  String getMealTypeDisplayName(String mealType) {
    switch (mealType.toLowerCase()) {
      case 'breakfast':
        return 'Breakfast';
      case 'lunch':
        return 'Lunch';
      case 'express':
        return 'Express Lunch';
      case 'both':
        return 'Breakfast & Lunch';
      default:
        return mealType.capitalize ?? mealType;
    }
  }

  // Check if order is valid for processing
  bool isOrderValid() {
    // Basic validation checks
    if (widget.totalAmount <= 0) return false;
    if (widget.mealDates.isEmpty) return false;
    if (widget.selectedMeals.isEmpty) return false;
    
    // Check if end date is after start date
    if (widget.endDate.isBefore(widget.startDate)) return false;
    
    return true;
  }

  // Get order summary text for sharing or logging
  String getOrderSummaryText() {
    final StringBuffer summary = StringBuffer();
    summary.writeln('--- Order Summary ---');
    
    if (widget.selectedStudent != null) {
      summary.writeln('Student: ${widget.selectedStudent!.name}');
      summary.writeln('School: ${widget.selectedStudent!.schoolName}');
      summary.writeln('Class: ${widget.selectedStudent!.className} - ${widget.selectedStudent!.division}');
    }
    
    summary.writeln('Plan Type: ${widget.planType}');
    summary.writeln('Meal Type: ${widget.mealType ?? 'Not specified'}');
    summary.writeln('Start Date: ${formatDate(widget.startDate)}');
    summary.writeln('End Date: ${formatDate(widget.endDate)}');
    summary.writeln('Total Meals: ${widget.mealDates.length}');
    summary.writeln('Subtotal: ${formatCurrency(widget.totalAmount)}');
    
    if (_promoDiscount > 0) {
      summary.writeln('Promo Code: $_appliedPromoCode');
      summary.writeln('Discount: -${formatCurrency(_promoDiscount)}');
    }
    
    summary.writeln('GST (5%): +${formatCurrency(_gstAmount)}');
    summary.writeln('Delivery: ${_deliveryCharges > 0 ? formatCurrency(_deliveryCharges) : 'FREE'}');
    summary.writeln('Final Amount: ${formatCurrency(_finalAmount)}');
    
    return summary.toString();
  }
}
