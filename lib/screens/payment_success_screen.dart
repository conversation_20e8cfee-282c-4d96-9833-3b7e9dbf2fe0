import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class PaymentSuccessScreen extends StatelessWidget {
  final String? orderId;
  final double? amount;
  final String? message;

  const PaymentSuccessScreen({Key? key, this.orderId, this.amount, this.message}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        automaticallyImplyLeading: false,
        title: Text('Payment Successful', style: GoogleFonts.poppins()),
        backgroundColor: Colors.green,
      ),
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.check_circle, color: Colors.green, size: 80),
              SizedBox(height: 24),
              Text(
                message ?? 'Your payment was successful!',
                style: GoogleFonts.poppins(fontSize: 20, fontWeight: FontWeight.w600),
                textAlign: TextAlign.center,
              ),
              if (amount != null) ...[
                SizedBox(height: 16),
                Text('Amount Paid: ₹${amount!.toStringAsFixed(2)}', style: GoogleFonts.poppins(fontSize: 16)),
              ],
              if (orderId != null) ...[
                SizedBox(height: 8),
                Text('Order ID: $orderId', style: GoogleFonts.poppins(fontSize: 14, color: Colors.grey[700])),
              ],
              SizedBox(height: 32),
              ElevatedButton(
                onPressed: () {
                  Navigator.pushNamedAndRemoveUntil(context, '/main', (route) => false);
                },
                style: ElevatedButton.styleFrom(backgroundColor: Colors.green),
                child: Text('Go to My Subscriptions', style: GoogleFonts.poppins()),
              ),
            ],
          ),
        ),
      ),
    );
  }
} 