import 'dart:developer';
import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:startwell/models/meal_model.dart';
import 'package:startwell/models/student_model.dart';
import 'package:startwell/services/student_profile_service.dart';
import 'package:startwell/utils/meal_plan_validator.dart';
import 'package:startwell/themes/app_theme.dart';
import 'package:startwell/screens/main_screen.dart';
import 'package:startwell/screens/my_subscription_screen.dart';
import 'package:intl/intl.dart';
import 'package:startwell/widgets/common/gradient_app_bar.dart';
import 'package:startwell/widgets/common/gradient_button.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:collection/collection.dart';
import '../utils/toast_utils.dart';

class PhonePeDummyScreen extends StatefulWidget {
  final String planType;
  final bool isCustomPlan;
  final List<bool> selectedWeekdays;
  final DateTime startDate;
  final DateTime endDate;
  final List<DateTime> mealDates;
  final double totalAmount;
  final List<Meal> selectedMeals;
  final bool isExpressOrder;
  final Student selectedStudent;
  final String? mealType;
  final DateTime? breakfastPreOrderDate;
  final DateTime? lunchPreOrderDate;
  final String? breakfastDeliveryMode;
  final String? lunchDeliveryMode;
  final List<bool>? breakfastSelectedWeekdays;
  final List<bool>? lunchSelectedWeekdays;
  final DateTime? preOrderStartDate;
  final DateTime? preOrderEndDate;

  const PhonePeDummyScreen({
    Key? key,
    required this.planType,
    required this.isCustomPlan,
    required this.selectedWeekdays,
    required this.startDate,
    required this.endDate,
    required this.mealDates,
    required this.totalAmount,
    required this.selectedMeals,
    required this.isExpressOrder,
    required this.selectedStudent,
    this.mealType,
    this.breakfastPreOrderDate,
    this.lunchPreOrderDate,
    this.breakfastDeliveryMode,
    this.lunchDeliveryMode,
    this.breakfastSelectedWeekdays,
    this.lunchSelectedWeekdays,
    this.preOrderStartDate,
    this.preOrderEndDate,
  }) : super(key: key);

  @override
  State<PhonePeDummyScreen> createState() => _PhonePeDummyScreenState();
}

class _PhonePeDummyScreenState extends State<PhonePeDummyScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: GradientAppBar(
        titleText: 'PhonePe Payment',
      ),
      body: Column(
        children: [
          Expanded(
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.payment,
                    size: 64,
                    color: AppTheme.purple,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'PhonePe Payment Screen',
                    style: GoogleFonts.poppins(
                      fontSize: 24,
                      fontWeight: FontWeight.w600,
                      color: AppTheme.textDark,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'This is a dummy screen for PhonePe payment integration',
                    style: GoogleFonts.poppins(
                      fontSize: 16,
                      color: AppTheme.textMedium,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 24),
                  Text(
                    'Amount to Pay: ₹${widget.totalAmount.toStringAsFixed(0)}',
                    style: GoogleFonts.poppins(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: AppTheme.purple,
                    ),
                  ),
                ],
              ),
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: SizedBox(
              width: double.infinity,
              height: 60,
              child: GradientButton(
                text: 'Pay Now',
                isFullWidth: true,
                onPressed: () => _processPayment(context),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _processPayment(BuildContext context) async {
    // Determine if both meal types are selected
    final bool hasBothMealTypes = (widget.mealType == 'both') ||
        (widget.breakfastPreOrderDate != null &&
            widget.lunchPreOrderDate != null);

    // Determine the meal plan type from the mealType parameter or from the selected meals
    final String planType = widget.mealType ??
        (widget.selectedMeals.first.categories.first == MealCategory.breakfast
            ? 'breakfast'
            : widget.selectedMeals.first.categories.first ==
                    MealCategory.expressOneDay
                ? 'express'
                : 'lunch');

    // Validate one last time
    final String? validationError =
        MealPlanValidator.validateMealPlan(widget.selectedStudent, planType);

    if (validationError != null) {
      ToastUtils.showToast(
        context: context,
        message: validationError,
        type: ToastType.error,
      );
      return;
    }

    // Show loading indicator
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const Center(
        child: CircularProgressIndicator(),
      ),
    );

    try {
      // Process payment (simulated for demo)
      await Future.delayed(const Duration(seconds: 2));

      // Extract correct meal for each plan
      Meal? breakfastMeal = widget.selectedMeals.firstWhereOrNull(
          (m) => m.categories.contains(MealCategory.breakfast));
      Meal? lunchMeal = widget.selectedMeals
          .firstWhereOrNull((m) => m.categories.contains(MealCategory.lunch));

      final StudentProfileService profileService = StudentProfileService();
      bool success = false;

      if (hasBothMealTypes || widget.mealType == 'both') {
        // Assign both breakfast and lunch plans with correct meal preferences and dates
        success = await profileService.assignMealPlan(
          widget.breakfastPreOrderDate ?? widget.startDate,
          widget.selectedStudent.id,
          'breakfast',
          widget.preOrderEndDate ?? widget.endDate,
          mealPreference: breakfastMeal?.name ?? 'Breakfast of the Day',
          selectedWeekdays: (widget.breakfastSelectedWeekdays?.cast<int>()) ??
              (widget.lunchSelectedWeekdays?.cast<int>()) ??
              (widget.isCustomPlan
                  ? widget.selectedWeekdays
                      .asMap()
                      .entries
                      .where((entry) => entry.value)
                      .map((entry) => entry.key + 1)
                      .toList()
                  : null),
        );
        if (success) {
          success = await profileService.assignMealPlan(
            widget.lunchPreOrderDate ?? widget.startDate,
            widget.selectedStudent.id,
            'lunch',
            widget.preOrderEndDate ?? widget.endDate,
            mealPreference: lunchMeal?.name ?? 'Lunch of the Day',
            selectedWeekdays: (widget.lunchSelectedWeekdays?.cast<int>()) ??
                (widget.isCustomPlan
                    ? widget.selectedWeekdays
                        .asMap()
                        .entries
                        .where((entry) => entry.value)
                        .map((entry) => entry.key + 1)
                        .toList()
                    : null),
          );
        }
      } else {
        // Standard single plan type
        String? mealPreference;
        if (planType == 'breakfast') {
          mealPreference = breakfastMeal?.name ?? 'Breakfast of the Day';
        } else if (planType == 'lunch') {
          mealPreference = lunchMeal?.name ?? 'Lunch of the Day';
        } else {
          // fallback for express or other
          mealPreference = widget.selectedMeals.isNotEmpty
              ? widget.selectedMeals.first.name
              : null;
        }
        success = await profileService.assignMealPlan(
          widget.breakfastPreOrderDate ??
              widget.lunchPreOrderDate ??
              widget.startDate,
          widget.selectedStudent.id,
          planType,
          widget.preOrderEndDate ?? widget.endDate,
          mealPreference: mealPreference,
          selectedWeekdays: (widget.breakfastSelectedWeekdays?.cast<int>()) ??
              (widget.lunchSelectedWeekdays?.cast<int>()) ??
              (widget.isCustomPlan
                  ? widget.selectedWeekdays
                      .asMap()
                      .entries
                      .where((entry) => entry.value)
                      .map((entry) => entry.key + 1)
                      .toList()
                  : null),
        );
      }

      // Close loading dialog
      Navigator.pop(context);

      if (success) {
        // Show success message dialog
        showDialog(
          context: context,
          barrierDismissible: false,
          builder: (context) => AlertDialog(
            title: Text(
              'Payment Successful',
              style: GoogleFonts.poppins(
                fontWeight: FontWeight.w600,
                color: Colors.green,
              ),
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Icon(
                  Icons.check_circle,
                  color: Colors.green,
                  size: 64,
                ),
                const SizedBox(height: 16),
                Text(
                  widget.isExpressOrder
                      ? 'Your express order has been placed successfully! Your meal will be delivered to \\${widget.selectedStudent.name} today.'
                      : (hasBothMealTypes || widget.mealType == 'both')
                          ? 'Your breakfast and lunch subscriptions have been activated! Meals will be delivered to \\${widget.selectedStudent.name} according to the schedule.'
                          : 'Your subscription has been activated! Meals will be delivered to \\${widget.selectedStudent.name} according to the schedule.',
                  style: GoogleFonts.poppins(
                    fontSize: 14,
                  ),
                ),
              ],
            ),
            actions: [
              SizedBox(
                width: double.infinity,
                child: GradientButton(
                  text: 'Manage Subscription',
                  isFullWidth: true,
                  onPressed: () {
                    Navigator.pop(context);
                    Navigator.pushAndRemoveUntil(
                      context,
                      MaterialPageRoute(
                        builder: (_) => MainScreen(
                          initialTabIndex: 2,
                        ),
                      ),
                      (route) => false,
                    );
                  },
                ),
              ),
            ],
          ),
        );

        // After successful payment, store the total for each plan
        final prefs = await SharedPreferences.getInstance();
        final studentId = widget.selectedStudent.id;
        final isBoth = (widget.mealType == 'both') ||
            (widget.breakfastPreOrderDate != null &&
                widget.lunchPreOrderDate != null);
        if (isBoth) {
          final double breakfastTotal = widget.totalAmount / 2;
          final double lunchTotal = widget.totalAmount / 2;
          await prefs.setDouble(
              'order_total_${studentId}_breakfast-$studentId', breakfastTotal);
          await prefs.setDouble(
              'order_total_${studentId}_lunch-$studentId', lunchTotal);
          // Store start/end/delivery for each plan
          await prefs.setString(
              'order_dates_${studentId}_breakfast-$studentId',
              jsonEncode({
                'startDate': (widget.breakfastPreOrderDate ?? widget.startDate)
                    .toIso8601String(),
                'endDate': (widget.preOrderEndDate ?? widget.endDate)
                    .toIso8601String(),
                'selectedWeekdays': _selectedWeekdaysToIndices(
                    widget.breakfastSelectedWeekdays ??
                        widget.selectedWeekdays),
              }));
          await prefs.setString(
              'order_dates_${studentId}_lunch-$studentId',
              jsonEncode({
                'startDate': (widget.lunchPreOrderDate ?? widget.startDate)
                    .toIso8601String(),
                'endDate': (widget.preOrderEndDate ?? widget.endDate)
                    .toIso8601String(),
                'selectedWeekdays': _selectedWeekdaysToIndices(
                    widget.lunchSelectedWeekdays ?? widget.selectedWeekdays),
              }));
        } else {
          String planId =
              (widget.planType == 'breakfast' || widget.mealType == 'breakfast')
                  ? 'breakfast-$studentId'
                  : 'lunch-$studentId';
          await prefs.setDouble(
              'order_total_${studentId}_$planId', widget.totalAmount);
          await prefs.setString(
              'order_dates_${studentId}_$planId',
              jsonEncode({
                'startDate': (widget.breakfastPreOrderDate ??
                        widget.lunchPreOrderDate ??
                        widget.startDate)
                    .toIso8601String(),
                'endDate': (widget.preOrderEndDate ?? widget.endDate)
                    .toIso8601String(),
                'selectedWeekdays': _selectedWeekdaysToIndices(
                    widget.breakfastSelectedWeekdays ??
                        widget.lunchSelectedWeekdays ??
                        widget.selectedWeekdays),
              }));
        }
      } else {
        ToastUtils.showToast(
          context: context,
          message:
              'There was an error processing your payment. Please try again.',
          type: ToastType.error,
        );
      }
    } catch (e) {
      Navigator.pop(context);
      ToastUtils.showToast(
        context: context,
        message: 'Payment error: $e',
        type: ToastType.error,
      );
    }
  }

  // Helper to convert List<bool>? to List<int> of selected weekday indices (1-based)
  List<int> _selectedWeekdaysToIndices(List<bool>? weekdays) {
    if (weekdays == null) return <int>[];
    return weekdays
        .asMap()
        .entries
        .where((e) => e.value)
        .map((e) => e.key + 1)
        .toList();
  }
}

class StartwellWalletDummyScreen extends StatefulWidget {
  final String planType;
  final bool isCustomPlan;
  final List<bool> selectedWeekdays;
  final DateTime startDate;
  final DateTime endDate;
  final List<DateTime> mealDates;
  final double totalAmount;
  final List<Meal> selectedMeals;
  final bool isExpressOrder;
  final Student selectedStudent;
  final String? mealType;
  final DateTime? breakfastPreOrderDate;
  final DateTime? lunchPreOrderDate;
  final String? breakfastDeliveryMode;
  final String? lunchDeliveryMode;
  final List<bool>? breakfastSelectedWeekdays;
  final List<bool>? lunchSelectedWeekdays;
  final DateTime? preOrderStartDate;
  final DateTime? preOrderEndDate;

  const StartwellWalletDummyScreen({
    Key? key,
    required this.planType,
    required this.isCustomPlan,
    required this.selectedWeekdays,
    required this.startDate,
    required this.endDate,
    required this.mealDates,
    required this.totalAmount,
    required this.selectedMeals,
    required this.isExpressOrder,
    required this.selectedStudent,
    this.mealType,
    this.breakfastPreOrderDate,
    this.lunchPreOrderDate,
    this.breakfastDeliveryMode,
    this.lunchDeliveryMode,
    this.breakfastSelectedWeekdays,
    this.lunchSelectedWeekdays,
    this.preOrderStartDate,
    this.preOrderEndDate,
  }) : super(key: key);

  @override
  State<StartwellWalletDummyScreen> createState() =>
      _StartwellWalletDummyScreenState();
}

class _StartwellWalletDummyScreenState
    extends State<StartwellWalletDummyScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: GradientAppBar(
        titleText: 'Startwell Wallet',
      ),
      body: Column(
        children: [
          Expanded(
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.account_balance_wallet,
                    size: 64,
                    color: AppTheme.purple,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Startwell Wallet Screen',
                    style: GoogleFonts.poppins(
                      fontSize: 24,
                      fontWeight: FontWeight.w600,
                      color: AppTheme.textDark,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'This is a dummy screen for Startwell Wallet payment',
                    style: GoogleFonts.poppins(
                      fontSize: 16,
                      color: AppTheme.textMedium,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 24),
                  Text(
                    'Amount to Pay: ₹${widget.totalAmount.toStringAsFixed(0)}',
                    style: GoogleFonts.poppins(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: AppTheme.purple,
                    ),
                  ),
                ],
              ),
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: SizedBox(
              width: double.infinity,
              height: 50,
              child: GradientButton(
                text: 'Pay Now',
                isFullWidth: true,
                onPressed: () => _processPayment(context),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _processPayment(BuildContext context) async {
    // Determine the meal plan type from the mealType parameter or from the selected meals
    final String planType = widget.mealType ??
        (widget.selectedMeals.first.categories.first == MealCategory.breakfast
            ? 'breakfast'
            : widget.selectedMeals.first.categories.first ==
                    MealCategory.expressOneDay
                ? 'express'
                : 'lunch');

    // Validate one last time
    final String? validationError =
        MealPlanValidator.validateMealPlan(widget.selectedStudent, planType);

    if (validationError != null) {
      ToastUtils.showToast(
        context: context,
        message: validationError,
        type: ToastType.error,
      );
      return;
    }

    // Show loading indicator
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const Center(
        child: CircularProgressIndicator(),
      ),
    );

    try {
      // Process payment (simulated for demo)
      await Future.delayed(const Duration(seconds: 2));

      // Extract correct meal for each plan
      Meal? breakfastMeal = widget.selectedMeals.firstWhereOrNull(
          (m) => m.categories.contains(MealCategory.breakfast));
      Meal? lunchMeal = widget.selectedMeals
          .firstWhereOrNull((m) => m.categories.contains(MealCategory.lunch));

      // Assign the meal plan to the student
      final StudentProfileService profileService = StudentProfileService();

      // If this is a breakfast or lunch plan (not express), use April 14, 2025 as start date
      DateTime actualStartDate = widget.startDate;
      // No longer override the start date - use the date selected by the user
      // if (planType == 'breakfast' || planType == 'lunch') {
      //   // Set standardized plan start date to April 14, 2025
      //   actualStartDate = DateTime(2025, 4, 14);
      // }

      log('[DEBUG] Using actual start date in payment screen: ${DateFormat('yyyy-MM-dd').format(actualStartDate)}');
      log('[DEBUG] Meal plan type: $planType');

      final success = await profileService.assignMealPlan(
        actualStartDate,
        widget.selectedStudent.id,
        planType,
        widget.endDate,
        mealPreference: breakfastMeal?.name ?? 'Breakfast of the Day',
        selectedWeekdays: (widget.breakfastSelectedWeekdays?.cast<int>()) ??
            (widget.lunchSelectedWeekdays?.cast<int>()) ??
            (widget.isCustomPlan
                ? widget.selectedWeekdays
                    .asMap()
                    .entries
                    .where((entry) => entry.value)
                    .map((entry) => entry.key + 1)
                    .toList()
                : null),
      );

      // Close loading dialog
      Navigator.pop(context);

      if (success) {
        // Show success message dialog
        showDialog(
          context: context,
          barrierDismissible: false,
          builder: (context) => AlertDialog(
            title: Text(
              'Payment Successful',
              style: GoogleFonts.poppins(
                fontWeight: FontWeight.w600,
                color: Colors.green,
              ),
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Icon(
                  Icons.check_circle,
                  color: Colors.green,
                  size: 64,
                ),
                const SizedBox(height: 16),
                Text(
                  widget.isExpressOrder
                      ? 'Your express order has been placed successfully! Your meal will be delivered to ${widget.selectedStudent.name} today.'
                      : 'Your subscription has been activated! Meals will be delivered to ${widget.selectedStudent.name} according to the schedule.',
                  style: GoogleFonts.poppins(
                    fontSize: 14,
                  ),
                ),
              ],
            ),
            actions: [
              SizedBox(
                width: double.infinity,
                child: GradientButton(
                  text: 'Manage Subscription',
                  isFullWidth: true,
                  onPressed: () {
                    // Close dialog
                    Navigator.pop(context);

                    // Store student profile before navigation
                    _storeStudentProfile(widget.selectedStudent);

                    // If this is a breakfast or lunch plan (not express), ensure startDate is April 14, 2025
                    DateTime actualStartDate = widget.startDate;
                    // No longer override start date - use what was selected by the user
                    // if (planType == 'breakfast' || planType == 'lunch') {
                    //   // Set standardized plan start date to April 14, 2025
                    //   actualStartDate = DateTime(2025, 4, 14);
                    // }

                    // Navigate directly to MySubscriptionScreen with Upcoming Meals tab (index 0)
                    Navigator.pushAndRemoveUntil(
                      context,
                      MaterialPageRoute(
                        builder: (_) => MySubscriptionScreen(
                          startDate: actualStartDate,
                          endDate: widget.endDate,
                          defaultTabIndex: 0,
                          selectedStudentId: widget.selectedStudent.id,
                        ),
                      ),
                      (route) => false, // Remove all previous routes
                    );
                  },
                ),
              ),
            ],
          ),
        );
      } else {
        ToastUtils.showToast(
          context: context,
          message:
              'There was an error processing your payment. Please try again.',
          type: ToastType.error,
        );
      }
    } catch (e) {
      // Close loading dialog
      Navigator.pop(context);

      ToastUtils.showToast(
        context: context,
        message: 'Payment error: $e',
        type: ToastType.error,
      );
    }
  }

  // Helper method to store student profile in SharedPreferences
  Future<void> _storeStudentProfile(Student student) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Store the student profile as JSON
      final key = 'student_profile_${student.id}';
      await prefs.setString(key, jsonEncode(student.toJson()));

      // Add student ID to the list of recently active students
      final activeStudentsKey = 'recently_active_students';
      List<String> activeStudents = [];

      if (prefs.containsKey(activeStudentsKey)) {
        final activeStudentsJson = prefs.getString(activeStudentsKey) ?? '[]';
        activeStudents = List<String>.from(jsonDecode(activeStudentsJson));
      }

      // Ensure this student is at the beginning of the list (most recent)
      activeStudents.remove(student.id); // Remove if exists
      activeStudents.insert(0, student.id); // Add to beginning

      // Keep only the 5 most recent students
      if (activeStudents.length > 5) {
        activeStudents = activeStudents.sublist(0, 5);
      }

      // Save the updated list
      await prefs.setString(activeStudentsKey, jsonEncode(activeStudents));

      log('Stored student profile with key: $key');
      log('Updated recently active students: $activeStudents');
    } catch (e) {
      log('Error storing student profile: $e');
    }
  }
}

class DummyPaymentSuccessScreen extends StatelessWidget {
  final String subscriptionId;

  const DummyPaymentSuccessScreen({
    Key? key,
    required this.subscriptionId,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: GradientAppBar(
        titleText: 'Payment Success',
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.check_circle,
              size: 100,
              color: Colors.green,
            ),
            const SizedBox(height: 24),
            Text(
              'Payment Successful!',
              style: GoogleFonts.poppins(
                fontSize: 24,
                fontWeight: FontWeight.w600,
                color: AppTheme.textDark,
              ),
            ),
            const SizedBox(height: 16),
            Text(
              'Your subscription is now active',
              style: GoogleFonts.poppins(
                fontSize: 16,
                color: AppTheme.textMedium,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Subscription ID: $subscriptionId',
              style: GoogleFonts.poppins(
                fontSize: 14,
                color: AppTheme.textMedium,
              ),
            ),
            const SizedBox(height: 32),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 24),
              child: GradientButton(
                text: 'Go to My Subscriptions',
                isFullWidth: true,
                onPressed: () {
                  Navigator.pushAndRemoveUntil(
                    context,
                    MaterialPageRoute(
                      builder: (_) => MainScreen(initialTabIndex: 2),
                    ),
                    (route) => false,
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}
