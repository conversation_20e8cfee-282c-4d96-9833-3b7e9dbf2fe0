import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:startwell/models/meal_model.dart';
import 'package:startwell/models/student_model.dart';
import 'package:startwell/models/user_profile.dart';
import 'package:startwell/models/customer_address_model.dart';
import 'package:startwell/screens/order_summary_screen.dart';
import 'package:startwell/screens/main_screen.dart';
import 'package:startwell/services/student_profile_service.dart';
import 'package:startwell/services/customer_address_service.dart';
import 'package:startwell/themes/app_theme.dart';
import 'package:startwell/utils/meal_plan_validator.dart';
import 'package:startwell/widgets/bottom_sheets/active_subscription_bottom_sheet.dart';
import 'package:startwell/widgets/common/info_banner.dart';
import 'package:intl/intl.dart';
import 'package:startwell/widgets/profile_avatar.dart';
import 'package:startwell/widgets/student/student_card_widget.dart';
import 'package:startwell/services/cart_storage_service.dart';
import 'package:startwell/services/meal_selection_manager.dart';
import 'package:startwell/utils/pre_order_date_calculator.dart';
import '../utils/toast_utils.dart';
import 'package:startwell/widgets/common/school_selector_dropdown.dart';
import 'package:startwell/screens/student_profile_form_screen.dart';

class ManageStudentProfileScreen extends StatefulWidget {
  final String? planType;
  final bool? isCustomPlan;
  final List<bool>? selectedWeekdays;
  final DateTime? startDate;
  final DateTime? endDate;
  final List<DateTime>? mealDates;
  final double? totalAmount;
  final List<Meal>? selectedMeals;
  final bool? isExpressOrder;
  final bool isManagementMode;
  final String? mealType;
  final UserProfile? userProfile;

  // Breakfast specific data
  final DateTime? breakfastStartDate;
  final DateTime? breakfastEndDate;
  final List<DateTime>? breakfastMealDates;
  final List<Meal>? breakfastSelectedMeals;
  final double? breakfastAmount;
  final String? breakfastPlanType;
  final List<bool>? breakfastSelectedWeekdays;
  final String? breakfastDeliveryMode;

  // Lunch specific data
  final DateTime? lunchStartDate;
  final DateTime? lunchEndDate;
  final List<DateTime>? lunchMealDates;
  final List<Meal>? lunchSelectedMeals;
  final double? lunchAmount;
  final String? lunchPlanType;
  final List<bool>? lunchSelectedWeekdays;
  final String? lunchDeliveryMode;

  const ManageStudentProfileScreen({
    Key? key,
    this.planType,
    this.isCustomPlan,
    this.selectedWeekdays,
    this.startDate,
    this.endDate,
    this.mealDates,
    this.totalAmount,
    this.selectedMeals,
    this.isExpressOrder,
    this.isManagementMode = false,
    this.mealType,
    this.userProfile,
    // New parameters for breakfast and lunch data
    this.breakfastStartDate,
    this.breakfastEndDate,
    this.breakfastMealDates,
    this.breakfastSelectedMeals,
    this.breakfastAmount,
    this.breakfastPlanType,
    this.breakfastSelectedWeekdays,
    this.breakfastDeliveryMode,
    this.lunchStartDate,
    this.lunchEndDate,
    this.lunchMealDates,
    this.lunchSelectedMeals,
    this.lunchAmount,
    this.lunchPlanType,
    this.lunchSelectedWeekdays,
    this.lunchDeliveryMode,
  }) : super(key: key);

  @override
  State<ManageStudentProfileScreen> createState() =>
      _ManageStudentProfileScreenState();
}

class _ManageStudentProfileScreenState
    extends State<ManageStudentProfileScreen> {
  // Use the StudentProfileService for persistence
  final StudentProfileService _profileService = StudentProfileService();
  
  // Customer Address Service for API integration
  final CustomerAddressService _customerAddressService = CustomerAddressService();

  // Student profiles list
  List<Student> _studentProfiles = [];

  // Currently selected student
  Student? _selectedStudent;

  // Loading state
  bool _isLoading = true;
  
  // API loading state
  bool _isLoadingFromAPI = false;
  String? _apiError;

  // Flag for testing - set to false to stop injecting a test student
  final bool _isTestingActiveSubscription = false;

  @override
  void initState() {
    super.initState();
    // Load student profiles from API on initialization
    _loadStudentProfilesFromAPI();
  }

  @override
  void dispose() {
    super.dispose();
  }

  // Method to refresh student profiles from API
  Future<void> _refreshStudentProfiles() async {
    if (!mounted) return;

    try {
      setState(() {
        _isLoadingFromAPI = true;
        _apiError = null;
      });

      final apiResponse = await _customerAddressService.getCustomerAddresses();
      
      if (apiResponse.success && apiResponse.data.isNotEmpty) {
        // Convert API data to Student objects
        final apiStudents = apiResponse.data.map((address) {
          return Student(
            id: address.pkCustomerAddressCode.toString(),
            name: address.childName,
            className: address.studentClass,
            division: address.division,
            floor: address.floor,
            schoolName: address.locationName,
            allergies: address.allergies.join(', '),
            grade: address.studentClass,
            section: address.division,
            profileImageUrl: '',
          );
        }).toList();

        if (!mounted) return;

        setState(() {
          _studentProfiles = apiStudents;
          _isLoadingFromAPI = false;
        });
        
        // Save API data to local storage
        await _profileService.saveStudentProfiles();
        
        ToastUtils.showToast(
          context: context,
          message: 'Student profiles refreshed successfully',
          type: ToastType.success,
        );
      } else {
        setState(() {
          _apiError = 'No data received from server';
          _isLoadingFromAPI = false;
        });
      }
    } catch (e) {
      if (!mounted) return;
      
      setState(() {
        _apiError = e.toString();
        _isLoadingFromAPI = false;
      });
      
      ToastUtils.showToast(
        context: context,
        message: 'Failed to refresh student profiles: $e',
        type: ToastType.error,
      );
    }
  }

  // Load student profiles from API on initialization
  Future<void> _loadStudentProfilesFromAPI() async {
    if (!mounted) return;

    try {
      setState(() {
        _isLoading = true;
        _isLoadingFromAPI = true;
        _apiError = null;
      });

      final apiResponse = await _customerAddressService.getCustomerAddresses();
      
      if (apiResponse.success && apiResponse.data.isNotEmpty) {
        // Convert API data to Student objects
        final apiStudents = apiResponse.data.map((address) {
          return Student(
            id: address.pkCustomerAddressCode.toString(),
            name: address.childName,
            className: address.studentClass,
            division: address.division,
            floor: address.floor,
            schoolName: address.locationName,
            allergies: address.allergies.join(', '),
            grade: address.studentClass,
            section: address.division,
            profileImageUrl: '',
          );
        }).toList();

        if (!mounted) return;

        setState(() {
          _studentProfiles = apiStudents;
          _isLoading = false;
          _isLoadingFromAPI = false;
        });
        
        // Save API data to local storage
        await _profileService.saveStudentProfiles();
        
        print('Student profiles loaded from API: ${_studentProfiles.length} students');
      } else {
        // If API fails, try to load from local storage as fallback
        await _loadStudentProfilesFromLocal();
        setState(() {
          _apiError = 'No data received from server';
          _isLoadingFromAPI = false;
        });
      }
    } catch (e) {
      if (!mounted) return;
      
      // If API fails, try to load from local storage as fallback
      await _loadStudentProfilesFromLocal();
      setState(() {
        _apiError = e.toString();
        _isLoadingFromAPI = false;
      });
      
      print('Error loading student profiles from API: $e');
    }
  }

  // Load student profiles from local storage as fallback
  Future<void> _loadStudentProfilesFromLocal() async {
    try {
      final profiles = await _profileService.getStudentProfiles();
      setState(() {
        _studentProfiles = profiles;
        _isLoading = false;
      });
      print('Student profiles loaded from local storage: ${_studentProfiles.length} students');
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      print('Error loading student profiles from local storage: $e');
    }
  }

  // Show form to create or edit a student profile
  void _showStudentForm({Student? student}) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => StudentProfileFormScreen(
          student: student,
          onSave: (formData) async {
            await _saveStudentProfile(formData);
          },
          onRefresh: _refreshStudentProfiles,
        ),
      ),
    );
  }

  // Save or update student profile
  Future<void> _saveStudentProfile(Map<String, dynamic> formData) async {
    print('Saving student profile...');
    print('formData: $formData');
    
    // Show loading state
    setState(() {
      _isLoading = true;
    });

    try {
      final isEditing = formData['isEditing'] as bool;
      
      if (isEditing) {
        // Update existing student via API
        final allergies = (formData['allergies'] as String).isNotEmpty
            ? (formData['allergies'] as String).split(',').map((e) => e.trim()).toList()
            : <String>[];

        final addressId = int.tryParse((formData['studentId'] ?? '').toString()) ?? 0;

        final response = await _customerAddressService.updateCustomerAddress(
          addressId: addressId,
          childName: (formData['studentName'] ?? '').toString(),
          studentClass: (formData['class'] ?? '').toString(),
          division: (formData['division'] ?? '').toString(),
          floor: (formData['floor'] ?? '').toString(),
          allergies: allergies,
          locationCode: _getLocationCodeFromSchoolName((formData['schoolName'] ?? '').toString()),
          isDefault: true, // or false as needed
        );

        print('Update student API response: $response');

        if (response['success'] == true) {
          await _refreshStudentProfiles();
          ToastUtils.showToast(
            context: context,
            message: 'Student profile updated successfully',
            type: ToastType.success,
          );
        } else {
          throw Exception('Failed to update student: ${response['message'] ?? 'Unknown error'}');
        }
      } else {
        // Create new student via API
        final allergies = (formData['allergies'] as String).isNotEmpty
            ? (formData['allergies'] as String).split(',').map((e) => e.trim()).toList()
            : <String>[];

        final response = await _customerAddressService.createCustomerAddress(
          childName: (formData['studentName'] ?? '').toString(),
          studentClass: (formData['class'] ?? '').toString(),
          division: (formData['division'] ?? '').toString(),
          floor: (formData['floor'] ?? '').toString(),
          allergies: allergies,
          locationCode: _getLocationCodeFromSchoolName((formData['schoolName'] ?? '').toString()),
        );

        print('Create student API response: $response');

        if (response['success'] == true) {
          // Refresh the student list from API
          await _refreshStudentProfiles();

          ToastUtils.showToast(
            context: context,
            message: 'Student profile created successfully',
            type: ToastType.success,
          );
        } else {
          throw Exception('Failed to create student: ${response['message'] ?? 'Unknown error'}');
        }
      }
    } catch (e) {
      print('Error saving student profile: $e');
      ToastUtils.showToast(
        context: context,
        message: 'Failed to save student profile: $e',
        type: ToastType.error,
      );
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  // Helper method to get location code from school name
  int _getLocationCodeFromSchoolName(String? schoolName) {
    if (schoolName == null || schoolName.isEmpty) {
      return 2; // Default location code
    }
    
    // This is a simple mapping - in a real app, you might want to store this mapping
    // or get it from the API response
    switch (schoolName.toLowerCase()) {
      case 'avalon heights international school, vashi':
        return 2;
      case 'billabong high international school, mulund':
        return 15;
      case 'bombay scottish school, powai':
        return 17;
      case 'dav public school, airoli':
        return 9;
      case 'gs shetty international school, bhandup':
        return 16;
      case 'new horizon world academy, koperkhairane':
        return 7;
      case 'north point school, koparkhairane':
        return 3;
      case 'oes international school, vashi':
        return 5;
      case 'orchids the international school, kurla':
        return 19;
      case 'orchids the international school, vikhroli':
        return 18;
      case 'st. mary\'s icse school, koperkhairane':
        return 6;
      case 'test school':
        return 24;
      case 'test school abc updated':
        return 20;
      case 'vpm\'s iam international school, airoli':
        return 25;
      default:
        return 2; // Default location code
    }
  }

  // Delete student profile (after confirmation)
  Future<void> _deleteStudentProfile(Student student) async {
    setState(() {
      _isLoading = true;
    });

    try {
      final addressId = int.tryParse(student.id) ?? 0;
      final response = await _customerAddressService.deleteCustomerAddress(addressId: addressId);

      print('Delete student API response: $response');

      if (response['success'] == true) {
        await _refreshStudentProfiles();
        ToastUtils.showToast(
          context: context,
          message: 'Student profile deleted successfully',
          type: ToastType.success,
        );
      } else {
        throw Exception('Failed to delete student: ${response['message'] ?? 'Unknown error'}');
      }
    } catch (e) {
      print('Error deleting student profile: $e');
      ToastUtils.showToast(
        context: context,
        message: 'Failed to delete student profile: $e',
        type: ToastType.error,
      );
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  // Show confirmation dialog for deleting a student
  void _showDeleteConfirmation(Student student) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        title: Text(
          'Delete Student Profile',
          style: GoogleFonts.poppins(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: AppTheme.textDark,
          ),
        ),
        content: Text(
          'Are you sure you want to delete ${student.name}\'s profile? This action cannot be undone.',
          style: GoogleFonts.poppins(
            fontSize: 14,
            color: AppTheme.textMedium,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(
              'Cancel',
              style: GoogleFonts.poppins(
                fontSize: 14,
                color: AppTheme.textMedium,
              ),
            ),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _deleteStudentProfile(student);
            },
            child: Text(
              'Delete',
              style: GoogleFonts.poppins(
                fontSize: 14,
                color: Colors.red,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Build the list of student profiles
  Widget _buildStudentsList() {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AppTheme.deepPurple.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section header
          Container(
            decoration: BoxDecoration(
              border: Border(
                left: BorderSide(color: AppTheme.deepPurple, width: 3),
              ),
            ),
            padding: const EdgeInsets.only(left: 10),
            margin: const EdgeInsets.only(bottom: 16, left: 4),
            child: Text(
              widget.isManagementMode ? 'Student Profiles' : 'Select a Student',
              style: GoogleFonts.poppins(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: AppTheme.textDark,
                letterSpacing: 0.3,
              ),
            ),
          ),

          // List of student profiles with animation
          ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: _studentProfiles.length,
            itemBuilder: (context, index) {
              final student = _studentProfiles[index];
              final isSelected = _selectedStudent?.id == student.id;

              return AnimatedContainer(
                duration: const Duration(milliseconds: 300),
                curve: Curves.easeInOut,
                margin: const EdgeInsets.only(bottom: 16),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: isSelected
                          ? AppTheme.purple.withOpacity(0.15)
                          : AppTheme.deepPurple.withOpacity(0.05),
                      blurRadius: isSelected ? 10 : 4,
                      spreadRadius: isSelected ? 1 : 0,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: StudentCardWidget(
                  student: student,
                  isSelected: isSelected,
                  onSelect: widget.isManagementMode ? null : _selectStudent,
                  onEdit: _showStudentForm,
                  onDelete: widget.isManagementMode
                      ? student.hasActivePlan
                          ? null
                          : _deleteStudentProfile
                      : null,
                  isManagementMode: widget.isManagementMode,
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  // Build API status indicator
  Widget _buildAPIStatusIndicator() {
    if (_isLoadingFromAPI) {
      return Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: AppTheme.purple.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: AppTheme.purple.withOpacity(0.3)),
        ),
        child: Row(
          children: [
            SizedBox(
              width: 16,
              height: 16,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(AppTheme.purple),
              ),
            ),
            const SizedBox(width: 12),
            Text(
              'Syncing with server...',
              style: GoogleFonts.poppins(
                fontSize: 14,
                color: AppTheme.purple,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      );
    }

    if (_apiError != null) {
      return Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.red.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.red.withOpacity(0.3)),
        ),
        child: Row(
          children: [
            Icon(
              Icons.error_outline,
              color: Colors.red,
              size: 16,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                'Using offline data (${_studentProfiles.length} students)',
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  color: Colors.red,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
            TextButton(
              onPressed: _refreshStudentProfiles,
              child: Text(
                'Retry',
                style: GoogleFonts.poppins(
                  color: AppTheme.purple,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
      );
    }

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.green.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.green.withOpacity(0.3)),
      ),
      child: Row(
        children: [
          Icon(
            Icons.check_circle_outline,
            color: Colors.green,
            size: 16,
          ),
          const SizedBox(width: 12),
          Text(
            'Synced with server (${_studentProfiles.length} students)',
            style: GoogleFonts.poppins(
              fontSize: 14,
              color: Colors.green,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  // Build empty state widget
  Widget _buildEmptyState() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [Colors.white, Color(0xFFF7F7F7)],
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AppTheme.deepPurple.withOpacity(0.08),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Color(0xFFEDE5FB),
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.person_outline,
              size: 48,
              color: Color(0xFF8B5CF6),
            ),
          ),
          const SizedBox(height: 24),
          Text(
            'No student profile found.',
            style: GoogleFonts.poppins(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: AppTheme.textDark,
            ),
          ),
          const SizedBox(height: 12),
          Text(
            widget.isManagementMode
                ? 'Create a profile to manage your students.'
                : 'Please create one to continue your subscription.',
            style: GoogleFonts.poppins(
              fontSize: 14,
              color: AppTheme.textMedium,
              height: 1.5,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 32),
          // SizedBox(
          //   width: 220,
          //   height: 50,
          //   child: ElevatedButton.icon(
          //     onPressed: () => _showStudentForm(),
          //     style: ElevatedButton.styleFrom(
          //       padding: EdgeInsets.zero,
          //       shape: RoundedRectangleBorder(
          //         borderRadius: BorderRadius.circular(24),
          //       ),
          //       elevation: 2,
          //     ),
          //     icon: Container(
          //       padding: const EdgeInsets.all(8),
          //       decoration: BoxDecoration(
          //         color: Colors.white.withOpacity(0.2),
          //         shape: BoxShape.circle,
          //       ),
          //       child: const Icon(
          //         Icons.add,
          //         color: Colors.white,
          //         size: 18,
          //       ),
          //     ),
          //   ),
          // ),
        ],
      ),
    );
  }

  // Select a student
  void _selectStudent(Student student) {
    if (widget.isManagementMode) {
      return;
    }
    HapticFeedback.lightImpact();

    // Set the selected student
    setState(() {
      _selectedStudent = student;
    });

    print('Selected student: ${student.name}');
    print('Has active breakfast: ${student.hasActiveBreakfast}');
    print('Has active lunch: ${student.hasActiveLunch}');

    // Check if student has active subscriptions
    bool hasActiveBreakfast =
        student.hasActiveBreakfast && student.breakfastPlanEndDate != null;
    bool hasActiveLunch =
        student.hasActiveLunch && student.lunchPlanEndDate != null;

    // Determine what meal types are being selected by the parent
    bool isSelectingBreakfast = widget.mealType == 'breakfast' ||
        widget.mealType == 'both' ||
        MealSelectionManager.hasBreakfastInCart;
    bool isSelectingLunch = widget.mealType == 'lunch' ||
        widget.mealType == 'both' ||
        MealSelectionManager.hasLunchInCart;
    bool isSelectingBoth = isSelectingBreakfast && isSelectingLunch;

    print('DEBUG: Selecting breakfast: $isSelectingBreakfast');
    print('DEBUG: Selecting lunch: $isSelectingLunch');
    print('DEBUG: Selecting both: $isSelectingBoth');
    print('DEBUG: Has active breakfast: $hasActiveBreakfast');
    print('DEBUG: Has active lunch: $hasActiveLunch');

    // Check which scenario applies
    bool shouldShowBottomSheet = false;

    // Always show the bottom sheet if there's an active subscription that matches
    // what the parent is trying to select
    if ((isSelectingBreakfast && hasActiveBreakfast) ||
        (isSelectingLunch && hasActiveLunch)) {
      shouldShowBottomSheet = true;
    }

    if (shouldShowBottomSheet) {
      // Student has relevant active subscriptions, show bottom sheet
      print('Student has relevant active subscriptions, showing bottom sheet');
      _showActiveSubscriptionsBottomSheet(student);
    } else {
      // Student has no relevant active subscriptions, redirect to order summary
      print(
        'Student has no relevant active subscriptions, redirecting to order summary',
      );
      _proceedToOrderSummary();
    }
  }

  // Show bottom sheet with active subscription information
  void _showActiveSubscriptionsBottomSheet(Student student) {
    // Determine what meal types are being selected by the parent
    bool isSelectingBreakfast = widget.mealType == 'breakfast' ||
        widget.mealType == 'both' ||
        MealSelectionManager.hasBreakfastInCart;
    bool isSelectingLunch = widget.mealType == 'lunch' ||
        widget.mealType == 'both' ||
        MealSelectionManager.hasLunchInCart;
    bool isSelectingBoth = isSelectingBreakfast && isSelectingLunch;

    // Check if student has active subscriptions
    bool hasActiveBreakfast =
        student.hasActiveBreakfast && student.breakfastPlanEndDate != null;
    bool hasActiveLunch =
        student.hasActiveLunch && student.lunchPlanEndDate != null;

    // Variables to track bottom sheet options
    bool showBreakfastPreorder = false;
    bool showLunchPreorder = false;
    String scenarioDescription = "";

    // Determine whether to show pre-order options for each meal type
    // Show breakfast pre-order if parent is selecting breakfast and student has active breakfast plan
    if (isSelectingBreakfast && hasActiveBreakfast) {
      showBreakfastPreorder = true;
    }

    // Show lunch pre-order if parent is selecting lunch and student has active lunch plan
    if (isSelectingLunch && hasActiveLunch) {
      showLunchPreorder = true;
    }

    // Set description based on what's being shown
    if (showBreakfastPreorder && showLunchPreorder) {
      scenarioDescription = "Pre-order available for both breakfast and lunch";
    } else if (showBreakfastPreorder) {
      scenarioDescription = "Pre-order available for breakfast only";
    } else if (showLunchPreorder) {
      scenarioDescription = "Pre-order available for lunch only";
    }

    print('DEBUG: ===== ACTIVE SUBSCRIPTION BOTTOM SHEET SETUP =====');
    print('DEBUG: Active scenario: $scenarioDescription');
    print('DEBUG: Show breakfast preorder: $showBreakfastPreorder');
    print('DEBUG: Show lunch preorder: $showLunchPreorder');

    // Get the proper weekday selections for each meal type
    List<bool>? breakfastSelectedWeekdays;
    List<bool>? lunchSelectedWeekdays;

    // For breakfast, use the specific breakfast weekdays if available
    if (widget.breakfastSelectedWeekdays != null) {
      // Create a completely new array instead of using List.from which can still cause issues
      breakfastSelectedWeekdays = List<bool>.filled(5, false);
      for (int i = 0;
          i < widget.breakfastSelectedWeekdays!.length && i < 5;
          i++) {
        breakfastSelectedWeekdays[i] = widget.breakfastSelectedWeekdays![i];
      }
      print(
          'DEBUG: Using breakfast-specific weekdays: $breakfastSelectedWeekdays');
    } else if (isSelectingBreakfast && widget.selectedWeekdays != null) {
      // Fallback to general weekdays for breakfast if no specific ones
      breakfastSelectedWeekdays = List<bool>.filled(5, false);
      for (int i = 0; i < widget.selectedWeekdays!.length && i < 5; i++) {
        breakfastSelectedWeekdays[i] = widget.selectedWeekdays![i];
      }
      print(
          'DEBUG: Using general weekdays for breakfast: $breakfastSelectedWeekdays');
    } else {
      // Default to Mon-Fri if no weekdays are provided
      breakfastSelectedWeekdays = List.generate(5, (_) => true);
      print('DEBUG: Using default Mon-Fri for breakfast');
    }

    // For lunch, use the specific lunch weekdays if available
    if (widget.lunchSelectedWeekdays != null) {
      // Create a completely new array instead of using List.from which can still cause issues
      lunchSelectedWeekdays = List<bool>.filled(5, false);
      for (int i = 0; i < widget.lunchSelectedWeekdays!.length && i < 5; i++) {
        lunchSelectedWeekdays[i] = widget.lunchSelectedWeekdays![i];
      }
      print('DEBUG: Using lunch-specific weekdays: $lunchSelectedWeekdays');
    } else if (isSelectingLunch && widget.selectedWeekdays != null) {
      // Fallback to general weekdays for lunch if no specific ones
      lunchSelectedWeekdays = List<bool>.filled(5, false);
      for (int i = 0; i < widget.selectedWeekdays!.length && i < 5; i++) {
        lunchSelectedWeekdays[i] = widget.selectedWeekdays![i];
      }
      print('DEBUG: Using general weekdays for lunch: $lunchSelectedWeekdays');
    } else {
      // Default to Mon-Fri if no weekdays are provided
      lunchSelectedWeekdays = List.generate(5, (_) => true);
      print('DEBUG: Using default Mon-Fri for lunch');
    }

    // Print delivery modes for debugging
    if (breakfastSelectedWeekdays != null) {
      String breakfastDeliveryMode =
          PreOrderDateCalculator.getDeliveryModeText(breakfastSelectedWeekdays);
      print(
          'DEBUG: Calculated breakfast delivery mode: $breakfastDeliveryMode');
    }
    if (lunchSelectedWeekdays != null) {
      String lunchDeliveryMode =
          PreOrderDateCalculator.getDeliveryModeText(lunchSelectedWeekdays);
      print('DEBUG: Calculated lunch delivery mode: $lunchDeliveryMode');
    }

    // Get plan types for both meal types
    String? breakfastPlanType = widget.breakfastPlanType ?? widget.planType;
    String? lunchPlanType = widget.lunchPlanType ?? widget.planType;

    print('DEBUG: ==== BEFORE LAUNCHING BOTTOM SHEET ====');
    print('DEBUG: Breakfast selected weekdays: $breakfastSelectedWeekdays');
    print('DEBUG: Lunch selected weekdays: $lunchSelectedWeekdays');
    // Make sure these arrays are different objects in memory
    print(
        'DEBUG: Are weekday arrays the same object? ${identical(breakfastSelectedWeekdays, lunchSelectedWeekdays)}');
    print('DEBUG: =====================================');

    // Show bottom sheet and track when it's closed
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(24)),
      ),
      builder: (context) => ActiveSubscriptionBottomSheet(
        student: student,
        showBreakfastPreorder: showBreakfastPreorder,
        showLunchPreorder: showLunchPreorder,
        // Pass plan types and independent delivery day arrays to bottom sheet
        breakfastPlanType: breakfastPlanType,
        lunchPlanType: lunchPlanType,
        breakfastSelectedWeekdays: breakfastSelectedWeekdays,
        lunchSelectedWeekdays: lunchSelectedWeekdays,
        onContinue: (
          DateTime? breakfastPreorderDate,
          DateTime? lunchPreorderDate,
          String? breakfastDeliveryMode,
          String? lunchDeliveryMode,
        ) {
          print('DEBUG: ===== RECEIVED FROM BOTTOM SHEET =====');
          print(
              'DEBUG: Selected breakfast preorder date: $breakfastPreorderDate');
          print('DEBUG: Selected lunch preorder date: $lunchPreorderDate');
          print('DEBUG: Final breakfast delivery mode: $breakfastDeliveryMode');
          print('DEBUG: Final lunch delivery mode: $lunchDeliveryMode');
          print('DEBUG: ======================================');

          // Navigate to order summary with pre-order dates and separate delivery modes
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => OrderSummaryScreen(
                planType: widget.planType ?? 'Single Day',
                isCustomPlan: widget.isCustomPlan ?? false,
                selectedWeekdays:
                    widget.selectedWeekdays ?? List.filled(5, false),
                startDate: widget.startDate ?? DateTime.now(),
                endDate: widget.endDate ?? DateTime.now(),
                mealDates: widget.mealDates ?? [],
                totalAmount: widget.totalAmount ?? 0.0,
                selectedMeals: widget.selectedMeals ?? [],
                isExpressOrder: widget.isExpressOrder ?? false,
                selectedStudent: _selectedStudent,
                mealType: widget.mealType,
                breakfastPreOrderDate: breakfastPreorderDate,
                lunchPreOrderDate: lunchPreorderDate,
                isPreOrder:
                    breakfastPreorderDate != null || lunchPreorderDate != null,
                // Pass specific delivery modes for each meal type
                breakfastDeliveryMode: breakfastDeliveryMode,
                lunchDeliveryMode: lunchDeliveryMode,
                // Pass breakfast and lunch specific data
                breakfastStartDate: widget.breakfastStartDate,
                breakfastEndDate: widget.breakfastEndDate,
                breakfastMealDates: widget.breakfastMealDates,
                breakfastSelectedMeals: widget.breakfastSelectedMeals,
                breakfastAmount: widget.breakfastAmount,
                breakfastPlanType: widget.breakfastPlanType,
                breakfastSelectedWeekdays: breakfastSelectedWeekdays,
                lunchStartDate: widget.lunchStartDate,
                lunchEndDate: widget.lunchEndDate,
                lunchMealDates: widget.lunchMealDates,
                lunchSelectedMeals: widget.lunchSelectedMeals,
                lunchAmount: widget.lunchAmount,
                lunchPlanType: widget.lunchPlanType,
                lunchSelectedWeekdays: lunchSelectedWeekdays,
              ),
            ),
          );
        },
      ),
    ).then((_) {
      // This runs when the bottom sheet is dismissed without pressing continue
      // Check if the user is still on this screen
      if (mounted) {
        // If the user navigated away (like to OrderSummaryScreen), they continued
        // If they didn't, then they dismissed the sheet without continuing
        // We can check for active routes, but a simpler approach:
        // If they're still on this screen and selected a student but didn't proceed,
        // then unselect the student
        if (_selectedStudent?.id == student.id) {
          print(
              'DEBUG: Bottom sheet dismissed without continuing - unselecting student');
          setState(() {
            _selectedStudent = null;
          });
        }
      }
    });
  }

  // Helper method to format dates
  String _formatDate(DateTime date) {
    return DateFormat('d MMM yyyy').format(date);
  }

  // Navigate to order summary screen with selected student
  void _proceedToOrderSummary() {
    // Check if a student is selected
    if (_selectedStudent == null) {
      ToastUtils.showToast(
        context: context,
        message: 'Please select a student to continue',
        type: ToastType.error,
      );
      return;
    }

    // Set meal selections based on mealType parameter
    if (widget.mealType == 'breakfast') {
      MealSelectionManager.hasBreakfastInCart = true;
      MealSelectionManager.hasLunchInCart = false;
    } else if (widget.mealType == 'lunch') {
      MealSelectionManager.hasBreakfastInCart = false;
      MealSelectionManager.hasLunchInCart = true;
    } else if (widget.mealType == 'both') {
      MealSelectionManager.hasBreakfastInCart = true;
      MealSelectionManager.hasLunchInCart = true;
    }

    // Check if student has active subscriptions
    bool hasActiveBreakfast = _selectedStudent!.hasActiveBreakfast &&
        _selectedStudent!.breakfastPlanEndDate != null;
    bool hasActiveLunch = _selectedStudent!.hasActiveLunch &&
        _selectedStudent!.lunchPlanEndDate != null;

    // Determine what meal types are being selected by the parent
    bool isSelectingBreakfast = widget.mealType == 'breakfast' ||
        widget.mealType == 'both' ||
        MealSelectionManager.hasBreakfastInCart;
    bool isSelectingLunch = widget.mealType == 'lunch' ||
        widget.mealType == 'both' ||
        MealSelectionManager.hasLunchInCart;

    // Debug info
    print('DEBUG: _proceedToOrderSummary - mealType: ${widget.mealType}');
    print(
        'DEBUG: _proceedToOrderSummary - isSelectingBreakfast: $isSelectingBreakfast');
    print(
        'DEBUG: _proceedToOrderSummary - isSelectingLunch: $isSelectingLunch');
    print(
        'DEBUG: _proceedToOrderSummary - hasActiveBreakfast: $hasActiveBreakfast');
    print('DEBUG: _proceedToOrderSummary - hasActiveLunch: $hasActiveLunch');

    // Log weekday selections for debugging
    if (widget.selectedWeekdays != null) {
      print(
          'DEBUG: _proceedToOrderSummary - selectedWeekdays: ${widget.selectedWeekdays}');
    }
    if (widget.breakfastSelectedWeekdays != null) {
      print(
          'DEBUG: _proceedToOrderSummary - breakfastSelectedWeekdays: ${widget.breakfastSelectedWeekdays}');
    }
    if (widget.lunchSelectedWeekdays != null) {
      print(
          'DEBUG: _proceedToOrderSummary - lunchSelectedWeekdays: ${widget.lunchSelectedWeekdays}');
    }

    if (widget.breakfastStartDate != null || widget.lunchStartDate != null) {
      print('DEBUG: Using separate date information for breakfast and lunch');
      print('DEBUG: breakfastStartDate: ${widget.breakfastStartDate}');
      print('DEBUG: breakfastEndDate: ${widget.breakfastEndDate}');
      print('DEBUG: lunchStartDate: ${widget.lunchStartDate}');
      print('DEBUG: lunchEndDate: ${widget.lunchEndDate}');
    }

    // Calculate delivery modes for breakfast and lunch if custom plan
    String? breakfastDeliveryMode = widget.breakfastDeliveryMode;
    String? lunchDeliveryMode = widget.lunchDeliveryMode;

    // Only calculate delivery modes if they weren't passed from Cart screen
    if ((breakfastDeliveryMode == null || lunchDeliveryMode == null) &&
        widget.isCustomPlan == true) {
      // For breakfast, use breakfast-specific weekdays if available, otherwise fallback to general
      if (widget.breakfastSelectedWeekdays != null &&
          isSelectingBreakfast &&
          breakfastDeliveryMode == null) {
        breakfastDeliveryMode = PreOrderDateCalculator.getDeliveryModeText(
          widget.breakfastSelectedWeekdays!,
        );
        print(
            'DEBUG: Using breakfast-specific weekdays for delivery mode: ${widget.breakfastSelectedWeekdays}');
      } else if (widget.selectedWeekdays != null &&
          isSelectingBreakfast &&
          breakfastDeliveryMode == null) {
        breakfastDeliveryMode = PreOrderDateCalculator.getDeliveryModeText(
          widget.selectedWeekdays!,
        );
        print(
            'DEBUG: Using general weekdays for breakfast delivery mode: ${widget.selectedWeekdays}');
      }

      // For lunch, use lunch-specific weekdays if available, otherwise fallback to general
      if (widget.lunchSelectedWeekdays != null &&
          isSelectingLunch &&
          lunchDeliveryMode == null) {
        lunchDeliveryMode = PreOrderDateCalculator.getDeliveryModeText(
          widget.lunchSelectedWeekdays!,
        );
        print(
            'DEBUG: Using lunch-specific weekdays for delivery mode: ${widget.lunchSelectedWeekdays}');
      } else if (widget.selectedWeekdays != null &&
          isSelectingLunch &&
          lunchDeliveryMode == null) {
        lunchDeliveryMode = PreOrderDateCalculator.getDeliveryModeText(
          widget.selectedWeekdays!,
        );
        print(
            'DEBUG: Using general weekdays for lunch delivery mode: ${widget.selectedWeekdays}');
      }
    } else if (breakfastDeliveryMode == null || lunchDeliveryMode == null) {
      // For regular plans, use Monday to Friday format if not passed from Cart
      breakfastDeliveryMode = breakfastDeliveryMode ?? "Monday to Friday";
      lunchDeliveryMode = lunchDeliveryMode ?? "Monday to Friday";
      print('DEBUG: Using Monday to Friday for regular plan delivery modes');
    }

    print('DEBUG: Final breakfast delivery mode: $breakfastDeliveryMode');
    print('DEBUG: Final lunch delivery mode: $lunchDeliveryMode');

    // Navigate directly to order summary
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => OrderSummaryScreen(
          planType: widget.planType ?? 'Single Day',
          isCustomPlan: widget.isCustomPlan ?? false,
          selectedWeekdays: widget.selectedWeekdays ?? List.filled(5, false),
          startDate: widget.startDate ?? DateTime.now(),
          endDate: widget.endDate ?? DateTime.now(),
          mealDates: widget.mealDates ?? [],
          totalAmount: widget.totalAmount ?? 0.0,
          selectedMeals: widget.selectedMeals ?? [],
          isExpressOrder: widget.isExpressOrder ?? false,
          selectedStudent: _selectedStudent,
          mealType: widget.mealType,
          breakfastPreOrderDate: null,
          lunchPreOrderDate: null,
          isPreOrder: false,
          // Pass specific delivery modes for each meal type
          breakfastDeliveryMode: breakfastDeliveryMode,
          lunchDeliveryMode: lunchDeliveryMode,
          // Pass breakfast and lunch specific data
          breakfastStartDate: widget.breakfastStartDate,
          breakfastEndDate: widget.breakfastEndDate,
          breakfastMealDates: widget.breakfastMealDates,
          breakfastSelectedMeals: widget.breakfastSelectedMeals,
          breakfastAmount: widget.breakfastAmount,
          breakfastPlanType: widget.breakfastPlanType,
          breakfastSelectedWeekdays: widget.breakfastSelectedWeekdays,
          lunchStartDate: widget.lunchStartDate,
          lunchEndDate: widget.lunchEndDate,
          lunchMealDates: widget.lunchMealDates,
          lunchSelectedMeals: widget.lunchSelectedMeals,
          lunchAmount: widget.lunchAmount,
          lunchPlanType: widget.lunchPlanType,
          lunchSelectedWeekdays: widget.lunchSelectedWeekdays,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final bool isExpressOrder = widget.isExpressOrder ?? false;
    final bool isWithinExpressWindow =
        isExpressOrder ? MealPlanValidator.isWithinExpressWindow() : true;

    return WillPopScope(
      onWillPop: () async {
        // Pop the current screen to return to the previous screen (Cart Screen)
        Navigator.pop(context);
        return false;
      },
      child: Scaffold(
        appBar: AppBar(
          title: Text(
            widget.isManagementMode ? 'Student Management' : 'Select Student Profile',
            style: GoogleFonts.poppins(
              fontSize: 20,
              fontWeight: FontWeight.w600,
              color: Colors.white,
            ),
          ),
          backgroundColor: Colors.transparent,
          flexibleSpace: Container(
            decoration: BoxDecoration(gradient: AppTheme.purpleToDeepPurple),
          ),
          elevation: 0,
          actions: [
            // Refresh button
            if (widget.isManagementMode)
              IconButton(
                icon: _isLoadingFromAPI
                    ? SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      )
                    : const Icon(Icons.refresh, color: Colors.white),
                onPressed: _isLoadingFromAPI ? null : _refreshStudentProfiles,
                tooltip: 'Refresh from API',
              ),
          ],
          leading: IconButton(
            icon: const Icon(Icons.arrow_back, color: Colors.white),
            onPressed: () async {
              // Store the current selection in cart before navigating back
              if (widget.selectedMeals?.isNotEmpty == true) {
                // First, load existing cart items
                final existingCartItems =
                    await CartStorageService.loadCartItems();

                // Create new cart item
                final cartItem = {
                  'planType': widget.planType,
                  'isCustomPlan': widget.isCustomPlan,
                  'selectedWeekdays': widget.selectedWeekdays,
                  'startDate': widget.startDate,
                  'endDate': widget.endDate,
                  'mealDates': widget.mealDates,
                  'totalAmount': widget.totalAmount,
                  'selectedMeals': widget.selectedMeals,
                  'isExpressOrder': widget.isExpressOrder,
                  'mealType': widget.mealType,
                };

                // Check if we already have this meal type in cart
                bool hasMealType = existingCartItems.any(
                  (item) => item['mealType'] == widget.mealType,
                );

                // If we don't have this meal type, add it to existing items
                if (!hasMealType) {
                  existingCartItems.add(cartItem);
                }

                // Save all cart items
                await CartStorageService.saveCartItems(existingCartItems);

                // Update meal selection manager
                if (widget.mealType == 'breakfast') {
                  MealSelectionManager.hasBreakfastInCart = true;
                } else if (widget.mealType == 'lunch') {
                  MealSelectionManager.hasLunchInCart = true;
                }
              }

              // Pop the current screen to return to the previous screen (Cart Screen)
              Navigator.pop(context);
            },
          ),
        ),
        body: _isLoading
            ? const Center(child: CircularProgressIndicator())
            : SafeArea(
                child: Column(
                  children: [
                    Expanded(
                      child: SingleChildScrollView(
                        physics: AlwaysScrollableScrollPhysics(),
                        padding: const EdgeInsets.fromLTRB(16, 16, 16, 24),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Express order window banner removed
                            if (isExpressOrder) const SizedBox(height: 16),

                            // Instructions banner removed
                            const SizedBox(height: 24),

                            // API Status indicator (only in management mode)
                            if (widget.isManagementMode) ...[
                              _buildAPIStatusIndicator(),
                              const SizedBox(height: 16),
                            ],

                            // Student profiles list
                            _studentProfiles.isEmpty
                                ? _buildEmptyState()
                                : _buildStudentsList(),
                          ],
                        ),
                      ),
                    ),

                    // Bottom buttons in a container with shadow
                    Container(
                      decoration: BoxDecoration(
                        color: Colors.white,
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.05),
                            blurRadius: 10,
                            offset: const Offset(0, -4),
                          ),
                        ],
                      ),
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          // Create new profile button
                          SizedBox(
                            width: double.infinity,
                            height: 60,
                            child: ElevatedButton.icon(
                              onPressed: () => _showStudentForm(),
                              style: ElevatedButton.styleFrom(
                                foregroundColor: AppTheme.purple,
                                backgroundColor: Colors.white,
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(50),
                                  side: BorderSide(color: AppTheme.purple),
                                ),
                                elevation: 0,
                              ),
                              icon: Container(
                                //padding: const EdgeInsets.all(1),
                                decoration: BoxDecoration(
                                  color: AppTheme.purple.withOpacity(0.1),
                                  shape: BoxShape.circle,
                                ),
                                child: const Icon(Icons.add, size: 16),
                              ),
                              label: Text(
                                'Create New Profile',
                                style: GoogleFonts.poppins(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                          ),

                          // Continue button (only shown in selection mode)
                          if (!widget.isManagementMode) ...[
                            const SizedBox(height: 16),
                            Container(
                              width: double.infinity,
                              height: 60,
                              decoration: BoxDecoration(
                                gradient: isExpressOrder
                                    ? LinearGradient(
                                        colors: [
                                          Colors.purple,
                                          Colors.deepPurple.shade500,
                                        ],
                                        begin: Alignment.topLeft,
                                        end: Alignment.bottomRight,
                                      )
                                    : AppTheme.purpleToDeepPurple,
                                borderRadius: BorderRadius.circular(50),
                              ),
                              child: ElevatedButton(
                                onPressed:
                                    (!isExpressOrder || isWithinExpressWindow)
                                        ? _proceedToOrderSummary
                                        : null,
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: Colors.transparent,
                                  shadowColor: Colors.transparent,
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(50),
                                  ),
                                  padding: EdgeInsets.zero,
                                ),
                                child: Padding(
                                  padding:
                                      const EdgeInsets.symmetric(vertical: 16),
                                  child: Text(
                                    'Continue',
                                    style: GoogleFonts.poppins(
                                      fontSize: 16,
                                      fontWeight: FontWeight.w600,
                                      color: Colors.white,
                                    ),
                                  ),
                                ),
                              ),
                            ),

                            // Warning for closed express window
                            if (isExpressOrder && !isWithinExpressWindow) ...[
                              const SizedBox(height: 8),
                              Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(
                                    Icons.info_outline,
                                    size: 16,
                                    color: Colors.red,
                                  ),
                                  const SizedBox(width: 8),
                                  Text(
                                    'Express ordering is currently unavailable',
                                    style: GoogleFonts.poppins(
                                      fontSize: 14,
                                      color: Colors.red,
                                      fontStyle: FontStyle.italic,
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ],
                        ],
                      ),
                    ),
                  ],
                ),
              ),
      ),
    );
  }
}


