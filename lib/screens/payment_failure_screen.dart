import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class PaymentFailureScreen extends StatelessWidget {
  final String? errorMessage;
  final VoidCallback? onRetry;

  const PaymentFailureScreen({Key? key, this.errorMessage, this.onRetry}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        automaticallyImplyLeading: false,
        title: Text('Payment Failed', style: GoogleFonts.poppins()),
        backgroundColor: Colors.red,
      ),
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.error, color: Colors.red, size: 80),
              SizedBox(height: 24),
              Text(
                'Payment could not be completed.',
                style: GoogleFonts.poppins(fontSize: 20, fontWeight: FontWeight.w600),
                textAlign: TextAlign.center,
              ),
              if (errorMessage != null) ...[
                SizedBox(height: 16),
                Text(errorMessage!, style: GoogleFonts.poppins(fontSize: 16, color: Colors.red[700]), textAlign: TextAlign.center),
              ],
              SizedBox(height: 32),
              ElevatedButton(
                onPressed: onRetry ?? () => Navigator.pop(context),
                style: ElevatedButton.styleFrom(backgroundColor: Colors.orange),
                child: Text('Retry Payment', style: GoogleFonts.poppins()),
              ),
              SizedBox(height: 12),
              TextButton(
                onPressed: () {
                  // TODO: Implement contact support (e.g., open support chat or email)
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('Contact <NAME_EMAIL>'),
                      backgroundColor: Colors.purple,
                    ),
                  );
                },
                child: Text('Contact Support', style: GoogleFonts.poppins()),
              ),
            ],
          ),
        ),
      ),
    );
  }
} 