import 'package:flutter/material.dart' hide MenuController;
import 'package:get/get.dart';
import '../controllers/menu_controller.dart';
import 'package:startwell/themes/app_theme.dart';
import 'package:google_fonts/google_fonts.dart';

class MenuView extends GetView<MenuController> {
  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final isSmallScreen = screenSize.width < 360;
    final isTablet = screenSize.width >= 768;
    final orange = AppTheme.orange;

    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Weekly Menu',
          style: GoogleFonts.poppins(
            fontSize: isTablet ? 18 : 16,
            fontWeight: FontWeight.w600,
            color: Colors.white,
          ),
        ),
        backgroundColor: Colors.transparent,
        flexibleSpace: Container(
          decoration: BoxDecoration(gradient: AppTheme.purpleToDeepPurple),
        ),
        elevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: Colors.white,
            size: 24,
          ),
          onPressed: () => Navigator.of(context).pop(),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh, color: Colors.white),
            onPressed: () => controller.loadWeeklyMenu(),
          ),
        ],
      ),
      backgroundColor: AppTheme.white,
      body: Obx(() {
        if (controller.isLoading.value) {
          return const Center(child: CircularProgressIndicator());
        }
        
        if (controller.error.value != null) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text('Error: ${controller.error.value}'),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: () => controller.loadWeeklyMenu(),
                  child: const Text('Retry'),
                ),
              ],
            ),
          );
        }

        final days = controller.getDays();
        final breakfastMenu = controller.getBreakfastMenu();
        final lunchMenu = controller.getLunchMenu();
        final jainMenu = controller.getJainMenu();

        Widget contentWidget = SingleChildScrollView(
          padding: EdgeInsets.symmetric(
            horizontal: isSmallScreen
                ? 12
                : isTablet
                    ? 24
                    : 20,
            vertical: isSmallScreen
                ? 16
                : isTablet
                    ? 28
                    : 24,
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Date display
              Container(
                margin: EdgeInsets.only(bottom: isSmallScreen ? 16 : 24),
                padding: EdgeInsets.symmetric(
                  horizontal: isSmallScreen ? 12 : 16,
                  vertical: isSmallScreen ? 8 : 10,
                ),
                decoration: BoxDecoration(
                  color: AppTheme.purple.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: AppTheme.purple.withOpacity(0.2),
                    width: 1.5,
                  ),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.calendar_today_rounded,
                      size: isSmallScreen
                          ? 16
                          : isTablet
                              ? 22
                              : 20,
                      color: AppTheme.purple,
                    ),
                    SizedBox(width: isSmallScreen ? 8 : 10),
                    Text(
                      controller.getDateRange(),
                      style: GoogleFonts.poppins(
                        fontSize: isSmallScreen
                            ? 12
                            : isTablet
                                ? 16
                                : 14,
                        fontWeight: FontWeight.w600,
                        color: AppTheme.purple,
                      ),
                    ),
                  ],
                ),
              ),
              // Breakfast Section
              Container(
                margin: const EdgeInsets.only(bottom: 32),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Section header
                    Container(
                      margin: const EdgeInsets.only(bottom: 16),
                      decoration: BoxDecoration(
                        border: Border(
                          left: BorderSide(
                            color: AppTheme.purple,
                            width: 4,
                          ),
                        ),
                      ),
                      padding: const EdgeInsets.only(left: 12),
                      child: Row(
                        children: [
                          Icon(
                            Icons.ramen_dining,
                            color: Colors.pink,
                            size: 24,
                          ),
                          const SizedBox(width: 12),
                          Text(
                            'Breakfast',
                            style: GoogleFonts.poppins(
                              fontSize: 18,
                              fontWeight: FontWeight.w600,
                              color: AppTheme.textDark,
                              letterSpacing: 0.3,
                            ),
                          ),
                        ],
                      ),
                    ),
                    _EnhancedMenuTable(
                      days: days,
                      sectionTitles: controller.getBreakfastSectionTitles(),
                      menu: breakfastMenu,
                      highlightColor: orange,
                    ),
                  ],
                ),
              ),
              // Lunch Section
              Container(
                margin: const EdgeInsets.only(bottom: 32),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Section header
                    Container(
                      margin: const EdgeInsets.only(bottom: 16),
                      decoration: BoxDecoration(
                        border: Border(
                          left: BorderSide(
                            color: AppTheme.purple,
                            width: 4,
                          ),
                        ),
                      ),
                      padding: const EdgeInsets.only(left: 12),
                      child: Row(
                        children: [
                          Icon(
                            Icons.flatware_rounded,
                            color: Colors.green,
                            size: 24,
                          ),
                          const SizedBox(width: 12),
                          Text(
                            'Lunch',
                            style: GoogleFonts.poppins(
                              fontSize: 18,
                              fontWeight: FontWeight.w600,
                              color: AppTheme.textDark,
                              letterSpacing: 0.3,
                            ),
                          ),
                        ],
                      ),
                    ),
                    _EnhancedMenuTable(
                      days: days,
                      sectionTitles: controller.getLunchSectionTitles(),
                      menu: lunchMenu,
                      highlightColor: orange,
                    ),
                  ],
                ),
              ),
              // Jain Menu Section
              Container(
                margin: const EdgeInsets.only(bottom: 32),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Section header
                    Container(
                      margin: const EdgeInsets.only(bottom: 16),
                      decoration: BoxDecoration(
                        border: Border(
                          left: BorderSide(
                            color: AppTheme.purple,
                            width: 4,
                          ),
                        ),
                      ),
                      padding: const EdgeInsets.only(left: 12),
                      child: Row(
                        children: [
                          Icon(
                            Icons.spa_outlined,
                            color: Colors.deepPurple,
                            size: 24,
                          ),
                          const SizedBox(width: 12),
                          Text(
                            'Jain Menu',
                            style: GoogleFonts.poppins(
                              fontSize: 20,
                              fontWeight: FontWeight.w600,
                              color: AppTheme.textDark,
                              letterSpacing: 0.3,
                            ),
                          ),
                        ],
                      ),
                    ),
                    _EnhancedMenuTable(
                      days: days,
                      sectionTitles: controller.getJainSectionTitles(),
                      menu: jainMenu,
                      highlightColor: AppTheme.deepPurple,
                    ),
                  ],
                ),
              ),
            ],
          ),
        );

        return contentWidget;
      }),
    );
  }
}

class _EnhancedMenuTable extends StatelessWidget {
  final List<String> days;
  final List<String> sectionTitles;
  final List<List<MenuDisplayItem>> menu;
  final Color highlightColor;
  const _EnhancedMenuTable({
    required this.days,
    required this.sectionTitles,
    required this.menu,
    required this.highlightColor,
  });

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final isSmallScreen = screenSize.width < 360;
    final isTablet = screenSize.width >= 768;

    final cellTextStyle = GoogleFonts.poppins(
      fontSize: isSmallScreen
          ? 10
          : isTablet
              ? 14
              : 12,
      color: AppTheme.textDark,
      height: 1.3,
    );
    final headerTextStyle = GoogleFonts.poppins(
      fontWeight: FontWeight.bold,
      color: AppTheme.purple,
      fontSize: isSmallScreen
          ? 11
          : isTablet
              ? 15
              : 13,
    );

    // Calculate responsive dimensions
    final dayColumnWidth = isSmallScreen
        ? 80.0
        : isTablet
            ? 120.0
            : 100.0;
    final sectionWidth = isSmallScreen
        ? 180.0
        : isTablet
            ? 260.0
            : 200.0;
    final rowHeight = isSmallScreen
        ? 56.0
        : isTablet
            ? 80.0
            : 64.0;
    final headerHeight = isSmallScreen
        ? 45.0
        : isTablet
            ? 60.0
            : 53.0;

    return LayoutBuilder(
      builder: (context, constraints) {
        // Calculate the max height needed for the header row
        final testTextStyle = headerTextStyle;
        final testTitles = ['Day', ...sectionTitles];
        final testWidths = [dayColumnWidth, ...List.filled(sectionTitles.length, sectionWidth)];
        final testHeights = testTitles.asMap().entries.map((entry) {
          final text = entry.value;
          final width = testWidths[entry.key] - 16; // account for padding
          final span = TextSpan(text: text, style: testTextStyle);
          final tp = TextPainter(text: span, maxLines: null, textDirection: TextDirection.ltr);
          tp.layout(maxWidth: width);
          return tp.height + 24; // add vertical padding
        }).toList();
        final maxHeaderHeight = testHeights.reduce((a, b) => a > b ? a : b);

        return Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.05),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Row(
            children: [
              // Fixed Day Column
              Container(
                width: dayColumnWidth,
                decoration: BoxDecoration(
                  border: Border(
                    right: BorderSide(color: Colors.grey.shade200),
                  ),
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(12),
                    bottomLeft: Radius.circular(12),
                  ),
                ),
                child: Column(
                  children: [
                    // Day header
                    Container(
                      height: maxHeaderHeight,
                      decoration: BoxDecoration(
                        color: AppTheme.purple.withOpacity(0.05),
                        border: Border(
                          bottom: BorderSide(color: Colors.grey.shade200),
                        ),
                        borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(12),
                        ),
                      ),
                      alignment: Alignment.center,
                      child: Text(
                        'Day',
                        style: headerTextStyle,
                        textAlign: TextAlign.center,
                        maxLines: null,
                        overflow: TextOverflow.visible,
                      ),
                    ),
                    // Days list
                    ...List.generate(days.length, (dayIndex) {
                      return Container(
                        height: rowHeight,
                        decoration: BoxDecoration(
                          border: Border(
                            bottom: BorderSide(
                              color: Colors.grey.shade200,
                              width: dayIndex < days.length - 1 ? 1 : 0,
                            ),
                          ),
                        ),
                        alignment: Alignment.center,
                        child: Text(
                          days[dayIndex],
                          style:
                              cellTextStyle.copyWith(fontWeight: FontWeight.w600),
                          textAlign: TextAlign.center,
                        ),
                      );
                    }),
                  ],
                ),
              ),

              // Scrollable Menu Types
              Expanded(
                child: SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: Container(
                    width: sectionTitles.length * sectionWidth,
                    child: Column(
                      children: [
                        // Menu types header
                        Container(
                          height: maxHeaderHeight,
                          decoration: BoxDecoration(
                            color: AppTheme.purple.withOpacity(0.05),
                            border: Border(
                              bottom: BorderSide(color: Colors.grey.shade200),
                            ),
                            borderRadius: BorderRadius.only(
                              topRight: Radius.circular(12),
                            ),
                          ),
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: sectionTitles
                                .asMap()
                                .entries
                                .map((entry) {
                              final index = entry.key;
                              final title = entry.value;
                              final isRecommended = title.contains('Day') || title.contains('Recommended');
                              return Container(
                                width: sectionWidth,
                                padding: EdgeInsets.symmetric(
                                  vertical: isSmallScreen ? 12 : 16,
                                  horizontal: isSmallScreen ? 6 : 8,
                                ),
                                decoration: isRecommended ? BoxDecoration(
                                  color: highlightColor.withOpacity(0.1),
                                  border: Border(
                                    top: BorderSide(color: highlightColor, width: 3),
                                  ),
                                ) : null,
                                alignment: Alignment.center,
                                child: Text(
                                  title,
                                  style: headerTextStyle.copyWith(
                                    color: isRecommended ? highlightColor : AppTheme.purple,
                                    fontWeight: isRecommended ? FontWeight.w700 : FontWeight.bold,
                                  ),
                                  textAlign: TextAlign.center,
                                  maxLines: null,
                                  overflow: TextOverflow.visible,
                                ),
                              );
                            })
                                .toList(),
                          ),
                        ),

                        // Menu items grid
                        ...List.generate(days.length, (dayIndex) {
                          return Container(
                            height: rowHeight,
                            decoration: BoxDecoration(
                              border: Border(
                                bottom: BorderSide(
                                  color: Colors.grey.shade200,
                                  width: dayIndex < days.length - 1 ? 1 : 0,
                                ),
                              ),
                            ),
                            child: Row(
                              children: List.generate(sectionTitles.length,
                                  (sectionIndex) {
                                final item = menu[dayIndex][sectionIndex];
                                final isRecommended = sectionTitles[sectionIndex].contains('Day') || 
                                                      sectionTitles[sectionIndex].contains('Recommended');
                                
                                return Container(
                                  width: sectionWidth,
                                  padding: EdgeInsets.symmetric(
                                    vertical: isSmallScreen ? 8 : 12,
                                    horizontal: isSmallScreen ? 6 : 8,
                                  ),
                                  decoration: isRecommended ? BoxDecoration(
                                    color: highlightColor.withOpacity(0.05),
                                    border: Border(
                                      left: BorderSide(color: highlightColor.withOpacity(0.3), width: 2),
                                    ),
                                  ) : null,
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      Expanded(
                                        child: Text(
                                          item.name,
                                          style: cellTextStyle.copyWith(
                                            fontWeight: isRecommended
                                                ? FontWeight.w600
                                                : FontWeight.normal,
                                            color: isRecommended
                                                ? highlightColor
                                                : AppTheme.textDark,
                                          ),
                                          textAlign: TextAlign.center,
                                          maxLines: null,
                                          overflow: TextOverflow.visible,
                                        ),
                                      ),
                                    ],
                                  ),
                                );
                              }),
                            ),
                          );
                        }),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }
} 