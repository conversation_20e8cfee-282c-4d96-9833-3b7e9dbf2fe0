import 'package:get/get.dart';
import '../../weekly_planner/data/providers/weekly_planner_repo.dart';
import '../../weekly_planner/data/models/planner_response.dart';
import '../../weekly_planner/data/models/meal.dart';
import 'package:intl/intl.dart';

class MenuController extends GetxController {
  final WeeklyPlannerRepo repo;
  MenuController(this.repo);

  final planner = Rxn<PlannerResponse>();
  final isLoading = false.obs;
  final error = RxnString();

  @override
  void onInit() {
    super.onInit();
    loadWeeklyMenu();
  }

  Future<void> loadWeeklyMenu() async {
    try {
      isLoading.value = true;
      error.value = null;
      final result = await repo.getPlanner();
      print('MenuController: API response parsed:');
      print('Week dates: ${result.weekDates.length}');
      print('Meal planner keys: ${result.mealPlanner.keys.toList()}');
      planner.value = result;
      print('MenuController: planner.value set.');
      
      // Debug breakfast menu details
      final breakfastMenu = getBreakfastMenu();
      print('Breakfast menu details:');
      for (int i = 0; i < breakfastMenu.length; i++) {
        print('Day $i: ${breakfastMenu[i].map((item) => '${item.name} (${item.isMealOfDay ? "Recommended" : "Regular"})').toList()}');
      }
      
      // Debug lunch menu details
      final lunchMenu = getLunchMenu();
      print('Lunch menu details:');
      for (int i = 0; i < lunchMenu.length; i++) {
        print('Day $i: ${lunchMenu[i].map((item) => '${item.name} (${item.isMealOfDay ? "Recommended" : "Regular"})').toList()}');
      }
      
      // Debug jain menu details
      final jainMenu = getJainMenu();
      print('Jain menu details:');
      for (int i = 0; i < jainMenu.length; i++) {
        print('Day $i: ${jainMenu[i].map((item) => '${item.name} (${item.isMealOfDay ? "Recommended" : "Regular"})').toList()}');
      }
    } catch (e) {
      error.value = e.toString();
      print('MenuController: Error: $e');
    } finally {
      isLoading.value = false;
    }
  }

  // Get formatted date range for display
  String getDateRange() {
    final plan = planner.value;
    if (plan == null || plan.weekDates.isEmpty) return 'Loading...';
    
    final firstDate = plan.weekDates.first.date;
    final lastDate = plan.weekDates.last.date;
    
    return '${DateFormat('dd').format(firstDate)}${_getDaySuffix(firstDate.day)} to ${DateFormat('dd').format(lastDate)}${_getDaySuffix(lastDate.day)} ${DateFormat('MMMM').format(firstDate)}';
  }

  String _getDaySuffix(int day) {
    if (day >= 11 && day <= 13) return 'th';
    switch (day % 10) {
      case 1: return 'st';
      case 2: return 'nd';
      case 3: return 'rd';
      default: return 'th';
    }
  }

  // Get day abbreviations
  List<String> getDays() {
    final plan = planner.value;
    if (plan == null) return ['Mon', 'Tue', 'Wed', 'Thu', 'Fri'];
    
    return plan.weekDates.map((date) => 
      DateFormat('EEE').format(date.date).substring(0, 3)
    ).toList();
  }

  // Get breakfast menu data
  List<List<MenuDisplayItem>> getBreakfastMenu() {
    return _getMenuForCategory('breakfast');
  }

  // Get lunch menu data
  List<List<MenuDisplayItem>> getLunchMenu() {
    return _getMenuForCategory('lunch');
  }

  // Get Jain menu data
  List<List<MenuDisplayItem>> getJainMenu() {
    return _getMenuForCategory('jain');
  }

  List<List<MenuDisplayItem>> _getMenuForCategory(String category) {
    final plan = planner.value;
    if (plan == null) return _getDefaultMenu();

    final List<List<MenuDisplayItem>> menu = [];
    
    for (final weekDate in plan.weekDates) {
      final dateKey = DateFormat('yyyy-MM-dd').format(weekDate.date);
      final meals = plan.mealPlanner[category]?[dateKey] ?? [];
      
      final List<MenuDisplayItem> dayMenu = [];
      
      // Find meals by type
      Meal? indianMeal;
      Meal? internationalMeal;
      Meal? recommendedMeal;
      
      for (final meal in meals) {
        if (meal.mealName.contains('Recommended')) {
          recommendedMeal = meal;
        } else if (meal.mealName.contains('Indian') || (category == 'jain' && meal.mealName.contains('Jain') && meal.mealName.contains('Breakfast'))) {
          indianMeal = meal;
        } else if (meal.mealName.contains('International') || (category == 'jain' && meal.mealName.contains('Jain') && meal.mealName.contains('Lunch'))) {
          internationalMeal = meal;
        }
      }
      
      // Get Indian meal items (comma separated) - remove "Jain:" prefix
      String indianMealItems = 'Loading...';
      if (indianMeal != null && indianMeal.items.isNotEmpty) {
        indianMealItems = indianMeal.items.map((item) => item.itemName.replaceAll('Jain: ', '')).join(', ');
      }
      
      // Get International meal items (comma separated) - remove "Jain:" prefix
      String internationalMealItems = 'Loading...';
      if (internationalMeal != null && internationalMeal.items.isNotEmpty) {
        internationalMealItems = internationalMeal.items.map((item) => item.itemName.replaceAll('Jain: ', '')).join(', ');
      }
      
      // Get recommended meal items (comma separated) - remove "Jain:" prefix
      String recommendedMealItems = 'Loading...';
      if (recommendedMeal != null && recommendedMeal.items.isNotEmpty) {
        recommendedMealItems = recommendedMeal.items.map((item) => item.itemName.replaceAll('Jain: ', '')).join(', ');
      }
      
      dayMenu.add(MenuDisplayItem(
        name: indianMealItems,
        isMealOfDay: false,
      ));
      
      dayMenu.add(MenuDisplayItem(
        name: internationalMealItems,
        isMealOfDay: false,
      ));
      
      dayMenu.add(MenuDisplayItem(
        name: recommendedMealItems,
        isMealOfDay: true,
      ));
      
      menu.add(dayMenu);
    }
    
    return menu;
  }

  // Get the recommended meal name for display
  String getRecommendedMealName(String category) {
    final plan = planner.value;
    if (plan == null) return 'Loading...';
    
    // Get the first day's recommended meal to extract the name
    if (plan.weekDates.isNotEmpty) {
      final firstDate = plan.weekDates.first.date;
      final dateKey = DateFormat('yyyy-MM-dd').format(firstDate);
      final meals = plan.mealPlanner[category]?[dateKey] ?? [];
      
      for (final meal in meals) {
        if (meal.mealName.contains('Recommended')) {
          return meal.mealName;
        }
      }
    }
    
    // Fallback names
    switch (category) {
      case 'breakfast':
        return 'Breakfast of the Day (Recommended)';
      case 'lunch':
        return 'Lunch of the Day (Recommended)';
      case 'jain':
        return 'Recommended';
      default:
        return 'Recommended';
    }
  }

  // Get dynamic section titles from API data
  List<String> getBreakfastSectionTitles() {
    // Always return all three section titles for breakfast, with 'Breakfast of the Day' first
    return ['Breakfast of the Day (Recommended)', 'Indian Breakfast', 'International Breakfast'];
  }

  List<String> getLunchSectionTitles() {
    final plan = planner.value;
    if (plan == null) return ['Lunch of the Day (Recommended)', 'Indian Lunch', 'International Lunch'];
    
    // Get the first day's meals to extract section titles
    if (plan.weekDates.isNotEmpty) {
      final firstDate = plan.weekDates.first.date;
      final dateKey = DateFormat('yyyy-MM-dd').format(firstDate);
      final meals = plan.mealPlanner['lunch']?[dateKey] ?? [];
      
      final List<String> titles = [];
      
      // Find Recommended meal first
      for (final meal in meals) {
        if (meal.mealName.contains('Recommended')) {
          titles.add(meal.mealName);
          break;
        }
      }
      // Find Indian meal
      for (final meal in meals) {
        if (meal.mealName.contains('Indian')) {
          titles.add(meal.mealName);
          break;
        }
      }
      // Find International meal
      for (final meal in meals) {
        if (meal.mealName.contains('International')) {
          titles.add(meal.mealName);
          break;
        }
      }
      return titles;
    }
    
    return ['Lunch of the Day (Recommended)', 'Indian Lunch', 'International Lunch'];
  }

  List<String> getJainSectionTitles() {
    final plan = planner.value;
    if (plan == null) return ['Jain Breakfast', 'Jain Lunch', 'Recommended'];
    
    // Get the first day's meals to extract section titles
    if (plan.weekDates.isNotEmpty) {
      final firstDate = plan.weekDates.first.date;
      final dateKey = DateFormat('yyyy-MM-dd').format(firstDate);
      final meals = plan.mealPlanner['jain']?[dateKey] ?? [];
      
      final List<String> titles = [];
      
      // Find Jain Breakfast meal
      for (final meal in meals) {
        if (meal.mealName.contains('Jain') && meal.mealName.contains('Breakfast')) {
          titles.add(meal.mealName);
          break;
        }
      }
      
      // Find Jain Lunch meal
      for (final meal in meals) {
        if (meal.mealName.contains('Jain') && meal.mealName.contains('Lunch')) {
          titles.add(meal.mealName);
          break;
        }
      }
      
      // Find Recommended meal
      for (final meal in meals) {
        if (meal.mealName.contains('Recommended')) {
          titles.add(meal.mealName);
          break;
        }
      }
      
      return titles;
    }
    
    return ['Jain Breakfast', 'Jain Lunch', 'Recommended'];
  }

  List<List<MenuDisplayItem>> _getDefaultMenu() {
    return List.generate(5, (dayIndex) => [
      MenuDisplayItem(name: 'Loading...', isMealOfDay: false),
      MenuDisplayItem(name: 'Loading...', isMealOfDay: false),
      MenuDisplayItem(name: 'Loading...', isMealOfDay: false),
    ]);
  }
}

class MenuDisplayItem {
  final String name;
  final bool isMealOfDay;
  
  MenuDisplayItem({
    required this.name,
    this.isMealOfDay = false,
  });
  
  @override
  String toString() {
    return 'MenuDisplayItem(name: "$name", isMealOfDay: $isMealOfDay)';
  }
} 