import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:startwell/models/meal_model.dart';
import 'package:startwell/models/subscription_plan_model.dart';
import 'package:startwell/services/meal_selection_manager.dart';
import 'package:startwell/services/subscription_plan_service.dart';
import 'package:startwell/models/user_profile.dart';
import 'package:table_calendar/table_calendar.dart';
import 'package:startwell/services/plan_duration_service.dart';

class SubscriptionSelectionController extends GetxController {
  // State variables
  var selectedPlanIndex = 0.obs;
  var isCustomPlan = false.obs;
  var showMealChip = false.obs;
  var selectedWeekdays = List<bool>.filled(5, false).obs;
  final List<String> weekdayNames = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri'];

  late DateTime startDate;
  late DateTime firstAvailableDate;
  DateTime? endDate;

  var mealDates = <DateTime>[].obs;
  DateTime? focusedCalendarDate;
  var calendarFormat = CalendarFormat.month.obs;

  var isMealScheduleExpanded = false.obs;

  // Meal selection state
  var breakfastMeals = [
    {'name': 'Breakfast of the Day', 'price': 75, 'isSelected': false},
    {'name': 'Indian Breakfast', 'price': 75, 'isSelected': false},
    {'name': 'International Breakfast', 'price': 75, 'isSelected': false},
    {'name': 'Jain Breakfast', 'price': 75, 'isSelected': false},
  ].obs;

  var lunchMeals = [
    {'name': 'Lunch of the Day', 'price': 125, 'isSelected': false},
    {'name': 'Indian Lunch', 'price': 125, 'isSelected': false},
    {'name': 'International Lunch', 'price': 125, 'isSelected': false},
    {'name': 'Jain Lunch', 'price': 125, 'isSelected': false},
  ].obs;

  var isBreakfastDisabled = false.obs;
  var isLunchDisabled = false.obs;

  var isBreakfastExpanded = false.obs;
  var isLunchExpanded = false.obs;

  var currentSelectedMeals = <Meal>[].obs;
  var currentTotalMealCost = 0.0.obs;
  var currentMealType = ''.obs;

  final List<Map<String, dynamic>> subscriptionPlans = [
    {
      'name': 'Single Day',
      'duration': '1 Day',
      'meals': 1,
      'discount': 0.0,
      'weeks': 0,
      'isSingleDay': true,
      'showPrice': false,
    },
    {
      'name': 'Weekly',
      'duration': '1 Week',
      'meals': 5,
      'discount': 0.0,
      'weeks': 1,
      'showPrice': false,
    },
    {
      'name': 'Monthly',
      'duration': '4 Weeks',
      'meals': 20,
      'discount': 0.0,
      'weeks': 4,
      'showPrice': false,
    },
    {
      'name': 'Quarterly',
      'duration': '3 Months',
      'meals': 60,
      'discount': 0.1,
      'weeks': 12,
      'showPrice': false,
    },
    {
      'name': 'Annual',
      'duration': '12 Months',
      'meals': 200,
      'discount': 0.2,
      'weeks': 48,
      'showPrice': false,
    },
  ];

  var selectedPlan = RxnString();
  var selectedStartDate = Rxn<DateTime>();
  var selectedEndDate = Rxn<DateTime>();
  var isPreOrder = false.obs;
  var currentEndDate = Rxn<DateTime>();

  var combinedPrice = 0.0.obs;
  var breakfastPrice = 0.0.obs;
  var lunchPrice = 0.0.obs;

  // API-driven plan durations
  var planDurations = <PlanDuration>[].obs;
  var isLoadingDurations = false.obs;
  var durationError = RxnString();

  @override
  void onInit() {
    super.onInit();
    print('SubscriptionSelectionController onInit called'); // Debug print
    loadPlanDurations();
  }

  Future<void> loadPlanDurations() async {
    print('loadPlanDurations called'); // Debug print
    try {
      isLoadingDurations.value = true;
      durationError.value = null;
      planDurations.value = await PlanDurationService.fetchDurations();
    } catch (e) {
      durationError.value = e.toString();
    } finally {
      isLoadingDurations.value = false;
    }
  }

  // Add business logic and helper methods here as needed
  // ...
} 