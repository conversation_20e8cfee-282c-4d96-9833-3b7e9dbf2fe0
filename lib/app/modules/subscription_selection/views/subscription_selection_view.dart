import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/subscription_selection_controller.dart';
import 'package:startwell/widgets/common/global_app_bar.dart';

class SubscriptionSelectionView extends GetView<SubscriptionSelectionController> {
  @override
  Widget build(BuildContext context) {
    print('SubscriptionSelectionView build called'); // Debug print
    return Scaffold(
      appBar: GlobalAppBar(
        title: 'Select Subscription Plan',
        showBackButton: true,
      ),
      body: Column(
        children: [
          Center(child: Text('Yeh screen render ho rahi hai')), // Dummy text for debug
          Expanded(
            child: Obx(() {
              // Main body layout, using controller state
              return SingleChildScrollView(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Plan selection section
                      Text('Select a Plan', style: TextStyle(fontWeight: FontWeight.bold, fontSize: 18)),
                      <PERSON><PERSON><PERSON><PERSON>(height: 12),
                      Obx(() {
                        if (controller.isLoadingDurations.value) {
                          return Center(child: CircularProgressIndicator());
                        }
                        if (controller.durationError.value != null) {
                          return Center(child: Text('Error: ${controller.durationError.value}'));
                        }
                        return GridView.builder(
                          shrinkWrap: true,
                          physics: NeverScrollableScrollPhysics(),
                          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                            crossAxisCount: 2, // Changed from 3 to 2 to increase width
                            childAspectRatio: 1.3,
                            crossAxisSpacing: 12,
                            mainAxisSpacing: 12,
                          ),
                          itemCount: controller.planDurations.length,
                          itemBuilder: (context, index) {
                            final plan = controller.planDurations[index];
                            final isSelected = controller.selectedPlanIndex.value == index;
                            print('Plan Name:  [32m [1m' + plan.planName + '\u001b[0m'); // Debug: print plan name in green bold
                            return GestureDetector(
                              onTap: () => controller.selectedPlanIndex.value = index,
                              child: AnimatedContainer(
                                duration: Duration(milliseconds: 200),
                                padding: EdgeInsets.all(16),
                                decoration: BoxDecoration(
                                  color: Colors.white, // Changed from Colors.black to Colors.white
                                  borderRadius: BorderRadius.circular(16),
                                  border: Border.all(
                                    color: isSelected ? Colors.deepPurple : Colors.grey.shade300,
                                    width: 2,
                                  ),
                                  boxShadow: [
                                    if (isSelected)
                                      BoxShadow(
                                        color: Colors.deepPurple.withOpacity(0.10),
                                        blurRadius: 12,
                                        offset: Offset(0, 4),
                                      ),
                                  ],
                                ),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Text(
                                      plan.planName, // Show exactly as in API response
                                      style: TextStyle(
                                        fontWeight: FontWeight.bold,
                                        fontSize: 16,
                                        color: Colors.black, // Use black for readability on white background
                                      ),
                                    ),
                                    SizedBox(height: 8),
                                    Text('${plan.planQuantity} meals', // plan_quantity as meals
                                        style: TextStyle(
                                          color: isSelected ? Colors.white70 : Colors.deepPurple,
                                          fontSize: 13,
                                        )),
                                    if (plan.discountPercentage != null && plan.discountPercentage! > 0)
                                      Padding(
                                        padding: const EdgeInsets.only(top: 8.0),
                                        child: Text(
                                          '${plan.discountPercentage!.toInt()}% OFF',
                                          style: TextStyle(
                                            color: isSelected ? Colors.yellow : Colors.deepOrange,
                                            fontWeight: FontWeight.bold,
                                            fontSize: 12,
                                          ),
                                        ),
                                      ),
                                  ],
                                ),
                              ),
                            );
                          },
                        );
                      }),
                      SizedBox(height: 24),
                      // Meal selection section
                      Text('Select Meals', style: TextStyle(fontWeight: FontWeight.bold, fontSize: 18)),
                      SizedBox(height: 12),
                      Obx(() => Column(
                        children: [
                          _MealSelectionExpandable(
                            title: 'Breakfast',
                            icon: Icons.free_breakfast,
                            color: Colors.orange,
                            meals: controller.breakfastMeals,
                            isDisabled: controller.isBreakfastDisabled.value,
                            isExpanded: controller.isBreakfastExpanded.value,
                            onToggle: () => controller.isBreakfastExpanded.value = !controller.isBreakfastExpanded.value,
                            onSelect: (index) {
                              for (var i = 0; i < controller.breakfastMeals.length; i++) {
                                controller.breakfastMeals[i]['isSelected'] = i == index;
                              }
                              controller.isBreakfastExpanded.value = false;
                            },
                          ),
                          SizedBox(height: 12),
                          _MealSelectionExpandable(
                            title: 'Lunch',
                            icon: Icons.lunch_dining,
                            color: Colors.green,
                            meals: controller.lunchMeals,
                            isDisabled: controller.isLunchDisabled.value,
                            isExpanded: controller.isLunchExpanded.value,
                            onToggle: () => controller.isLunchExpanded.value = !controller.isLunchExpanded.value,
                            onSelect: (index) {
                              for (var i = 0; i < controller.lunchMeals.length; i++) {
                                controller.lunchMeals[i]['isSelected'] = i == index;
                              }
                              controller.isLunchExpanded.value = false;
                            },
                          ),
                        ],
                      )),
                      SizedBox(height: 24),
                      // Date selection section
                      Text('Select Start Date', style: TextStyle(fontWeight: FontWeight.bold, fontSize: 18)),
                      SizedBox(height: 12),
                      Obx(() {
                        final startDate = controller.selectedStartDate.value;
                        final endDate = controller.selectedEndDate.value;
                        return Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Text('Start Date: ', style: TextStyle(fontWeight: FontWeight.w500)),
                                Text(startDate != null ? startDate.toString().split(' ')[0] : 'Not selected'),
                                SizedBox(width: 12),
                                ElevatedButton(
                                  onPressed: () async {
                                    final picked = await showDatePicker(
                                      context: Get.context!,
                                      initialDate: startDate ?? DateTime.now(),
                                      firstDate: DateTime.now(),
                                      lastDate: DateTime.now().add(Duration(days: 365)),
                                    );
                                    if (picked != null) {
                                      controller.selectedStartDate.value = picked;
                                      // Optionally, update end date here based on plan logic
                                      controller.selectedEndDate.value = picked.add(Duration(days: 30));
                                    }
                                  },
                                  child: Text('Pick Date'),
                                ),
                              ],
                            ),
                            SizedBox(height: 8),
                            Row(
                              children: [
                                Text('End Date: ', style: TextStyle(fontWeight: FontWeight.w500)),
                                Text(endDate != null ? endDate.toString().split(' ')[0] : 'Auto-calculated'),
                              ],
                            ),
                          ],
                        );
                      }),
                      SizedBox(height: 24),
                      // Summary/Action section
                      Text('Summary', style: TextStyle(fontWeight: FontWeight.bold, fontSize: 18)),
                      SizedBox(height: 12),
                      Obx(() {
                        final plan = controller.subscriptionPlans[controller.selectedPlanIndex.value];
                        final startDate = controller.selectedStartDate.value;
                        final endDate = controller.selectedEndDate.value;
                        final selectedBreakfast = controller.breakfastMeals.firstWhereOrNull((m) => m['isSelected'] == true);
                        final selectedLunch = controller.lunchMeals.firstWhereOrNull((m) => m['isSelected'] == true);
                        final total = ((selectedBreakfast != null ? (selectedBreakfast['price'] as num) : 0) + (selectedLunch != null ? (selectedLunch['price'] as num) : 0)) * (plan['meals'] as int? ?? 1);
                        return Card(
                          margin: EdgeInsets.only(bottom: 16),
                          child: Padding(
                            padding: const EdgeInsets.all(16.0),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text('Plan: ${plan['name']} (${plan['duration']})'),
                                SizedBox(height: 4),
                                Text('Start: ' + (startDate != null ? startDate.toString().split(' ')[0] : '-')),
                                Text('End: ' + (endDate != null ? endDate.toString().split(' ')[0] : '-')),
                                SizedBox(height: 4),
                                Text('Breakfast: ' + (selectedBreakfast != null ? (selectedBreakfast['name'] as String) : '-')),
                                Text('Lunch: ' + (selectedLunch != null ? (selectedLunch['name'] as String) : '-')),
                                SizedBox(height: 8),
                                Text('Total: ₹$total', style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16)),
                              ],
                            ),
                          ),
                        );
                      }),
                      SizedBox(height: 8),
                      Obx(() => ElevatedButton(
                        onPressed: () {
                          // TODO: Implement checkout navigation/action
                          Get.snackbar('Proceed', 'Checkout action goes here');
                        },
                        child: Text('Proceed to Checkout'),
                      )),
                    ],
                  ),
                ),
              );
            }),
          ),
        ],
      ),
    );
  }
}

class _MealSelectionExpandable extends StatelessWidget {
  final String title;
  final IconData icon;
  final Color color;
  final List meals;
  final bool isDisabled;
  final bool isExpanded;
  final VoidCallback onToggle;
  final Function(int) onSelect;

  const _MealSelectionExpandable({
    required this.title,
    required this.icon,
    required this.color,
    required this.meals,
    required this.isDisabled,
    required this.isExpanded,
    required this.onToggle,
    required this.onSelect,
  });

  @override
  Widget build(BuildContext context) {
    final selectedMeal = meals.indexWhere((m) => m['isSelected'] == true);
    return AnimatedContainer(
      duration: Duration(milliseconds: 300),
      margin: EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isDisabled
              ? Colors.grey.shade300
              : (selectedMeal != -1 ? color : color.withOpacity(0.3)),
          width: selectedMeal != -1 ? 2 : 1,
        ),
        boxShadow: [
          BoxShadow(
            color: color.withOpacity(0.05),
            blurRadius: 8,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          InkWell(
            onTap: isDisabled ? null : onToggle,
            borderRadius: isExpanded
                ? BorderRadius.vertical(top: Radius.circular(11))
                : BorderRadius.circular(11),
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              decoration: BoxDecoration(
                color: selectedMeal != -1 ? color.withOpacity(0.05) : Colors.white,
                borderRadius: isExpanded
                    ? BorderRadius.vertical(top: Radius.circular(11))
                    : BorderRadius.circular(11),
              ),
              child: Row(
                children: [
                  Icon(icon, color: isDisabled ? Colors.grey : color, size: 24),
                  SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      selectedMeal != -1 ? meals[selectedMeal]['name'] : 'Select ${title.toLowerCase()}',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                        color: isDisabled ? Colors.grey : Colors.black,
                      ),
                    ),
                  ),
                  Icon(
                    isExpanded ? Icons.keyboard_arrow_up : Icons.keyboard_arrow_down,
                    color: isDisabled ? Colors.grey : color,
                    size: 28,
                  ),
                ],
              ),
            ),
          ),
          AnimatedCrossFade(
            firstChild: SizedBox(height: 0),
            secondChild: Column(
              children: List.generate(meals.length, (index) {
                final meal = meals[index];
                return InkWell(
                  onTap: isDisabled
                      ? null
                      : () => onSelect(index),
                  child: Container(
                    padding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                    decoration: BoxDecoration(
                      color: meal['isSelected'] ? color.withOpacity(0.05) : Colors.white,
                      border: Border(
                        bottom: index != meals.length - 1
                            ? BorderSide(color: Colors.grey.withOpacity(0.2), width: 1)
                            : BorderSide.none,
                      ),
                    ),
                    child: Row(
                      children: [
                        Icon(Icons.check_circle, color: meal['isSelected'] ? color : Colors.grey, size: 20),
                        SizedBox(width: 12),
                        Expanded(
                          child: Text(
                            meal['name'],
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                              color: Colors.black,
                            ),
                          ),
                        ),
                        Text('₹${meal['price']}', style: TextStyle(fontWeight: FontWeight.bold)),
                      ],
                    ),
                  ),
                );
              }),
            ),
            crossFadeState: isExpanded ? CrossFadeState.showSecond : CrossFadeState.showFirst,
            duration: Duration(milliseconds: 250),
          ),
        ],
      ),
    );
  }
} 