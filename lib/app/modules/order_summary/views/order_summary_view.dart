import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:startwell/themes/app_theme.dart';
import 'package:startwell/models/student_model.dart';
import 'package:startwell/models/meal_model.dart';
import 'package:startwell/utils/meal_constants.dart';
import '../controllers/order_summary_controller.dart';
import '../widgets/plan_section.dart';
import '../widgets/payment_summary_section.dart';
import '../widgets/student_info_card.dart';

class OrderSummaryView extends GetView<OrderSummaryController> {
  @override
  Widget build(BuildContext context) {
    final hasDiscount = controller.planType == 'Quarterly' ||
        controller.planType == 'Half-Yearly' ||
        controller.planType == 'Annual';

    return Scaffold(
      appBar: AppBar(title: Text('Order Summary')),
      backgroundColor: AppTheme.offWhite,
      body: Column(
        children: [
          Container(
            height: 4,
            decoration: const BoxDecoration(
              gradient: AppTheme.purpleToDeepPurple,
            ),
          ),
          Expanded(
            child: SingleChildScrollView(
              physics: const BouncingScrollPhysics(),
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(height: 16),
                    StudentInfoCard(controller: controller),
                    const SizedBox(height: 16),
                    PlanSection(controller: controller),
                    const SizedBox(height: 16),
                    PaymentSummarySection(controller: controller),
                    const SizedBox(height: 24),
                    Obx(() => TextField(
                          controller: controller.promoController,
                          decoration: InputDecoration(
                            labelText: 'Promo Code',
                            errorText: controller.promoErrorMessage.value.isNotEmpty
                                ? controller.promoErrorMessage.value
                                : null,
                            suffixIcon: controller.isValidatingPromo.value
                                ? CircularProgressIndicator()
                                : IconButton(
                                    icon: Icon(Icons.check),
                                    onPressed: controller.validatePromoCode,
                                  ),
                          ),
                        )),
                    const SizedBox(height: 16),
                    Obx(() => controller.isPromoValid.value
                        ? Text('Promo applied: \\u{1F389} ${controller.appliedPromoCode.value} - Discount: ₹${controller.promoDiscountRx.value?.toStringAsFixed(0) ?? '0'}',
                            style: TextStyle(color: Colors.green))
                        : SizedBox()),
                    const SizedBox(height: 24),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
      bottomNavigationBar: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Obx(() => SizedBox(
          width: double.infinity,
          height: 56,
          child: ElevatedButton(
            style: ElevatedButton.styleFrom(
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(50)),
              padding: const EdgeInsets.symmetric(vertical: 16),
              backgroundColor: AppTheme.purple,
            ),
            onPressed: controller.isLoading.value ? null : controller.validateAndProceedToPayment,
            child: controller.isLoading.value
                ? CircularProgressIndicator(color: Colors.white)
                : Text('Proceed to Payment', style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600, color: Colors.white)),
          ),
        )),
      ),
    );
  }
} 