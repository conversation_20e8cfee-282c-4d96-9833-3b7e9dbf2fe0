import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'package:startwell/models/student_model.dart';
import 'package:startwell/models/meal_model.dart';

class OrderSummaryController extends GetxController with GetSingleTickerProviderStateMixin {
  // Parameters from previous screen
  late final String planType;
  late final bool isCustomPlan;
  late final List<bool> selectedWeekdays;
  late final DateTime startDate;
  late final DateTime endDate;
  late final List<DateTime> mealDates;
  late final double totalAmount;
  late final List<Meal> selectedMeals;
  late final bool isExpressOrder;
  Student? selectedStudent;
  String? mealType;
  DateTime? breakfastPreOrderDate;
  DateTime? lunchPreOrderDate;
  bool isPreOrder = false;
  String? selectedPlanType;
  String? deliveryMode;
  String? breakfastDeliveryMode;
  String? lunchDeliveryMode;
  DateTime? breakfastStartDate;
  DateTime? breakfastEndDate;
  List<DateTime>? breakfastMealDates;
  List<Meal>? breakfastSelectedMeals;
  double? breakfastAmount;
  String? breakfastPlanType;
  List<bool>? breakfastSelectedWeekdays;
  DateTime? lunchStartDate;
  DateTime? lunchEndDate;
  List<DateTime>? lunchMealDates;
  List<Meal>? lunchSelectedMeals;
  double? lunchAmount;
  String? lunchPlanType;
  List<bool>? lunchSelectedWeekdays;
  String? promoCode;
  double? promoDiscount;
  DateTime? preOrderStartDate;
  DateTime? preOrderEndDate;

  // State
  late AnimationController animationController;
  late Animation<double> fadeAnimation;
  late Animation<double> slideAnimation;
  final promoController = TextEditingController();
  final appliedPromoCode = RxnString();
  final isValidatingPromo = false.obs;
  final isPromoValid = false.obs;
  final promoErrorMessage = ''.obs;
  final promoDiscountRx = 0.0.obs;
  final gstPercentage = 0.05;
  final deliveryCharges = 0.0;
  final hasBothMealTypes = false.obs;
  final isLoading = false.obs;

  final validPromoCodes = {
    "WELCOME10": 0.10,
    "SUMMER20": 0.20,
    "STARTWELL25": 0.25,
  };

  @override
  void onInit() {
    super.onInit();
    final args = Get.arguments as Map<String, dynamic>?;
    if (args != null) {
      planType = args['planType'];
      isCustomPlan = args['isCustomPlan'];
      selectedWeekdays = List<bool>.from(args['selectedWeekdays']);
      startDate = args['startDate'];
      endDate = args['endDate'];
      mealDates = List<DateTime>.from(args['mealDates']);
      totalAmount = args['totalAmount'];
      selectedMeals = List<Meal>.from(args['selectedMeals']);
      isExpressOrder = args['isExpressOrder'];
      selectedStudent = args['selectedStudent'];
      mealType = args['mealType'];
      breakfastPreOrderDate = args['breakfastPreOrderDate'];
      lunchPreOrderDate = args['lunchPreOrderDate'];
      isPreOrder = args['isPreOrder'] ?? false;
      selectedPlanType = args['selectedPlanType'];
      deliveryMode = args['deliveryMode'];
      breakfastDeliveryMode = args['breakfastDeliveryMode'];
      lunchDeliveryMode = args['lunchDeliveryMode'];
      breakfastStartDate = args['breakfastStartDate'];
      breakfastEndDate = args['breakfastEndDate'];
      breakfastMealDates = args['breakfastMealDates'];
      breakfastSelectedMeals = args['breakfastSelectedMeals'];
      breakfastAmount = args['breakfastAmount'];
      breakfastPlanType = args['breakfastPlanType'];
      breakfastSelectedWeekdays = args['breakfastSelectedWeekdays'];
      lunchStartDate = args['lunchStartDate'];
      lunchEndDate = args['lunchEndDate'];
      lunchMealDates = args['lunchMealDates'];
      lunchSelectedMeals = args['lunchSelectedMeals'];
      lunchAmount = args['lunchAmount'];
      lunchPlanType = args['lunchPlanType'];
      lunchSelectedWeekdays = args['lunchSelectedWeekdays'];
      promoCode = args['promoCode'];
      promoDiscount = args['promoDiscount'];
      preOrderStartDate = args['preOrderStartDate'];
      preOrderEndDate = args['preOrderEndDate'];
    }
    if (promoCode != null && promoDiscount != null) {
      isPromoValid.value = true;
      appliedPromoCode.value = promoCode;
      promoDiscountRx.value = promoDiscount!;
    }
    hasBothMealTypes.value = (mealType == 'both') ||
        (breakfastPreOrderDate != null && lunchPreOrderDate != null);
  }

  @override
  void onClose() {
    promoController.dispose();
    super.onClose();
  }

  String getSelectedWeekdaysText() {
    final List<String> weekdayNames = [
      'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'
    ];
    List<String> selectedDays = [];
    for (int i = 0; i < selectedWeekdays.length; i++) {
      if (selectedWeekdays[i]) {
        selectedDays.add(weekdayNames[i]);
      }
    }
    if (selectedDays.isEmpty) {
      return "None";
    } else if (selectedDays.length == 5) {
      return "All Weekdays";
    } else {
      return selectedDays.join(", ");
    }
  }

  Future<void> navigateToPaymentMethods(String planType) async {
    // ... (migrate logic from _navigateToPaymentMethods)
    // Use Get.toNamed for navigation
  }

  void validatePromoCode() {
    final code = promoController.text.trim().toUpperCase();
    isValidatingPromo.value = true;
    Future.delayed(const Duration(milliseconds: 500), () {
      if (validPromoCodes.containsKey(code)) {
        isPromoValid.value = true;
        appliedPromoCode.value = code;
        promoDiscountRx.value = totalAmount * validPromoCodes[code]!;
        promoErrorMessage.value = '';
      } else {
        isPromoValid.value = false;
        appliedPromoCode.value = null;
        promoDiscountRx.value = 0.0;
        promoErrorMessage.value = 'Invalid promo code';
      }
      isValidatingPromo.value = false;
    });
  }

  void validateAndProceedToPayment() async {
    isLoading.value = true;
    final String planType;
    if (mealType != null) {
      planType = mealType!;
    } else if (selectedMeals.isNotEmpty) {
      if (selectedMeals.first.categories.first == 'breakfast') {
        planType = 'breakfast';
      } else if (selectedMeals.first.categories.first == 'expressOneDay') {
        planType = 'express';
      } else {
        planType = 'lunch';
      }
    } else {
      planType = 'lunch';
    }
    if (selectedStudent == null) {
      await navigateToPaymentMethods(planType);
      isLoading.value = false;
      return;
    }
    // Validate the meal plan before proceeding
    // (Assume MealPlanValidator.validateMealPlan is available and returns String? error)
    final String? validationError = null; // TODO: Integrate actual validator if available
    if (validationError != null) {
      Get.snackbar('Validation Error', validationError, backgroundColor: Get.theme.colorScheme.error.withOpacity(0.8), colorText: Colors.white);
      isLoading.value = false;
      return;
    }
    await navigateToPaymentMethods(planType);
    isLoading.value = false;
  }
} 