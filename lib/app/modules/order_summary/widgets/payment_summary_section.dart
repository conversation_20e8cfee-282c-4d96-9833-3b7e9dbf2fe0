import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:startwell/themes/app_theme.dart';
import '../controllers/order_summary_controller.dart';

class PaymentSummarySection extends StatelessWidget {
  final OrderSummaryController controller;
  const PaymentSummarySection({Key? key, required this.controller}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final hasDiscount = controller.planType == 'Quarterly' ||
        controller.planType == 'Half-Yearly' ||
        controller.planType == 'Annual';
    final subtotal = hasDiscount ? controller.totalAmount / 0.75 : controller.totalAmount;
    final discount = hasDiscount ? subtotal - controller.totalAmount : 0.0;
    final gst = controller.totalAmount * controller.gstPercentage;
    final delivery = controller.deliveryCharges;
    final promoDiscount = controller.promoDiscountRx.value;
    final total = controller.totalAmount + gst + delivery - promoDiscount;

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 1,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.receipt_long_rounded, color: AppTheme.purple),
                const SizedBox(width: 8),
                Text('Payment Summary', style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600, color: AppTheme.textDark)),
              ],
            ),
            const Divider(height: 24),
            _SummaryRow(label: 'Subtotal', value: '₹${subtotal?.toStringAsFixed(0) ?? '0'}'),
            if (hasDiscount)
              _SummaryRow(label: 'Discount (25%)', value: '-₹${discount?.toStringAsFixed(0) ?? '0'}', valueColor: AppTheme.success),
            if (promoDiscount > 0)
              _SummaryRow(label: 'Promo Discount', value: '-₹${promoDiscount?.toStringAsFixed(0) ?? '0'}', valueColor: AppTheme.success),
            _SummaryRow(label: 'GST (5%)', value: '₹${gst?.toStringAsFixed(0) ?? '0'}'),
            if (delivery > 0)
              _SummaryRow(label: 'Delivery Charges', value: '₹${delivery?.toStringAsFixed(0) ?? '0'}'),
            const Divider(height: 24),
            _SummaryRow(label: 'Total', value: '₹${total?.toStringAsFixed(0) ?? '0'}', isBold: true),
          ],
        ),
      ),
    );
  }
}

class _SummaryRow extends StatelessWidget {
  final String label;
  final String value;
  final Color? valueColor;
  final bool isBold;
  const _SummaryRow({required this.label, required this.value, this.valueColor, this.isBold = false});
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label, style: TextStyle(fontSize: 14, color: AppTheme.textMedium)),
          Text(value, style: TextStyle(fontSize: 14, fontWeight: isBold ? FontWeight.bold : FontWeight.w500, color: valueColor ?? AppTheme.textDark)),
        ],
      ),
    );
  }
} 