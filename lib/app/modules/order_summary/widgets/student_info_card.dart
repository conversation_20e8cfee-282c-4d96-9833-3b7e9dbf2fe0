import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:startwell/themes/app_theme.dart';
import '../controllers/order_summary_controller.dart';

class StudentInfoCard extends StatelessWidget {
  final OrderSummaryController controller;
  const StudentInfoCard({Key? key, required this.controller}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final student = controller.selectedStudent;
    if (student == null) return SizedBox();
    return Card(
      elevation: 1,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.person, size: 20, color: AppTheme.purple),
                const SizedBox(width: 8),
                Text("Student Information", style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600, color: AppTheme.textDark)),
              ],
            ),
            const Divider(height: 24),
            _DetailRow(label: "Name", value: student.name, icon: Icons.person_outline),
            _DetailRow(label: "School", value: student.schoolName, icon: Icons.school_outlined),
            _DetailRow(label: "Class", value: "${student.className} - ${student.division}", icon: Icons.class_outlined),
            _DetailRow(label: "Floor", value: student.floor, icon: Icons.apartment_outlined),
            if (student.allergies.isNotEmpty)
              _DetailRow(label: "Allergies", value: student.allergies, icon: Icons.healing_outlined, valueColor: Colors.orange),
          ],
        ),
      ),
    );
  }
}

class _DetailRow extends StatelessWidget {
  final String label;
  final String value;
  final IconData icon;
  final Color? valueColor;
  const _DetailRow({required this.label, required this.value, required this.icon, this.valueColor});
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(icon, size: 18, color: Colors.purple),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(label, style: TextStyle(fontSize: 13, color: AppTheme.textMedium)),
                Text(value, style: TextStyle(fontSize: 14, fontWeight: FontWeight.w500, color: valueColor ?? AppTheme.textDark)),
              ],
            ),
          ),
        ],
      ),
    );
  }
} 