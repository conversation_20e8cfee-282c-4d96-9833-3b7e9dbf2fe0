import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:startwell/themes/app_theme.dart';
import 'package:startwell/utils/meal_constants.dart';
import 'package:intl/intl.dart';
import '../controllers/order_summary_controller.dart';

class PlanSection extends StatelessWidget {
  final OrderSummaryController controller;
  const PlanSection({Key? key, required this.controller}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // If both breakfast and lunch plans exist
    if (controller.breakfastStartDate != null && controller.lunchStartDate != null) {
      return Card(
        margin: const EdgeInsets.only(bottom: 16),
        elevation: 1,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  const Icon(Icons.calendar_today, size: 20, color: AppTheme.purple),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      "Subscription Plan",
                      style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600, color: AppTheme.textDark),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
              if (controller.selectedStudent != null) ...[
                const SizedBox(height: 8),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                  decoration: BoxDecoration(
                    gradient: AppTheme.purpleToDeepPurple,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Icon(Icons.person_outline, size: 14, color: Colors.white),
                      const SizedBox(width: 4),
                      Text(controller.selectedStudent!.name, style: TextStyle(fontSize: 12, fontWeight: FontWeight.w600, color: Colors.white), overflow: TextOverflow.ellipsis),
                    ],
                  ),
                ),
              ],
              Divider(height: 24, color: Colors.grey.withOpacity(0.2)),
              _MealPlanDetailsSection(
                title: "Breakfast Plan",
                isPreOrder: controller.isPreOrder,
                preOrderDate: controller.breakfastPreOrderDate,
                icon: MealConstants.breakfastIcon,
                iconColor: MealConstants.breakfastIconColor,
                specificStartDate: controller.breakfastStartDate,
                specificEndDate: controller.breakfastEndDate,
                specificMealDates: controller.breakfastMealDates,
                specificAmount: controller.breakfastAmount,
                specificPlanType: controller.breakfastPlanType,
                specificDeliveryMode: controller.breakfastDeliveryMode,
                specificSelectedWeekdays: controller.breakfastSelectedWeekdays,
                hideMealTitle: true,
                controller: controller,
              ),
              const SizedBox(height: 24),
              Divider(height: 1, color: Colors.grey.withOpacity(0.2)),
              const SizedBox(height: 24),
              _MealPlanDetailsSection(
                title: "Lunch Plan",
                isPreOrder: controller.isPreOrder,
                preOrderDate: controller.lunchPreOrderDate,
                icon: MealConstants.lunchIcon,
                iconColor: MealConstants.lunchIconColor,
                specificStartDate: controller.lunchStartDate,
                specificEndDate: controller.lunchEndDate,
                specificMealDates: controller.lunchMealDates,
                specificAmount: controller.lunchAmount,
                specificPlanType: controller.lunchPlanType,
                specificDeliveryMode: controller.lunchDeliveryMode,
                specificSelectedWeekdays: controller.lunchSelectedWeekdays,
                hideMealTitle: true,
                controller: controller,
              ),
              Container(
                margin: const EdgeInsets.only(top: 24),
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: AppTheme.purple.withOpacity(0.05),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: AppTheme.purple.withOpacity(0.3), width: 1),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text("Total Price", style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600, color: AppTheme.textDark)),
                    Text("₹${controller.totalAmount?.toStringAsFixed(0) ?? '0'}", style: TextStyle(fontSize: 18, fontWeight: FontWeight.w700, color: AppTheme.purple)),
                  ],
                ),
              ),
            ],
          ),
        ),
      );
    } else {
      // Single plan display
      return Card(
        margin: const EdgeInsets.only(bottom: 16),
        elevation: 1,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  const Icon(Icons.calendar_today, size: 20, color: AppTheme.purple),
                  const SizedBox(width: 8),
                  Text(
                    controller.isExpressOrder ? "Express Delivery" : "Subscription Plan",
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.w600, color: AppTheme.textDark),
                  ),
                ],
              ),
              if (controller.selectedStudent != null) ...[
                const SizedBox(height: 8),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(colors: [AppTheme.purple.withOpacity(0.15), AppTheme.purple.withOpacity(0.15)]),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: AppTheme.purple.withOpacity(0.3), width: 1),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Icon(Icons.person_outline, size: 14, color: AppTheme.purple),
                      const SizedBox(width: 4),
                      Text(controller.selectedStudent!.name, style: TextStyle(fontSize: 12, fontWeight: FontWeight.w500, color: AppTheme.purple)),
                    ],
                  ),
                ),
              ],
              Divider(height: 32, color: Colors.grey.withOpacity(0.2)),
              // Meal name
              _DetailRow(
                label: "Selected Meal",
                value: controller.selectedMeals.isNotEmpty ? controller.selectedMeals.first.name : (controller.mealType == 'breakfast' ? "Breakfast of the Day" : "Lunch of the Day"),
                icon: Icons.restaurant_menu_outlined,
              ),
              _DetailRow(
                label: "Delivery Days",
                value: controller.deliveryMode ?? 'Monday to Friday',
                icon: Icons.calendar_view_week_outlined,
              ),
              Row(
                children: [
                  Expanded(
                    child: _DetailRow(
                      label: "Start Date",
                      value: DateFormat('d MMMM yyyy').format(controller.startDate),
                      icon: Icons.event_available_outlined,
                    ),
                  ),
                  Expanded(
                    child: _DetailRow(
                      label: "End Date",
                      value: DateFormat('d MMMM yyyy').format(controller.endDate),
                      icon: Icons.event_available_outlined,
                    ),
                  ),
                ],
              ),
              Container(
                margin: const EdgeInsets.only(top: 16),
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: AppTheme.purple.withOpacity(0.05),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: AppTheme.purple.withOpacity(0.3), width: 1),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text("Total Price", style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600, color: AppTheme.textDark)),
                    Text("₹${controller.totalAmount?.toStringAsFixed(0) ?? '0'}", style: TextStyle(fontSize: 18, fontWeight: FontWeight.w700, color: AppTheme.purple)),
                  ],
                ),
              ),
            ],
          ),
        ),
      );
    }
  }
}

class _MealPlanDetailsSection extends StatelessWidget {
  final String title;
  final bool isPreOrder;
  final DateTime? preOrderDate;
  final IconData icon;
  final Color iconColor;
  final DateTime? specificStartDate;
  final DateTime? specificEndDate;
  final List<DateTime>? specificMealDates;
  final double? specificAmount;
  final String? specificPlanType;
  final String? specificDeliveryMode;
  final List<bool>? specificSelectedWeekdays;
  final bool hideMealTitle;
  final OrderSummaryController controller;

  const _MealPlanDetailsSection({
    required this.title,
    required this.isPreOrder,
    required this.preOrderDate,
    required this.icon,
    required this.iconColor,
    this.specificStartDate,
    this.specificEndDate,
    this.specificMealDates,
    this.specificAmount,
    this.specificPlanType,
    this.specificDeliveryMode,
    this.specificSelectedWeekdays,
    this.hideMealTitle = false,
    required this.controller,
  });

  @override
  Widget build(BuildContext context) {
    final startDate = isPreOrder && preOrderDate != null ? preOrderDate : (specificStartDate ?? controller.startDate);
    final endDate = isPreOrder && specificEndDate != null ? specificEndDate : (controller.preOrderEndDate ?? controller.endDate);
    final mealDates = specificMealDates?.length ?? controller.mealDates.length;
    final totalAmount = specificAmount ?? controller.totalAmount;
    final planType = specificPlanType ?? controller.selectedPlanType ?? controller.planType;
    String mealName;
    final mealType = title == "Breakfast Plan" ? 'breakfast' : 'lunch';
    if (title == "Breakfast Plan" && controller.breakfastSelectedMeals != null && controller.breakfastSelectedMeals!.isNotEmpty) {
      mealName = controller.breakfastSelectedMeals!.first.name;
    } else if (title == "Lunch Plan" && controller.lunchSelectedMeals != null && controller.lunchSelectedMeals!.isNotEmpty) {
      mealName = controller.lunchSelectedMeals!.first.name;
    } else if (controller.selectedMeals.isNotEmpty) {
      mealName = controller.selectedMeals.first.name;
    } else {
      mealName = mealType == 'breakfast' ? 'International Breakfast' : 'International Lunch';
    }
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (!hideMealTitle)
          Row(
            children: [
              Icon(icon, size: 20, color: iconColor),
              const SizedBox(width: 8),
              Text(title, style: TextStyle(fontSize: 15, fontWeight: FontWeight.w600, color: AppTheme.textDark)),
            ],
          ),
        const SizedBox(height: 12),
        Row(
          children: [
            // You can add meal image here if needed
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(mealName, style: TextStyle(fontSize: 14, fontWeight: FontWeight.w500, color: AppTheme.textDark)),
                  const SizedBox(height: 8),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(colors: [iconColor.withOpacity(0.15), iconColor.withOpacity(0.15)]),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: iconColor.withOpacity(0.3), width: 1),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(icon, size: 16, color: iconColor),
                        const SizedBox(width: 6),
                        Text(title == "Breakfast Plan" ? "Breakfast" : "Lunch", style: TextStyle(fontSize: 13, fontWeight: FontWeight.w600, color: iconColor)),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        _DetailRow(
          label: "Delivery Days",
          value: specificDeliveryMode ?? 'Monday to Friday',
          icon: Icons.calendar_view_week_outlined,
        ),
        Row(
          children: [
            Expanded(
              child: _DetailRow(
                label: "Start Date",
                value: DateFormat('d MMM yyyy').format(startDate!),
                icon: Icons.date_range_outlined,
              ),
            ),
            Expanded(
              child: _DetailRow(
                label: "End Date",
                value: DateFormat('d MMM yyyy').format(endDate!),
                icon: Icons.event_outlined,
              ),
            ),
          ],
        ),
        if (specificAmount != null)
          _DetailRow(
            label: "Amount",
            value: "₹${specificAmount?.toStringAsFixed(0) ?? '0'}",
            icon: Icons.currency_rupee,
          ),
      ],
    );
  }
}

class _DetailRow extends StatelessWidget {
  final String label;
  final String value;
  final IconData icon;
  const _DetailRow({required this.label, required this.value, required this.icon});
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(icon, size: 18, color: Colors.purple),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(label, style: TextStyle(fontSize: 13, color: AppTheme.textMedium)),
                Text(value, style: TextStyle(fontSize: 14, fontWeight: FontWeight.w500, color: AppTheme.textDark)),
              ],
            ),
          ),
        ],
      ),
    );
  }
} 