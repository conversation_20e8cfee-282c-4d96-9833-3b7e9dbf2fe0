import 'package:get/get.dart';
import 'package:startwell/models/meal_model.dart';
import 'package:startwell/models/subscription_plan_model.dart';
import 'package:startwell/services/cart_storage_service.dart';
import 'package:startwell/services/meal_selection_manager.dart';
import 'package:startwell/services/subscription_plan_service.dart';

class CartController extends GetxController {
  var cartItems = <Map<String, dynamic>>[].obs;
  var hasBreakfastInCart = false.obs;
  var hasLunchInCart = false.obs;
  var subscriptionPlan = Rxn<SubscriptionPlanModel>();

  @override
  void onInit() {
    super.onInit();
    loadCartItems().then((_) {
      checkMealTypesInCart();
      loadSubscriptionPlan();
    });
  }

  Future<void> loadSubscriptionPlan() async {
    final plan = await SubscriptionPlanService.getSubscriptionPlan();
    subscriptionPlan.value = plan;
  }

  Future<void> loadCartItems() async {
    try {
      final savedCartItems = await CartStorageService.loadCartItems();
      if (savedCartItems.isNotEmpty) {
        cartItems.value = savedCartItems;
      }
    } catch (e) {
      print('Error loading cart items: $e');
    }
  }

  Future<void> saveCartItems() async {
    try {
      await CartStorageService.saveCartItems(cartItems);
    } catch (e) {
      print('Error saving cart items: $e');
    }
  }

  void checkMealTypesInCart() {
    hasBreakfastInCart.value = cartItems.any((item) => item['mealType'] == 'breakfast');
    hasLunchInCart.value = cartItems.any((item) => item['mealType'] == 'lunch');
    MealSelectionManager.hasBreakfastInCart = hasBreakfastInCart.value;
    MealSelectionManager.hasLunchInCart = hasLunchInCart.value;
  }

  bool get hasBothMealTypesInCart => hasBreakfastInCart.value && hasLunchInCart.value;

  // Add more business logic and helper methods as needed
} 