import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/cart_controller.dart';

class CartView extends GetView<CartController> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('Cart')),
      body: Obx(() {
        return Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('Cart Items', style: TextStyle(fontWeight: FontWeight.bold, fontSize: 18)),
              SizedBox(height: 12),
              Expanded(
                child: controller.cartItems.isEmpty
                    ? Center(child: Text('No items in cart'))
                    : ListView.builder(
                        itemCount: controller.cartItems.length,
                        itemBuilder: (context, index) {
                          final item = controller.cartItems[index];
                          return Card(
                            margin: EdgeInsets.only(bottom: 12),
                            child: ListTile(
                              title: Text(item['mealType'] ?? 'Meal'),
                              subtitle: Text('Plan: ' + (item['planType'] ?? '-')),
                              trailing: Text('₹${item['totalAmount'] ?? 0}'),
                            ),
                          );
                        },
                      ),
              ),
              SizedBox(height: 16),
              Obx(() {
                final total = controller.cartItems.fold<double>(0, (sum, item) => sum + (item['totalAmount'] ?? 0));
                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('Total: ₹$total', style: TextStyle(fontWeight: FontWeight.bold, fontSize: 18)),
                    SizedBox(height: 8),
                    ElevatedButton(
                      onPressed: () {
                        // TODO: Implement checkout navigation/action
                        Get.snackbar('Proceed', 'Checkout action goes here');
                      },
                      child: Text('Proceed to Checkout'),
                    ),
                  ],
                );
              }),
            ],
          ),
        );
      }),
    );
  }
} 