import 'package:get/get.dart';
import 'package:startwell/models/wallet_transaction.dart';
import 'package:startwell/services/wallet_service.dart';
import 'package:flutter/material.dart';

class WalletController extends GetxController {
  final walletService = WalletService();

  var totalBalance = 0.0.obs;
  var usableBalance = 0.0.obs;
  var lockedBalance = 0.0.obs;
  var transactions = <WalletTransaction>[].obs;
  var isLoading = true.obs;
  var isAddingMoney = false.obs;
  var hasError = false.obs;
  var errorMessage = ''.obs;
  var amountController = TextEditingController();
  var selectedPaymentMethod = 'PhonePe'.obs;

  final Map<String, String> paymentMethodIcons = {
    'PhonePe': 'assets/images/payment/phonepe.png',
    'Google Pay': 'assets/images/payment/gpay.png',
    'Paytm': 'assets/images/payment/paytm.png',
  };

  @override
  void onInit() {
    super.onInit();
    fetchWalletData();
  }

  @override
  void onClose() {
    amountController.dispose();
    super.onClose();
  }

  Future<void> fetchWalletData() async {
    isLoading.value = true;
    hasError.value = false;
    errorMessage.value = '';
    try {
      final balanceData = await walletService.getWalletBalance();
      final txns = await walletService.getTransactionHistory();
      totalBalance.value = balanceData['totalBalance'] ?? 0.0;
      usableBalance.value = balanceData['usableBalance'] ?? 0.0;
      lockedBalance.value = balanceData['lockedBalance'] ?? 0.0;
      transactions.value = txns;
    } catch (e) {
      hasError.value = true;
      errorMessage.value = 'Failed to load wallet data. Please try again.';
    } finally {
      isLoading.value = false;
    }
  }

  void showAddMoneyModal(BuildContext context) {
    amountController.clear();
    selectedPaymentMethod.value = 'PhonePe';
    Get.bottomSheet(
      _AddMoneySheet(controller: this),
      isScrollControlled: true,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
    );
  }

  void selectPaymentMethod(String method) {
    selectedPaymentMethod.value = method;
  }

  Future<void> addMoneyToWallet(BuildContext context) async {
    final amount = double.tryParse(amountController.text) ?? 0.0;
    if (amount <= 0) {
      Get.snackbar('Invalid Amount', 'Please enter a valid amount.');
      return;
    }
    isAddingMoney.value = true;
    await Future.delayed(Duration(seconds: 2)); // Simulate payment
    // Here you would call your payment API and update wallet
    totalBalance.value += amount;
    usableBalance.value += amount;
    transactions.insert(0, WalletTransaction(
      id: DateTime.now().toString(),
      amount: amount,
      type: TransactionType.credit, // TODO: Set actual type if needed
      date: DateTime.now(),
      description: 'Added via ${selectedPaymentMethod.value}',
      paymentMode: '', // TODO: Set actual payment mode
      transactedBy: '', // TODO: Set actual transactedBy
    ));
    isAddingMoney.value = false;
    Get.back();
    Get.snackbar('Success', 'Money added to wallet!');
  }
}

class _AddMoneySheet extends StatelessWidget {
  final WalletController controller;
  const _AddMoneySheet({required this.controller});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(
        bottom: MediaQuery.of(context).viewInsets.bottom,
        left: 20,
        right: 20,
        top: 20,
      ),
      child: Obx(() => SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Add Money to Wallet', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
            SizedBox(height: 20),
            TextField(
              controller: controller.amountController,
              keyboardType: TextInputType.number,
              decoration: InputDecoration(
                labelText: 'Amount',
                prefixText: '₹ ',
                border: OutlineInputBorder(),
              ),
            ),
            SizedBox(height: 20),
            Text('Payment Method', style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold)),
            SizedBox(height: 10),
            ...controller.paymentMethodIcons.entries.map((entry) => ListTile(
              leading: Image.asset(entry.value, width: 32, height: 32),
              title: Text(entry.key),
              trailing: Radio<String>(
                value: entry.key,
                groupValue: controller.selectedPaymentMethod.value,
                onChanged: (val) => controller.selectPaymentMethod(val!),
              ),
              onTap: () => controller.selectPaymentMethod(entry.key),
            )),
            SizedBox(height: 20),
            controller.isAddingMoney.value
                ? Center(child: CircularProgressIndicator())
                : SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: () => controller.addMoneyToWallet(context),
                      child: Text('Add Money'),
                    ),
                  ),
            SizedBox(height: 10),
          ],
        ),
      )),
    );
  }
} 