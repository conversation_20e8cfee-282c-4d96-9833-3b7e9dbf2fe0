import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/wallet_controller.dart';
import 'package:startwell/themes/app_theme.dart';

class WalletView extends GetView<WalletController> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('Wallet')),
      body: Obx(() {
        if (controller.isLoading.value) {
          return Center(child: CircularProgressIndicator());
        }
        if (controller.hasError.value) {
          return Center(child: Text(controller.errorMessage.value));
        }
        return Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text('Wallet Balance', style: TextStyle(fontWeight: FontWeight.bold, fontSize: 18)),
                  ElevatedButton(
                    onPressed: () => controller.showAddMoneyModal(context),
                    child: Text('Add Money'),
                  ),
                ],
              ),
              SizedBox(height: 16),
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      _BalanceColumn(label: 'Total', value: controller.totalBalance.value),
                      _BalanceColumn(label: 'Usable', value: controller.usableBalance.value),
                      _BalanceColumn(label: 'Locked', value: controller.lockedBalance.value),
                    ],
                  ),
                ),
              ),
              SizedBox(height: 24),
              Text('Transactions', style: TextStyle(fontWeight: FontWeight.bold, fontSize: 18)),
              SizedBox(height: 12),
              Expanded(
                child: controller.transactions.isEmpty
                    ? Center(child: Text('No transactions found'))
                    : ListView.builder(
                        itemCount: controller.transactions.length,
                        itemBuilder: (context, index) {
                          final txn = controller.transactions[index];
                          return Card(
                            margin: EdgeInsets.only(bottom: 10),
                            child: ListTile(
                              leading: Icon(
                                txn.type == 'credit' ? Icons.arrow_downward : Icons.arrow_upward,
                                color: txn.type == 'credit' ? Colors.green : Colors.red,
                              ),
                              title: Text(txn.description ?? ''),
                              subtitle: Text('${txn.date.toLocal()}'),
                              trailing: Text(
                                (txn.type == 'credit' ? '+' : '-') + '₹${txn.amount}',
                                style: TextStyle(
                                  color: txn.type == 'credit' ? Colors.green : Colors.red,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          );
                        },
                      ),
              ),
            ],
          ),
        );
      }),
    );
  }
}

class _BalanceColumn extends StatelessWidget {
  final String label;
  final double value;
  const _BalanceColumn({required this.label, required this.value});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Text(label, style: TextStyle(color: Colors.grey)),
        SizedBox(height: 4),
        Text('₹$value', style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16)),
      ],
    );
  }
} 