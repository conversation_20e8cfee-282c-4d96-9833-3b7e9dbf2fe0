import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/dashboard_controller.dart';
import 'package:startwell/themes/app_theme.dart';
import 'package:startwell/widgets/home/<USER>';
import 'package:startwell/widgets/home/<USER>';
import 'package:startwell/widgets/home/<USER>';
import 'package:startwell/widgets/home/<USER>';
import 'package:startwell/widgets/home/<USER>';
import 'package:startwell/widgets/profile_avatar.dart';
import 'package:startwell/widgets/common/global_app_bar.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:startwell/app/routes/app_routes.dart';
import 'package:startwell/screens/subscription_selection_screen.dart';
import 'package:startwell/services/meal_selection_manager.dart';

class DashboardView extends GetView<DashboardController> {
  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmall = screenWidth < 350;
    final sectionPadding = isSmall ? 10.0 : 20.0;
    final cardPadding = isSmall ? 10.0 : 18.0;
    final cardSpacing = isSmall ? 8.0 : 16.0;
    return Scaffold(
      appBar: GlobalAppBar(
        title: 'Home',
        userProfile: null, // You can pass userProfile from controller if needed
      ),
      body: Obx(() {
        if (controller.isLoading.value) {
          return const Center(child: CircularProgressIndicator());
        }
        return SafeArea(
          child: SingleChildScrollView(
            physics: const AlwaysScrollableScrollPhysics(),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: EdgeInsets.all(0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Why Parents Choose Us Section
                      _buildAnimatedSection(
                        context,
                        animation: controller.fadeAnimation,
                        slideAnimation: controller.slideAnimation,
                        delay: 0.2,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const ValueCarousel(),
                            // Order Meal Button
                            Padding(
                              padding: EdgeInsets.symmetric(
                                horizontal: isSmall ? 16 : 24,
                                vertical: 12,
                              ),
                              child: LayoutBuilder(
                                builder: (context, constraints) {
                                  final screenWidth = MediaQuery.of(context).size.width;
                                  final isTablet = screenWidth > 600;
                                  final buttonWidth = isTablet
                                      ? constraints.maxWidth * 0.6
                                      : constraints.maxWidth;
                                  return Center(
                                    child: SizedBox(
                                      width: buttonWidth,
                                      child: ElevatedButton(
                                        onPressed: () {
                                          // Create a meal selection manager for the subscription screen
                                          final selectionManager = MealSelectionManager();
                                          
                                          // Navigate to the existing subscription selection screen
                                          Navigator.push(
                                            context,
                                            MaterialPageRoute(
                                              builder: (context) => SubscriptionSelectionScreen(
                                                selectionManager: selectionManager,
                                                selectedMeals: [],
                                                totalMealCost: 0.0,
                                                initialPlanIndex: 1, // Weekly plan
                                                isExpressOrder: false,
                                                mealType: 'lunch',
                                              ),
                                            ),
                                          );
                                        },
                                        style: ElevatedButton.styleFrom(
                                          backgroundColor: AppTheme.orange,
                                          foregroundColor: AppTheme.textDark,
                                          padding: EdgeInsets.symmetric(
                                            vertical: isSmall ? 14 : (isTablet ? 20 : 16),
                                          ),
                                          shape: RoundedRectangleBorder(
                                            borderRadius: BorderRadius.circular(50),
                                          ),
                                          elevation: 4,
                                        ),
                                        child: Row(
                                          mainAxisAlignment: MainAxisAlignment.center,
                                          children: [
                                            Text(
                                              'Order Meals',
                                              style: GoogleFonts.poppins(
                                                fontSize: isSmall ? 15 : (isTablet ? 18 : 16),
                                                fontWeight: FontWeight.w600,
                                              ),
                                            ),
                                            SizedBox(width: isSmall ? 8 : 12),
                                            Icon(
                                              Icons.arrow_forward,
                                              size: isSmall ? 18 : (isTablet ? 22 : 20),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  );
                                },
                              ),
                            ),
                          ],
                        ),
                      ),
                      SizedBox(height: isSmall ? 15 : 25),
                      // Upcoming Meals Section
                      _buildAnimatedSection(
                        context,
                        animation: controller.fadeAnimation,
                        slideAnimation: controller.slideAnimation,
                        delay: 0.3,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            SectionTitle(
                              title: 'Upcoming Meals',
                              actionText: 'See All',
                              onActionPressed: () {
                                Get.toNamed(Routes.MENU);
                              },
                            ),
                            SizedBox(height: isSmall ? 8 : 15),
                            const UpcomingMealCardList(),
                          ],
                        ),
                      ),
                      // Subscription Overview Section
                      _buildAnimatedSection(
                        context,
                        animation: controller.fadeAnimation,
                        slideAnimation: controller.slideAnimation,
                        delay: 0.35,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            SectionTitle(
                              title: 'Your Subscriptions',
                              actionText: null,
                              onActionPressed: null,
                            ),
                            SizedBox(height: isSmall ? 8 : 15),
                            // No active plans message
                            if (!controller.hasActivePlans.value)
                              Card(
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                elevation: 5,
                                shadowColor: AppTheme.deepPurple.withOpacity(0.15),
                                child: Ink(
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(12),
                                    color: Colors.grey[50],
                                  ),
                                  child: Padding(
                                    padding: EdgeInsets.all(cardPadding),
                                    child: Row(
                                      children: [
                                        Container(
                                          width: isSmall ? 32 : 48,
                                          height: isSmall ? 32 : 48,
                                          decoration: BoxDecoration(
                                            color: Colors.grey.withOpacity(0.1),
                                            shape: BoxShape.circle,
                                          ),
                                          child: const Icon(
                                            Icons.calendar_month,
                                            color: Colors.grey,
                                            size: 24,
                                          ),
                                        ),
                                        SizedBox(width: isSmall ? 8 : 16),
                                        Expanded(
                                          child: Text(
                                            'No active subscription plans found',
                                            style: GoogleFonts.poppins(
                                              fontSize: isSmall ? 13 : 16,
                                              color: AppTheme.textMedium,
                                            ),
                                            maxLines: 2,
                                            overflow: TextOverflow.ellipsis,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ),
                            // Active plans - Show aggregated data for all students
                            if (controller.hasActivePlans.value)
                              Builder(
                                builder: (context) {
                                  final studentsWithPlans = controller.students.where((student) => controller.studentPlans.containsKey(student.id)).toList();
                                  if (studentsWithPlans.isEmpty) return const SizedBox.shrink();
                                  int totalActivePlans = 0;
                                  int totalRemainingMeals = 0;
                                  int totalMeals = 0;
                                  Map<String, int> mealTypeCount = {};
                                  for (var student in studentsWithPlans) {
                                    final plans = controller.studentPlans[student.id] ?? [];
                                    totalActivePlans += plans.length;
                                    for (var plan in plans) {
                                      totalRemainingMeals += plan.remainingMeals.toInt();
                                      totalMeals += plan.totalMeals.toInt();
                                      final mealType = plan.subscription.planType;
                                      final formattedType = mealType == 'breakfast' ? 'Breakfast' : 'Lunch';
                                      mealTypeCount[formattedType] = (mealTypeCount[formattedType] ?? 0) + plan.remainingMeals.toInt();
                                    }
                                  }
                                  final double progress = totalMeals > 0 ? (totalMeals - totalRemainingMeals) / totalMeals : 0.0;
                                  final firstStudent = studentsWithPlans.first;
                                  return Row(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      // Active Plan Card
                                      Expanded(
                                        child: Card(
                                          shape: RoundedRectangleBorder(
                                            borderRadius: BorderRadius.circular(16),
                                          ),
                                          elevation: 5,
                                          shadowColor: AppTheme.deepPurple.withOpacity(0.1),
                                          color: Colors.grey[50],
                                          child: InkWell(
                                            onTap: () {
                                              // Use Get.toNamed or controller method for navigation
                                            },
                                            borderRadius: BorderRadius.circular(16),
                                            child: Padding(
                                              padding: EdgeInsets.all(cardPadding),
                                              child: Column(
                                                crossAxisAlignment: CrossAxisAlignment.start,
                                                children: [
                                                  Row(
                                                    mainAxisSize: MainAxisSize.max,
                                                    children: [
                                                      const Icon(
                                                        Icons.calendar_month,
                                                        color: Colors.green,
                                                        size: 20,
                                                      ),
                                                      SizedBox(width: isSmall ? 8 : 15),
                                                      Flexible(
                                                        child: Text(
                                                          'Active Plan',
                                                          style: GoogleFonts.poppins(
                                                            fontSize: isSmall ? 10 : 12,
                                                            fontWeight: FontWeight.w600,
                                                            color: Colors.grey.shade700,
                                                          ),
                                                          maxLines: 1,
                                                          overflow: TextOverflow.ellipsis,
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                  Text(
                                                    '$totalActivePlans',
                                                    style: GoogleFonts.poppins(
                                                      fontSize: isSmall ? 18 : 24,
                                                      fontWeight: FontWeight.w600,
                                                      color: AppTheme.textDark,
                                                    ),
                                                    maxLines: 1,
                                                    overflow: TextOverflow.ellipsis,
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ),
                                        ),
                                      ),
                                      SizedBox(width: cardSpacing),
                                      // Remaining Meals Card
                                      Expanded(
                                        child: Card(
                                          shape: RoundedRectangleBorder(
                                            borderRadius: BorderRadius.circular(16),
                                          ),
                                          elevation: 5,
                                          shadowColor: AppTheme.orange.withOpacity(0.1),
                                          color: Colors.grey[50],
                                          child: InkWell(
                                            onTap: () {
                                              // Use Get.toNamed or controller method for navigation
                                            },
                                            borderRadius: BorderRadius.circular(16),
                                            child: Padding(
                                              padding: EdgeInsets.all(cardPadding),
                                              child: Column(
                                                crossAxisAlignment: CrossAxisAlignment.start,
                                                children: [
                                                  Row(
                                                    children: [
                                                      const Icon(
                                                        Icons.restaurant,
                                                        color: AppTheme.orange,
                                                        size: 20,
                                                      ),
                                                      SizedBox(width: isSmall ? 8 : 15),
                                                      Flexible(
                                                        child: Text(
                                                          'Remaining Meals',
                                                          style: GoogleFonts.poppins(
                                                            fontSize: isSmall ? 10 : 12,
                                                            fontWeight: FontWeight.w600,
                                                            color: Colors.grey.shade700,
                                                          ),
                                                          maxLines: 1,
                                                          overflow: TextOverflow.ellipsis,
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                  Text(
                                                    '$totalRemainingMeals',
                                                    style: GoogleFonts.poppins(
                                                      fontSize: isSmall ? 18 : 24,
                                                      fontWeight: FontWeight.w600,
                                                      color: AppTheme.textDark,
                                                    ),
                                                    maxLines: 1,
                                                    overflow: TextOverflow.ellipsis,
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ),
                                        ),
                                      ),
                                    ],
                                  );
                                },
                              ),
                          ],
                        ),
                      ),
                      // Testimonials Section
                      _buildAnimatedSection(
                        context,
                        animation: controller.fadeAnimation,
                        slideAnimation: controller.slideAnimation,
                        delay: 0.4,
                        child: const TestimonialsSection(),
                      ),
                      // Footer Note
                      _buildAnimatedSection(
                        context,
                        animation: controller.fadeAnimation,
                        slideAnimation: controller.slideAnimation,
                        delay: 0.5,
                        child: const FooterNote(),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      }),
    );
  }

  Widget _buildAnimatedSection(
    BuildContext context, {
    required Animation<double> animation,
    required Animation<double> slideAnimation,
    required double delay,
    required Widget child,
    double? margin,
  }) {
    final delayedAnimation = CurvedAnimation(
      parent: controller.animationController,
      curve: Interval(delay, 1.0, curve: Curves.easeOut),
    );
    return Container(
      margin: EdgeInsets.all(margin ?? 20),
      child: AnimatedBuilder(
        animation: delayedAnimation,
        builder: (context, child) {
          return Opacity(
            opacity: animation.value,
            child: Transform.translate(
              offset: Offset(
                0,
                slideAnimation.value * (1 - delayedAnimation.value),
              ),
              child: child,
            ),
          );
        },
        child: child,
      ),
    );
  }
} 