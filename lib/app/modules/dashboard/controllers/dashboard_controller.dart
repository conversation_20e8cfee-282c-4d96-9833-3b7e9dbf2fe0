import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:startwell/models/student_model.dart';
import 'package:startwell/models/subscription_model.dart';
import 'package:startwell/services/student_profile_service.dart';
import 'package:startwell/services/subscription_service.dart' as services;
import 'package:startwell/types/subscription_types.dart';

class DashboardController extends GetxController with GetTickerProviderStateMixin {
  var isLoading = true.obs;
  var hasActivePlans = false.obs;
  var students = <Student>[].obs;
  var studentPlans = <String, List<SubscriptionPlanData>>{}.obs;

  late AnimationController animationController;
  late Animation<double> fadeAnimation;
  late Animation<double> slideAnimation;
  late AnimationController menuIconController;
  late Animation<double> menuFloatAnimation;
  late Animation<double> menuFadeAnimation;

  @override
  void onInit() {
    super.onInit();
    _initAnimations();
    loadStudentData();
  }

  void _initAnimations() {
    animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1000),
    );
    menuIconController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 2000),
    )..repeat(reverse: true);
    menuFloatAnimation = Tween<double>(begin: 0.0, end: 3.0).animate(
      CurvedAnimation(parent: menuIconController, curve: Curves.easeInOut),
    );
    menuFadeAnimation = Tween<double>(begin: 0.8, end: 1.0).animate(
      CurvedAnimation(parent: menuIconController, curve: Curves.easeInOut),
    );
    fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: animationController, curve: const Interval(0.2, 1.0, curve: Curves.easeOut)),
    );
    slideAnimation = Tween<double>(begin: 20.0, end: 0.0).animate(
      CurvedAnimation(parent: animationController, curve: const Interval(0.2, 1.0, curve: Curves.easeOutCubic)),
    );
  }

  Future<void> loadStudentData() async {
    try {
      final studentProfileService = StudentProfileService();
      final subscriptionService = services.SubscriptionService();
      final List<Student> studentsList = await studentProfileService.getStudentProfiles();
      students.value = studentsList;
      Map<String, List<SubscriptionPlanData>> plans = {};
      bool hasAnyActivePlans = false;
      for (var student in studentsList) {
        final List<Subscription> subscriptions = await subscriptionService.getActiveSubscriptionsForStudent(student.id);
        if (subscriptions.isNotEmpty) {
          hasAnyActivePlans = true;
          List<SubscriptionPlanData> planDataList = [];
          for (var subscription in subscriptions) {
            final cancelledMeals = await subscriptionService.getCancelledMeals(student.id);
            final int cancelledCount = cancelledMeals.length;
            final int totalMeals = calculateTotalMeals(subscription);
            final int consumedMeals = (subscription.planType == 'express') ? 0 : calculateConsumedMeals(subscription, cancelledCount);
            planDataList.add(
              SubscriptionPlanData(
                student: student,
                subscription: subscription,
                planType: getPlanTypeDisplay(subscription),
                totalMeals: totalMeals,
                remainingMeals: totalMeals - consumedMeals,
                nextRenewalDate: subscription.endDate.day.toString() + ' ' + getMonthName(subscription.endDate.month),
              ),
            );
          }
          plans[student.id] = planDataList;
        }
      }
      studentPlans.value = plans;
      hasActivePlans.value = hasAnyActivePlans;
      isLoading.value = false;
      animationController.forward();
    } catch (e) {
      isLoading.value = false;
      animationController.forward();
    }
  }

  String getPlanTypeDisplay(Subscription plan) {
    String planPeriod;
    if (plan.duration == SubscriptionDuration.singleDay) {
      planPeriod = "Single Day";
    } else {
      final int durationInDays = plan.endDate.difference(plan.startDate).inDays + 1;
      if (durationInDays <= 7) {
        planPeriod = "Weekly";
      } else if (durationInDays <= 31) {
        planPeriod = "Monthly";
      } else if (durationInDays <= 90) {
        planPeriod = "Quarterly";
      } else if (durationInDays <= 180) {
        planPeriod = "Half-Yearly";
      } else if (durationInDays <= 365) {
        planPeriod = "Annual";
      } else {
        planPeriod = "Long Term";
      }
    }
    final mealType = plan.planType == 'breakfast' ? 'Breakfast' : 'Lunch';
    return "$planPeriod $mealType Plan";
  }

  String getMonthName(int month) {
    const months = [
      'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
      'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec',
    ];
    return months[month - 1];
  }

  int calculateTotalMeals(Subscription subscription) {
    if (subscription.planType == 'express') {
      return 1;
    }
    final days = subscription.endDate.difference(subscription.startDate).inDays + 1;
    if (subscription.selectedWeekdays.isNotEmpty) {
      final weekdayCounts = <int, int>{};
      for (int i = 0; i < days; i++) {
        final date = subscription.startDate.add(Duration(days: i));
        final weekday = date.weekday;
        if (subscription.selectedWeekdays.contains(weekday)) {
          weekdayCounts[weekday] = (weekdayCounts[weekday] ?? 0) + 1;
        }
      }
      return weekdayCounts.values.fold(0, (sum, count) => sum + count);
    } else {
      final weekdays = [1, 2, 3, 4, 5];
      int count = 0;
      for (int i = 0; i < days; i++) {
        final date = subscription.startDate.add(Duration(days: i));
        if (weekdays.contains(date.weekday)) {
          count++;
        }
      }
      return count;
    }
  }

  int calculateConsumedMeals(Subscription subscription, int cancelledCount) {
    final today = DateTime.now();
    if (today.isBefore(subscription.startDate)) {
      return 0;
    }
    final endDate = subscription.endDate.isAfter(today) ? today : subscription.endDate;
    final daysPassed = endDate.difference(subscription.startDate).inDays + 1;
    if (subscription.selectedWeekdays.isNotEmpty) {
      final weekdayCounts = <int, int>{};
      for (int i = 0; i < daysPassed; i++) {
        final date = subscription.startDate.add(Duration(days: i));
        final weekday = date.weekday;
        if (subscription.selectedWeekdays.contains(weekday)) {
          weekdayCounts[weekday] = (weekdayCounts[weekday] ?? 0) + 1;
        }
      }
      final totalPassed = weekdayCounts.values.fold(0, (sum, count) => sum + count);
      return totalPassed - cancelledCount;
    } else {
      final weekdays = [1, 2, 3, 4, 5];
      int count = 0;
      for (int i = 0; i < daysPassed; i++) {
        final date = subscription.startDate.add(Duration(days: i));
        if (weekdays.contains(date.weekday)) {
          count++;
        }
      }
      return count - cancelledCount;
    }
  }

  @override
  void onClose() {
    animationController.dispose();
    menuIconController.dispose();
    super.onClose();
  }
} 