import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/weekly_planner_controller.dart';
import 'package:intl/intl.dart';
import '../widgets/meal_category_tile.dart';

class WeeklyPlannerView extends GetView<WeeklyPlannerController> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text("Weekly Planner"),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () => controller.loadPlanner(),
          ),
        ],
      ),
      body: Obx(() {
        if (controller.isLoading.value) {
          return const Center(child: CircularProgressIndicator());
        }
        
        if (controller.error.value != null) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text('Error: ${controller.error.value}'),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: () => controller.loadPlanner(),
                  child: const Text('Retry'),
                ),
              ],
            ),
          );
        }
        
        final plan = controller.planner.value;
        if (plan == null) {
          return const Center(child: Text('No data available'));
        }
        
        return ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: plan.weekDates.length,
          itemBuilder: (_, i) {
            final day = plan.weekDates[i];
            final dateKey = DateFormat('yyyy-MM-dd').format(day.date);
            
            return Card(
              margin: const EdgeInsets.only(bottom: 16),
              child: ExpansionTile(
                title: Text(
                  "${day.dayName} (${DateFormat('dd MMM').format(day.date)})",
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                children: plan.mealPlanner.keys.map((category) {
                  final meals = plan.mealPlanner[category]?[dateKey] ?? [];
                  if (meals.isEmpty) return const SizedBox.shrink();
                  
                  return MealCategoryTile(
                    category: category.capitalize!,
                    meals: meals,
                  );
                }).toList(),
              ),
            );
          },
        );
      }),
    );
  }
} 