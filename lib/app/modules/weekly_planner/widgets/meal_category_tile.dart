import 'package:flutter/material.dart';
import '../data/models/meal.dart';

class MealCategoryTile extends StatelessWidget {
  final String category;
  final List<Meal> meals;
  
  const MealCategoryTile({
    required this.category, 
    required this.meals,
  });
  
  @override
  Widget build(BuildContext context) {
    return ExpansionTile(
      title: Text(
        category, 
        style: Theme.of(context).textTheme.titleMedium?.copyWith(
          fontWeight: FontWeight.w600,
        ),
      ),
      children: meals.map((meal) => Card(
        margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
        child: ExpansionTile(
          title: Text(
            meal.mealName,
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              fontWeight: FontWeight.w500,
            ),
          ),
          children: meal.items.map((item) => ListTile(
            dense: true,
            title: Text(item.itemName),
            subtitle: Text('Quantity: ${item.quantity}'),
            trailing: Chip(
              label: Text(
                item.category,
                style: const TextStyle(fontSize: 10),
              ),
              backgroundColor: Colors.grey[200],
            ),
          )).toList(),
        ),
      )).toList(),
    );
  }
} 