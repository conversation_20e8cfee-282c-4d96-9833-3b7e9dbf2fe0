import 'package:get/get.dart';
import '../data/providers/weekly_planner_provider.dart';
import '../data/providers/weekly_planner_repo.dart';
import '../controllers/weekly_planner_controller.dart';

class WeeklyPlannerBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => WeeklyPlannerProvider());
    Get.lazyPut(() => WeeklyPlannerRepo(Get.find()));
    Get.lazyPut(() => WeeklyPlannerController(Get.find()));
  }
} 