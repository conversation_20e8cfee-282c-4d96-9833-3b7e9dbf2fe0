import '../data/providers/weekly_planner_repo.dart';
import '../data/models/planner_response.dart';
import 'package:get/get.dart';

class WeeklyPlannerController extends GetxController {
  final WeeklyPlannerRepo repo;
  WeeklyPlannerController(this.repo);

  final planner = Rxn<PlannerResponse>();
  final isLoading = false.obs;
  final error = RxnString();

  @override
  void onInit() {
    super.onInit();
    loadPlanner();
  }

  Future<void> loadPlanner() async {
    try {
      isLoading.value = true;
      planner.value = await repo.getPlanner();
    } catch (e) {
      error.value = e.toString();
    } finally {
      isLoading.value = false;
    }
  }
} 