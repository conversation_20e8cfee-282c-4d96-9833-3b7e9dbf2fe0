import '../models/planner_response.dart';
import 'weekly_planner_provider.dart';

class WeeklyPlannerRepo {
  final WeeklyPlannerProvider api;
  WeeklyPlannerRepo(this.api);

  Future<PlannerResponse> getPlanner() async {
    final res = await api.fetchPlanner();
    
    // Debug: Print the raw API response
    print('=== WEEKLY PLANNER API RESPONSE ===');
    print('Status: ${res.statusCode}');
    print('Body: ${res.body}');
    print('===================================');
    
    if (res.isOk && res.body['success'] == true) {
      return PlannerResponse.fromJson(res.body['data']);
    }
    throw Exception(res.bodyString ?? 'Unknown error');
  }
} 