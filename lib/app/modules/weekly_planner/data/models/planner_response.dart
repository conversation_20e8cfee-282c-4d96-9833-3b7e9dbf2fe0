import 'week_date.dart';
import 'meal.dart';

class PlannerResponse {
  final List<WeekDate> weekDates;
  final Map<String, Map<String, List<Meal>>> mealPlanner; // category -> date -> meals
  final Map<String, dynamic> summary;
  
  PlannerResponse({
    required this.weekDates, 
    required this.mealPlanner,
    required this.summary,
  });
  
  factory PlannerResponse.fromJson(Map<String, dynamic> j) {
    final planner = <String, Map<String, List<Meal>>>{};
    
    // Parse meal_planner
    (j['meal_planner'] as Map<String, dynamic>).forEach((cat, val) {
      planner[cat] = (val as Map<String, dynamic>).map((date, list) =>
          MapEntry(date, (list as List).map((e) => Meal.fromJson(e)).toList()));
    });
    
    return PlannerResponse(
      weekDates: (j['week_dates'] as List).map((e) => WeekDate.fromJson(e)).toList(),
      mealPlanner: planner,
      summary: j['summary'] ?? {},
    );
  }
} 