class MenuItem {
  final int itemCode;
  final String itemName;
  final String quantity;
  final String category;
  final String source;
  final String date;
  
  MenuItem({
    required this.itemCode, 
    required this.itemName, 
    required this.quantity, 
    required this.category,
    required this.source,
    required this.date,
  });
  
  factory MenuItem.fromJson(Map<String, dynamic> j) => MenuItem(
        itemCode: j['item_code'],
        itemName: j['item_name'],
        quantity: j['quantity'],
        category: j['category'],
        source: j['source'],
        date: j['date'],
      );
} 