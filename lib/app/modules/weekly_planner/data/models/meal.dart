import 'menu_item.dart';

class Meal {
  final int mealId;
  final String mealName;
  final DateTime date;
  final String dayName;
  final List<MenuItem> items;
  Meal({required this.mealId, required this.mealName, required this.date, required this.dayName, required this.items});
  factory Meal.fromJson(Map<String, dynamic> j) => Meal(
        mealId: j['meal_id'],
        mealName: j['meal_name'],
        date: DateTime.parse(j['date']),
        dayName: j['day_name'],
        items: (j['items'] as List).map((e) => MenuItem.fromJson(e)).toList(),
      );
} 