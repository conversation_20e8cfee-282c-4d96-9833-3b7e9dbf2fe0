import 'package:get/get.dart';
import 'package:startwell/models/user_profile.dart';
import 'package:startwell/services/user_profile_service.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:flutter/material.dart';
import 'package:startwell/themes/app_theme.dart';
import 'package:startwell/utils/toast_utils.dart';

class ProfileController extends GetxController {
  final userProfileService = UserProfileService();
  var userProfile = Rxn<UserProfile>();
  var isLoading = true.obs;
  var appVersion = ''.obs;
  var tempProfileImagePath = RxnString();

  // Controllers for edit form
  final nameController = TextEditingController();
  final emailController = TextEditingController();
  final phoneController = TextEditingController();

  @override
  void onInit() {
    super.onInit();
    loadUserProfile();
    loadAppVersion();
  }

  @override
  void onClose() {
    nameController.dispose();
    emailController.dispose();
    phoneController.dispose();
    super.onClose();
  }

  Future<void> loadUserProfile() async {
    isLoading.value = true;
    try {
      final profile = await userProfileService.getCurrentProfile();
      if (profile == null) {
        userProfile.value = await userProfileService.createSampleProfile();
      } else {
        userProfile.value = profile;
      }
      nameController.text = userProfile.value?.name ?? '';
      emailController.text = userProfile.value?.email ?? '';
      phoneController.text = userProfile.value?.phoneNumber ?? '';
    } catch (e) {
      print('Error loading user profile: $e');
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> loadAppVersion() async {
    try {
      final packageInfo = await PackageInfo.fromPlatform();
      appVersion.value = 'v 0{packageInfo.version} ( 00{packageInfo.buildNumber})';
    } catch (e) {
      appVersion.value = 'Version information unavailable';
    }
  }

  Future<void> updateProfile(BuildContext context) async {
    if (nameController.text.isEmpty || emailController.text.isEmpty) {
      ToastUtils.showToast(
        context: context,
        message: 'Name and email cannot be empty',
        type: ToastType.error,
      );
      return;
    }
    isLoading.value = true;
    try {
      final success = await userProfileService.updateProfile(
        name: nameController.text,
        email: emailController.text,
        phoneNumber: phoneController.text,
      );
      if (success) {
        ToastUtils.showToast(
          context: context,
          message: 'Profile updated successfully',
          type: ToastType.success,
        );
        await loadUserProfile();
      } else {
        ToastUtils.showToast(
          context: context,
          message: 'Failed to update profile',
          type: ToastType.error,
        );
      }
    } catch (e) {
      ToastUtils.showToast(
        context: context,
        message: 'Error updating profile: $e',
        type: ToastType.error,
      );
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> showLogoutConfirmation(BuildContext context) async {
    final confirmed = await Get.dialog<bool>(
      AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        title: Text('Confirm Logout'),
        content: Text('Are you sure you want to logout from your account?'),
        actions: [
          TextButton(
            onPressed: () => Get.back(result: false),
            child: Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Get.back(result: true),
            style: ElevatedButton.styleFrom(backgroundColor: AppTheme.error),
            child: Text('Logout'),
          ),
        ],
      ),
    );
    if (confirmed == true) {
      // TODO: Implement logout logic (clear user data, navigate to login, etc.)
      ToastUtils.showToast(
        context: context,
        message: 'Logged out',
        type: ToastType.success,
      );
      // Example: Get.offAllNamed(Routes.LOGIN);
    }
  }
} 