import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/profile_controller.dart';
import 'package:startwell/themes/app_theme.dart';

class ProfileView extends GetView<ProfileController> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('Profile Settings')),
      body: Obx(() {
        if (controller.isLoading.value) {
          return Center(child: CircularProgressIndicator());
        }
        return SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Profile Info
              Center(
                child: Column(
                  children: [
                    CircleAvatar(
                      radius: 40,
                      backgroundColor: Colors.grey.shade200,
                      child: Icon(Icons.person, size: 48, color: Colors.grey),
                    ),
                    SizedBox(height: 12),
                    Text(
                      controller.userProfile.value?.name ?? '-',
                      style: TextStyle(fontWeight: FontWeight.bold, fontSize: 20),
                    ),
                    SizedBox(height: 4),
                    Text(
                      controller.userProfile.value?.email ?? '-',
                      style: TextStyle(color: Colors.grey.shade700),
                    ),
                  ],
                ),
              ),
              SizedBox(height: 24),
              // Edit Form
              Text('Edit Profile', style: TextStyle(fontWeight: FontWeight.bold, fontSize: 18)),
              SizedBox(height: 12),
              TextField(
                controller: controller.nameController,
                decoration: InputDecoration(labelText: 'Name'),
              ),
              SizedBox(height: 12),
              TextField(
                controller: controller.emailController,
                decoration: InputDecoration(labelText: 'Email'),
              ),
              SizedBox(height: 12),
              TextField(
                controller: controller.phoneController,
                decoration: InputDecoration(labelText: 'Phone Number'),
                keyboardType: TextInputType.phone,
              ),
              SizedBox(height: 16),
              ElevatedButton(
                onPressed: () => controller.updateProfile(context),
                child: Text('Update Profile'),
              ),
              SizedBox(height: 32),
              // App Version
              Text('App Version', style: TextStyle(fontWeight: FontWeight.bold)),
              SizedBox(height: 4),
              Text(controller.appVersion.value),
              SizedBox(height: 32),
              // Logout
              Center(
                child: ElevatedButton.icon(
                  onPressed: () => controller.showLogoutConfirmation(context),
                  icon: Icon(Icons.logout, color: Colors.white),
                  label: Text('Logout'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppTheme.error,
                    foregroundColor: Colors.white,
                  ),
                ),
              ),
            ],
          ),
        );
      }),
    );
  }
} 