import 'package:get/get.dart';
import 'package:startwell/models/student_model.dart';
import 'package:startwell/services/student_profile_service.dart';
import 'package:startwell/services/delivery_location_service.dart';
import 'package:startwell/services/selected_student_service.dart';
import 'package:startwell/services/customer_address_service.dart';
import 'package:flutter/material.dart';
import 'package:startwell/utils/toast_utils.dart';

class StudentController extends GetxController {
  final profileService = StudentProfileService();
  final deliveryLocationService = DeliveryLocationService();
  final selectedStudentService = SelectedStudentService();
  final customerAddressService = CustomerAddressService();
  var studentProfiles = <Student>[].obs;
  var selectedStudent = Rxn<Student>();
  var isLoading = true.obs;
  var isEditing = false.obs;
  var editingStudentIndex = (-1).obs;
  var selectedSchool = RxnString();
  var schoolNames = <String>[].obs;
  var isLoadingSchools = false.obs;
  var isLoadingFromAPI = false.obs;
  var apiError = RxnString();

  // Form controllers
  final schoolNameController = TextEditingController();
  final studentNameController = TextEditingController();
  final classController = TextEditingController();
  final divisionController = TextEditingController();
  final floorController = TextEditingController();
  final allergiesController = TextEditingController();
  final formKey = GlobalKey<FormState>();

  @override
  void onInit() {
    super.onInit();
    loadStudentProfilesFromAPI();
    loadSchoolNames();
  }

  @override
  void onClose() {
    schoolNameController.dispose();
    studentNameController.dispose();
    classController.dispose();
    divisionController.dispose();
    floorController.dispose();
    allergiesController.dispose();
    super.onClose();
  }

  // Load student profiles from API on initialization
  Future<void> loadStudentProfilesFromAPI() async {
    isLoading.value = true;
    isLoadingFromAPI.value = true;
    apiError.value = null;
    
    try {
      final apiResponse = await customerAddressService.getCustomerAddresses();
      
      if (apiResponse.success && apiResponse.data.isNotEmpty) {
        // Convert API data to Student objects
        final apiStudents = apiResponse.data.map((address) {
          return Student(
            id: address.pkCustomerAddressCode.toString(),
            name: address.childName,
            className: address.studentClass,
            division: address.division,
            floor: address.floor,
            schoolName: address.locationName,
            allergies: address.allergies.join(', '),
            grade: address.studentClass,
            section: address.division,
            profileImageUrl: '',
          );
        }).toList();

        studentProfiles.value = apiStudents;
        isLoadingFromAPI.value = false;
        
        // Save API data to local storage
        await profileService.saveStudentProfiles();
        
        // Load selected student after profiles are loaded
        await loadSelectedStudent();
        
        print('Student profiles loaded from API: ${studentProfiles.length} students');
      } else {
        // If API fails, try to load from local storage as fallback
        await loadStudentProfilesFromLocal();
        apiError.value = 'No data received from server';
        isLoadingFromAPI.value = false;
      }
    } catch (e) {
      // If API fails, try to load from local storage as fallback
      await loadStudentProfilesFromLocal();
      apiError.value = e.toString();
      isLoadingFromAPI.value = false;
      
      print('Error loading student profiles from API: $e');
    } finally {
      isLoading.value = false;
    }
  }

  // Load student profiles from local storage as fallback
  Future<void> loadStudentProfilesFromLocal() async {
    try {
      final profiles = await profileService.getStudentProfiles();
      studentProfiles.value = profiles;
      // Load selected student after profiles are loaded
      await loadSelectedStudent();
      print('Student profiles loaded from local storage: ${profiles.length} students');
    } catch (e) {
      ToastUtils.showToast(
        context: Get.context!,
        message: 'Failed to load student profiles from local storage',
        type: ToastType.error,
      );
      print('Error loading student profiles from local storage: $e');
    }
  }

  // Legacy method for backward compatibility
  Future<void> loadStudentProfiles() async {
    await loadStudentProfilesFromAPI();
  }

  Future<void> loadSchoolNames() async {
    isLoadingSchools.value = true;
    try {
      final schools = await deliveryLocationService.getSchoolNames();
      schoolNames.value = schools;
    } catch (e) {
      ToastUtils.showToast(
        context: Get.context!,
        message: 'Failed to load schools',
        type: ToastType.error,
      );
    } finally {
      isLoadingSchools.value = false;
    }
  }

  Future<void> loadSelectedStudent() async {
    try {
      final selectedStudentId = await selectedStudentService.loadSelectedStudent();
      if (selectedStudentId != null) {
        final student = studentProfiles.firstWhereOrNull((s) => s.id == selectedStudentId);
        if (student != null) {
          selectedStudent.value = student;
        }
      }
    } catch (e) {
      print('Error loading selected student: $e');
    }
  }

  Future<void> selectStudent(Student student) async {
    try {
      await selectedStudentService.setSelectedStudent(student.id);
      selectedStudent.value = student;
      ToastUtils.showToast(
        context: Get.context!,
        message: 'Selected ${student.name}',
        type: ToastType.success,
      );
    } catch (e) {
      ToastUtils.showToast(
        context: Get.context!,
        message: 'Failed to select student',
        type: ToastType.error,
      );
    }
  }

  void startAddStudent() {
    isEditing.value = false;
    editingStudentIndex.value = -1;
    schoolNameController.clear();
    studentNameController.clear();
    classController.clear();
    divisionController.clear();
    floorController.clear();
    allergiesController.clear();
    selectedSchool.value = null;
  }

  void startEditStudent(int index) {
    isEditing.value = true;
    editingStudentIndex.value = index;
    final student = studentProfiles[index];
    schoolNameController.text = student.schoolName ?? '';
    studentNameController.text = student.name;
    classController.text = student.className ?? '';
    divisionController.text = student.division ?? '';
    floorController.text = student.floor ?? '';
    allergiesController.text = student.allergies ?? '';
    selectedSchool.value = student.schoolName;
  }

  Future<void> saveStudent() async {
    if (!formKey.currentState!.validate()) return;
    isLoading.value = true;
    try {
      final student = Student(
        id: '', // TODO: Set actual id
        grade: '', // TODO: Set actual grade
        section: '', // TODO: Set actual section
        profileImageUrl: '', // TODO: Set actual profile image URL
        name: studentNameController.text,
        schoolName: schoolNameController.text,
        className: classController.text,
        division: divisionController.text,
        floor: floorController.text,
        allergies: allergiesController.text,
      );
      if (isEditing.value && editingStudentIndex.value >= 0) {
        studentProfiles[editingStudentIndex.value] = student;
      } else {
        studentProfiles.add(student);
      }
      await profileService.saveStudentProfiles();
      ToastUtils.showToast(
        context: Get.context!,
        message: isEditing.value ? 'Student updated' : 'Student added',
        type: ToastType.success,
      );
      isEditing.value = false;
      editingStudentIndex.value = -1;
      loadStudentProfiles();
    } catch (e) {
      ToastUtils.showToast(
        context: Get.context!,
        message: 'Failed to save student',
        type: ToastType.error,
      );
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> deleteStudent(int index) async {
    isLoading.value = true;
    try {
      studentProfiles.removeAt(index);
      await profileService.saveStudentProfiles();
      ToastUtils.showToast(
        context: Get.context!,
        message: 'Student deleted',
        type: ToastType.success,
      );
      loadStudentProfiles();
    } catch (e) {
      ToastUtils.showToast(
        context: Get.context!,
        message: 'Failed to delete student',
        type: ToastType.error,
      );
    } finally {
      isLoading.value = false;
    }
  }
} 