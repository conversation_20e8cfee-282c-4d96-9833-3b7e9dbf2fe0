import 'package:get/get.dart';
import '../modules/dashboard/bindings/dashboard_binding.dart';
import '../modules/dashboard/views/dashboard_view.dart';
import '../modules/menu/bindings/menu_binding.dart';
import '../modules/menu/views/menu_view.dart';
import '../modules/subscription_selection/bindings/subscription_selection_binding.dart';
import '../modules/subscription_selection/views/subscription_selection_view.dart';
import '../modules/cart/bindings/cart_binding.dart';
import '../modules/cart/views/cart_view.dart';
import '../modules/profile/bindings/profile_binding.dart';
import '../modules/profile/views/profile_view.dart';
import '../modules/student/bindings/student_binding.dart';
import '../modules/student/views/student_view.dart';
import '../modules/wallet/bindings/wallet_binding.dart';
import '../modules/wallet/views/wallet_view.dart';
import '../modules/order_summary/bindings/order_summary_binding.dart';
import '../modules/order_summary/views/order_summary_view.dart';
import '../modules/weekly_planner/bindings/weekly_planner_binding.dart';
import '../modules/weekly_planner/views/weekly_planner_view.dart';
import 'app_routes.dart';

// Import screens that don't have GetX modules yet
import '../../screens/login_screen.dart';
import '../../screens/signup_screen.dart';
import '../../screens/main_screen.dart';
import '../../screens/profile_settings_screen.dart';
import '../../screens/forgot_password_screen.dart';
import '../../screens/cart_screen.dart';

class AppPages {
  static final routes = [
    GetPage(
      name: Routes.LOGIN,
      page: () => const LoginScreen(),
    ),
    GetPage(
      name: Routes.SIGNUP,
      page: () => const SignupScreen(),
    ),
    GetPage(
      name: Routes.MAIN,
      page: () => const MainScreen(),
    ),
    GetPage(
      name: Routes.PROFILE_SETTINGS,
      page: () => const ProfileSettingsScreen(),
    ),
    GetPage(
      name: Routes.FORGOT_PASSWORD,
      page: () => const ForgotPasswordScreen(),
    ),
    GetPage(
      name: Routes.DASHBOARD,
      page: () => DashboardView(),
      binding: DashboardBinding(),
    ),
    GetPage(
      name: Routes.SUBSCRIPTION_SELECTION,
      page: () => SubscriptionSelectionView(),
      binding: SubscriptionSelectionBinding(),
    ),
    GetPage(
      name: Routes.MENU,
      page: () => MenuView(),
      binding: MenuBinding(),
    ),
    GetPage(
      name: Routes.CART,
      page: () => CartView(),
      binding: CartBinding(),
    ),
    GetPage(
      name: Routes.PROFILE,
      page: () => ProfileView(),
      binding: ProfileBinding(),
    ),
    GetPage(
      name: Routes.STUDENT,
      page: () => StudentView(),
      binding: StudentBinding(),
    ),
    GetPage(
      name: Routes.WALLET,
      page: () => WalletView(),
      binding: WalletBinding(),
    ),
    GetPage(
      name: Routes.ORDER_SUMMARY,
      page: () => OrderSummaryView(),
      binding: OrderSummaryBinding(),
    ),
    GetPage(
      name: Routes.WEEKLY_PLANNER,
      page: () => WeeklyPlannerView(),
      binding: WeeklyPlannerBinding(),
    ),
  ];
} 