abstract class Routes {
  static const LOGIN = '/login';
  static const SIGNUP = '/signup';
  static const DASHBOARD = '/dashboard';
  static const FORGOT_PASSWORD = '/forgot-password';
  static const MAIN = '/main';
  static const PROFILE_SETTINGS = '/profile-settings';
  static const CART = '/cart';
  static const SUBSCRIPTION_SELECTION = '/subscription-selection';
  static const MENU = '/menu';
  static const STUDENT = '/student';
  static const WALLET = '/wallet';
  static const ORDER_SUMMARY = '/order-summary';
  static const PROFILE = '/profile';
  static const WEEKLY_PLANNER = '/weekly-planner';
  // Lowercase aliases for backward compatibility
  static const login = LOGIN;
  static const signup = SIGNUP;
  static const dashboard = DASHBOARD;
  static const forgotPassword = FORGOT_PASSWORD;
  static const main = MAIN;
  static const profileSettings = PROFILE_SETTINGS;
  static const cart = CART;
  static const profile = PROFILE;
  // Add other routes here
} 