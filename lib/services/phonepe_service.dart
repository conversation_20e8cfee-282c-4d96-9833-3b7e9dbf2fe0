import 'package:flutter/services.dart';
import 'dart:developer';

class PhonePeService {
  static const MethodChannel _channel = MethodChannel('com.startwell.phonepe/checkout');

  /// Check if PhonePe app is installed on the device
  static Future<bool> isPhonePeInstalled() async {
    try {
      final bool isInstalled = await _channel.invokeMethod("isPhonePeInstalled");
      log('PhonePe installed: $isInstalled');
      return isInstalled;
    } on PlatformException catch (e) {
      log("PhonePe installation check error: ${e.message}");
      return false;
    }
  }

  /// Start PhonePe transaction
  static Future<PhonePeResponse?> startTransaction({
    required String url,
    required String checksum,
    String packageName = 'com.phonepe.app',
    String environment = 'UAT', // 'UAT' for testing, 'PROD' for production
  }) async {
    try {
      log('Starting PhonePe transaction...');
      log('URL: $url');
      log('Environment: $environment');
      log('Package: $packageName');

      final Map<dynamic, dynamic> response = await _channel.invokeMethod(
        "startPhonePeTransaction",
        {
          "url": url,
          "checksum": checksum,
          "packageName": packageName,
          "environment": environment,
        },
      );

      log('PhonePe response: $response');

      return PhonePeResponse.fromMap(response);
    } on PlatformException catch (e) {
      log("PhonePe transaction error: ${e.message}");
      return PhonePeResponse(
        status: 'ERROR',
        message: e.message ?? 'Unknown error occurred',
      );
    } catch (e) {
      log("PhonePe unexpected error: $e");
      return PhonePeResponse(
        status: 'ERROR',
        message: 'Unexpected error: $e',
      );
    }
  }
}

class PhonePeResponse {
  final String status;
  final String message;
  final String? transactionId;
  final String? merchantTransactionId;

  PhonePeResponse({
    required this.status,
    required this.message,
    this.transactionId,
    this.merchantTransactionId,
  });

  factory PhonePeResponse.fromMap(Map<dynamic, dynamic> map) {
    return PhonePeResponse(
      status: map['status']?.toString() ?? 'UNKNOWN',
      message: map['message']?.toString() ?? 'No message',
      transactionId: map['transactionId']?.toString(),
      merchantTransactionId: map['merchantTransactionId']?.toString(),
    );
  }

  bool get isSuccess => status == 'PAYMENT_SUCCESS' || status == 'PAYMENT_INITIATED';
  bool get isFailure => status == 'PAYMENT_FAILED' || status == 'ERROR';
  bool get isPending => status == 'PAYMENT_PENDING';

  @override
  String toString() {
    return 'PhonePeResponse(status: $status, message: $message, transactionId: $transactionId)';
  }
} 