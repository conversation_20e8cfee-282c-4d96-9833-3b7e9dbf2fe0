import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:startwell/models/customer_address_model.dart';

class CustomerAddressService {
  static const String baseUrl = 'http://*************:8002';
  
  Future<CustomerAddressesResponse> getCustomerAddresses({
    int customerId = 3787,
    int perPage = 15,
    String city = '9',
  }) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/api/test/customer-addresses?customer_id=$customerId&per_page=$perPage&city=$city'),
        headers: {
          'accept': 'application/json',
          'Content-Type': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonData = json.decode(response.body);
        return CustomerAddressesResponse.fromJson(jsonData);
      } else {
        throw Exception('Failed to load customer addresses: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error fetching customer addresses: $e');
    }
  }

  Future<Map<String, dynamic>> createCustomerAddress({
    required String childName,
    required String studentClass,
    required String division,
    required String floor,
    required List<String> allergies,
    int customerId = 3787,
    int locationCode = 2,
    String menuType = 'school',
    String city = '9',
    bool isDefault = false,
  }) async {
    try {
      final requestBody = {
        'customer_id': customerId,
        'location_code': locationCode,
        'child_name': childName,
        'class': studentClass,
        'division': division,
        'floor': floor,
        'allergies': allergies,
        'menu_type': menuType,
        'city': city,
        'default': isDefault,
      };

      print('Creating customer address with body: $requestBody');

      final response = await http.post(
        Uri.parse('$baseUrl/api/test/customer-addresses'),
        headers: {
          'accept': 'application/json',
          'Content-Type': 'application/json',
        },
        body: json.encode(requestBody),
      );

      print('Create API Response Status: ${response.statusCode}');
      print('Create API Response Body: ${response.body}');

      if (response.statusCode == 200 || response.statusCode == 201) {
        final Map<String, dynamic> jsonData = json.decode(response.body);
        return jsonData;
      } else {
        throw Exception('Failed to create customer address: ${response.statusCode} - ${response.body}');
      }
    } catch (e) {
      throw Exception('Error creating customer address: $e');
    }
  }

  Future<Map<String, dynamic>> updateCustomerAddress({
    required int addressId,
    required String childName,
    required String studentClass,
    required String division,
    required String floor,
    required List<String> allergies,
    int customerId = 3787,
    int locationCode = 2,
    bool isDefault = false,
  }) async {
    try {
      final requestBody = {
        'customer_id': customerId,
        'location_code': locationCode,
        'child_name': childName,
        'class': studentClass,
        'division': division,
        'floor': floor,
        'allergies': allergies,
        'default': isDefault,
      };

      print('Updating customer address $addressId with body: $requestBody');

      final response = await http.put(
        Uri.parse('$baseUrl/api/test/customer-addresses/$addressId'),
        headers: {
          'accept': 'application/json',
          'Content-Type': 'application/json',
        },
        body: json.encode(requestBody),
      );

      print('Update API Response Status: ${response.statusCode}');
      print('Update API Response Body: ${response.body}');

      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonData = json.decode(response.body);
        return jsonData;
      } else {
        throw Exception('Failed to update customer address: ${response.statusCode} - ${response.body}');
      }
    } catch (e) {
      throw Exception('Error updating customer address: $e');
    }
  }

  Future<Map<String, dynamic>> deleteCustomerAddress({
    required int addressId,
    int customerId = 3787,
  }) async {
    try {
      final response = await http.delete(
        Uri.parse('$baseUrl/api/test/customer-addresses/$addressId?customer_id=$customerId'),
        headers: {
          'accept': 'application/json',
          'Content-Type': 'application/json',
        },
      );

      print('Delete API Response Status: ${response.statusCode}');
      print('Delete API Response Body: ${response.body}');

      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonData = json.decode(response.body);
        return jsonData;
      } else {
        throw Exception('Failed to delete customer address: ${response.statusCode} - ${response.body}');
      }
    } catch (e) {
      throw Exception('Error deleting customer address: $e');
    }
  }

  // Helper method to convert CustomerAddress to existing Student model format
  Map<String, dynamic> customerAddressToStudentMap(CustomerAddress address) {
    return {
      'id': address.pkCustomerAddressCode.toString(),
      'name': address.childName,
      'class': address.studentClass,
      'division': address.division,
      'floor': address.floor,
      'school': address.locationName,
      'allergies': address.allergies,
      'hasAllergies': address.hasAllergies,
      'addressCode': address.pkCustomerAddressCode,
      'locationCode': address.locationCode,
      'locationName': address.locationName,
      'locationAddress': address.locationAddress,
      'isDefault': address.isDefault,
    };
  }

  // Helper method to get students from customer addresses
  List<Map<String, dynamic>> getStudentsFromAddresses(List<CustomerAddress> addresses) {
    return addresses.map((address) => customerAddressToStudentMap(address)).toList();
  }
} 