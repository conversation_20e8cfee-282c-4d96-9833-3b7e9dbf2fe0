import 'base_api_service.dart';
import 'api_config.dart';
import 'api_response.dart';

/// Calendar API service for date restrictions and availability
class CalendarApiService {
  /// Get calendar date conditions for meal plan selection
  static Future<ApiResponse<CalendarConditions>> getCalendarConditions({
    required String mealType, // 'breakfast', 'lunch', 'both'
    required String planType, // 'single', 'weekly', 'monthly', etc.
    int? locationId,
    int? customerId,
  }) async {
    try {
      // Validate input parameters
      if (mealType.trim().isEmpty) {
        return ApiResponse.error(
          message: 'Meal type is required',
          error: ApiError(
            type: ApiErrorType.validationError,
            message: 'Please specify meal type (breakfast, lunch, or both)',
          ),
        );
      }

      if (planType.trim().isEmpty) {
        return ApiResponse.error(
          message: 'Plan type is required',
          error: ApiError(
            type: ApiErrorType.validationError,
            message: 'Please specify plan type',
          ),
        );
      }

      // Build query parameters
      final queryParams = <String, String>{
        'meal_type': mealType.toLowerCase(),
        'plan_type': planType.toLowerCase(),
      };

      if (locationId != null) {
        queryParams['location_id'] = locationId.toString();
      }

      if (customerId != null) {
        queryParams['customer_id'] = customerId.toString();
      }

      // Convert to query string
      final queryString = queryParams.entries
          .map((entry) => '${entry.key}=${Uri.encodeComponent(entry.value)}')
          .join('&');

      final response = await BaseApiService.get<CalendarConditions>(
        ApiConfig.buildServiceUrl('plans', '/calendar-conditions?$queryString'),
        fromJson: (data) => CalendarConditions.fromJson(data),
      );

      return response;
    } catch (e) {
      return ApiResponse.error(
        message: 'Failed to fetch calendar conditions',
        error: ApiError(
          type: ApiErrorType.unknown,
          message: e.toString(),
        ),
      );
    }
  }

  /// Get available dates for a specific date range
  static Future<ApiResponse<AvailableDates>> getAvailableDates({
    required DateTime startDate,
    required DateTime endDate,
    required String mealType,
    required String planType,
    int? locationId,
    int? customerId,
  }) async {
    try {
      // Validate date range
      if (startDate.isAfter(endDate)) {
        return ApiResponse.error(
          message: 'Invalid date range',
          error: ApiError(
            type: ApiErrorType.validationError,
            message: 'Start date must be before end date',
          ),
        );
      }

      final requestBody = {
        'start_date': startDate.toIso8601String(),
        'end_date': endDate.toIso8601String(),
        'meal_type': mealType.toLowerCase(),
        'plan_type': planType.toLowerCase(),
      };

      if (locationId != null) {
        requestBody['location_id'] = locationId;
      }

      if (customerId != null) {
        requestBody['customer_id'] = customerId;
      }

      final response = await BaseApiService.post<AvailableDates>(
        ApiConfig.buildServiceUrl('plans', '/available-dates'),
        body: requestBody,
        fromJson: (data) => AvailableDates.fromJson(data),
      );

      return response;
    } catch (e) {
      return ApiResponse.error(
        message: 'Failed to fetch available dates',
        error: ApiError(
          type: ApiErrorType.unknown,
          message: e.toString(),
        ),
      );
    }
  }

  /// Validate a specific date for meal delivery
  static Future<ApiResponse<DateValidation>> validateDate({
    required DateTime date,
    required String mealType,
    required String planType,
    int? locationId,
    int? customerId,
  }) async {
    try {
      final requestBody = {
        'date': date.toIso8601String(),
        'meal_type': mealType.toLowerCase(),
        'plan_type': planType.toLowerCase(),
      };

      if (locationId != null) {
        requestBody['location_id'] = locationId;
      }

      if (customerId != null) {
        requestBody['customer_id'] = customerId;
      }

      final response = await BaseApiService.post<DateValidation>(
        ApiConfig.buildServiceUrl('plans', '/validate-date'),
        body: requestBody,
        fromJson: (data) => DateValidation.fromJson(data),
      );

      return response;
    } catch (e) {
      return ApiResponse.error(
        message: 'Failed to validate date',
        error: ApiError(
          type: ApiErrorType.unknown,
          message: e.toString(),
        ),
      );
    }
  }

  /// Get delivery schedule for a location
  static Future<ApiResponse<DeliverySchedule>> getDeliverySchedule({
    required int locationId,
    String? mealType,
  }) async {
    try {
      final queryParams = <String, String>{
        'location_id': locationId.toString(),
      };

      if (mealType != null && mealType.isNotEmpty) {
        queryParams['meal_type'] = mealType.toLowerCase();
      }

      final queryString = queryParams.entries
          .map((entry) => '${entry.key}=${Uri.encodeComponent(entry.value)}')
          .join('&');

      final response = await BaseApiService.get<DeliverySchedule>(
        ApiConfig.buildServiceUrl('plans', '/delivery-schedule?$queryString'),
        fromJson: (data) => DeliverySchedule.fromJson(data),
      );

      return response;
    } catch (e) {
      return ApiResponse.error(
        message: 'Failed to fetch delivery schedule',
        error: ApiError(
          type: ApiErrorType.unknown,
          message: e.toString(),
        ),
      );
    }
  }

  /// Get holidays and blackout dates
  static Future<ApiResponse<List<BlackoutDate>>> getBlackoutDates({
    required DateTime startDate,
    required DateTime endDate,
    int? locationId,
  }) async {
    try {
      if (startDate.isAfter(endDate)) {
        return ApiResponse.error(
          message: 'Invalid date range',
          error: ApiError(
            type: ApiErrorType.validationError,
            message: 'Start date must be before end date',
          ),
        );
      }

      final queryParams = <String, String>{
        'start_date': startDate.toIso8601String().split('T')[0], // Date only
        'end_date': endDate.toIso8601String().split('T')[0], // Date only
      };

      if (locationId != null) {
        queryParams['location_id'] = locationId.toString();
      }

      final queryString = queryParams.entries
          .map((entry) => '${entry.key}=${Uri.encodeComponent(entry.value)}')
          .join('&');

      final response = await BaseApiService.get<List<BlackoutDate>>(
        ApiConfig.buildServiceUrl('plans', '/blackout-dates?$queryString'),
        fromJson: (data) {
          final List<dynamic> datesList = data as List<dynamic>;
          return datesList.map((item) => BlackoutDate.fromJson(item)).toList();
        },
      );

      return response;
    } catch (e) {
      return ApiResponse.error(
        message: 'Failed to fetch blackout dates',
        error: ApiError(
          type: ApiErrorType.unknown,
          message: e.toString(),
        ),
      );
    }
  }
}

/// Model classes for calendar functionality

class CalendarConditions {
  final bool allowWeekends;
  final bool allowHolidays;
  final int maxAdvanceBookingDays;
  final int minAdvanceBookingDays;
  final List<String> allowedWeekdays;
  final List<TimeSlot> deliveryTimeSlots;
  final Map<String, dynamic>? customRestrictions;

  CalendarConditions({
    required this.allowWeekends,
    required this.allowHolidays,
    required this.maxAdvanceBookingDays,
    required this.minAdvanceBookingDays,
    required this.allowedWeekdays,
    required this.deliveryTimeSlots,
    this.customRestrictions,
  });

  factory CalendarConditions.fromJson(Map<String, dynamic> json) {
    return CalendarConditions(
      allowWeekends: json['allow_weekends'] == true,
      allowHolidays: json['allow_holidays'] == true,
      maxAdvanceBookingDays: json['max_advance_booking_days'] as int? ?? 30,
      minAdvanceBookingDays: json['min_advance_booking_days'] as int? ?? 1,
      allowedWeekdays: List<String>.from(json['allowed_weekdays'] ?? []),
      deliveryTimeSlots: (json['delivery_time_slots'] as List<dynamic>?)
              ?.map((item) => TimeSlot.fromJson(item))
              .toList() ??
          [],
      customRestrictions: json['custom_restrictions'] as Map<String, dynamic>?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'allow_weekends': allowWeekends,
      'allow_holidays': allowHolidays,
      'max_advance_booking_days': maxAdvanceBookingDays,
      'min_advance_booking_days': minAdvanceBookingDays,
      'allowed_weekdays': allowedWeekdays,
      'delivery_time_slots': deliveryTimeSlots.map((slot) => slot.toJson()).toList(),
      'custom_restrictions': customRestrictions,
    };
  }
}

class AvailableDates {
  final List<DateTime> availableDates;
  final List<DateTime> unavailableDates;
  final Map<String, String> unavailableReasons;

  AvailableDates({
    required this.availableDates,
    required this.unavailableDates,
    required this.unavailableReasons,
  });

  factory AvailableDates.fromJson(Map<String, dynamic> json) {
    return AvailableDates(
      availableDates: (json['available_dates'] as List<dynamic>?)
              ?.map((date) => DateTime.parse(date))
              .toList() ??
          [],
      unavailableDates: (json['unavailable_dates'] as List<dynamic>?)
              ?.map((date) => DateTime.parse(date))
              .toList() ??
          [],
      unavailableReasons: Map<String, String>.from(json['unavailable_reasons'] ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'available_dates': availableDates.map((date) => date.toIso8601String()).toList(),
      'unavailable_dates': unavailableDates.map((date) => date.toIso8601String()).toList(),
      'unavailable_reasons': unavailableReasons,
    };
  }
}

class DateValidation {
  final bool isValid;
  final String message;
  final List<String>? issues;
  final Map<String, dynamic>? suggestions;

  DateValidation({
    required this.isValid,
    required this.message,
    this.issues,
    this.suggestions,
  });

  factory DateValidation.fromJson(Map<String, dynamic> json) {
    return DateValidation(
      isValid: json['is_valid'] == true,
      message: json['message']?.toString() ?? '',
      issues: (json['issues'] as List<dynamic>?)?.map((item) => item.toString()).toList(),
      suggestions: json['suggestions'] as Map<String, dynamic>?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'is_valid': isValid,
      'message': message,
      'issues': issues,
      'suggestions': suggestions,
    };
  }
}

class DeliverySchedule {
  final Map<String, List<TimeSlot>> weeklySchedule;
  final List<SpecialSchedule> specialSchedules;
  final String timeZone;

  DeliverySchedule({
    required this.weeklySchedule,
    required this.specialSchedules,
    required this.timeZone,
  });

  factory DeliverySchedule.fromJson(Map<String, dynamic> json) {
    final weeklyScheduleData = json['weekly_schedule'] as Map<String, dynamic>? ?? {};
    final weeklySchedule = <String, List<TimeSlot>>{};
    
    weeklyScheduleData.forEach((day, timeSlots) {
      weeklySchedule[day] = (timeSlots as List<dynamic>)
          .map((slot) => TimeSlot.fromJson(slot))
          .toList();
    });

    return DeliverySchedule(
      weeklySchedule: weeklySchedule,
      specialSchedules: (json['special_schedules'] as List<dynamic>?)
              ?.map((item) => SpecialSchedule.fromJson(item))
              .toList() ??
          [],
      timeZone: json['time_zone']?.toString() ?? 'UTC',
    );
  }

  Map<String, dynamic> toJson() {
    final weeklyScheduleJson = <String, dynamic>{};
    weeklySchedule.forEach((day, timeSlots) {
      weeklyScheduleJson[day] = timeSlots.map((slot) => slot.toJson()).toList();
    });

    return {
      'weekly_schedule': weeklyScheduleJson,
      'special_schedules': specialSchedules.map((schedule) => schedule.toJson()).toList(),
      'time_zone': timeZone,
    };
  }
}

class TimeSlot {
  final String startTime;
  final String endTime;
  final String mealType;
  final bool isActive;

  TimeSlot({
    required this.startTime,
    required this.endTime,
    required this.mealType,
    required this.isActive,
  });

  factory TimeSlot.fromJson(Map<String, dynamic> json) {
    return TimeSlot(
      startTime: json['start_time']?.toString() ?? '',
      endTime: json['end_time']?.toString() ?? '',
      mealType: json['meal_type']?.toString() ?? '',
      isActive: json['is_active'] == true,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'start_time': startTime,
      'end_time': endTime,
      'meal_type': mealType,
      'is_active': isActive,
    };
  }
}

class SpecialSchedule {
  final DateTime date;
  final List<TimeSlot> timeSlots;
  final String reason;
  final bool isHoliday;

  SpecialSchedule({
    required this.date,
    required this.timeSlots,
    required this.reason,
    required this.isHoliday,
  });

  factory SpecialSchedule.fromJson(Map<String, dynamic> json) {
    return SpecialSchedule(
      date: DateTime.parse(json['date']),
      timeSlots: (json['time_slots'] as List<dynamic>?)
              ?.map((slot) => TimeSlot.fromJson(slot))
              .toList() ??
          [],
      reason: json['reason']?.toString() ?? '',
      isHoliday: json['is_holiday'] == true,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'date': date.toIso8601String(),
      'time_slots': timeSlots.map((slot) => slot.toJson()).toList(),
      'reason': reason,
      'is_holiday': isHoliday,
    };
  }
}

class BlackoutDate {
  final DateTime date;
  final String reason;
  final String type; // 'holiday', 'maintenance', 'custom'
  final bool isRecurring;

  BlackoutDate({
    required this.date,
    required this.reason,
    required this.type,
    required this.isRecurring,
  });

  factory BlackoutDate.fromJson(Map<String, dynamic> json) {
    return BlackoutDate(
      date: DateTime.parse(json['date']),
      reason: json['reason']?.toString() ?? '',
      type: json['type']?.toString() ?? 'custom',
      isRecurring: json['is_recurring'] == true,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'date': date.toIso8601String(),
      'reason': reason,
      'type': type,
      'is_recurring': isRecurring,
    };
  }
} 