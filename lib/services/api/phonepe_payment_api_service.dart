import 'dart:async';
import 'dart:developer' as dev;
import 'package:startwell/services/phonepe_official_service.dart';
import 'api_config.dart';
import 'base_api_service.dart';
import 'api_response.dart';

/// PhonePe Payment API Service using new architecture
class PhonePePaymentApiService {
  /// Create backend order before starting payment
  static Future<ApiResponse<Map<String, dynamic>>> createOrder({
    required double amount,
    required String customerId,
    required String planType,
    required String paymentMode, // 'phonepe'
    Map<String, dynamic>? extraData,
  }) async {
    try {
      final body = {
        'amount': amount,
        'customer_id': customerId,
        'plan_type': planType,
        'payment_mode': paymentMode,
        if (extraData != null) ...extraData,
      };
      final response = await BaseApiService.post<Map<String, dynamic>>(
        ApiConfig.buildServiceUrl('payment', '/pg/v1/pay'),
        body: body,
        fromJson: (data) => data as Map<String, dynamic>,
      );
      return response;
    } catch (e) {
      return ApiResponse.error(
        message: 'Failed to create order',
        error: ApiError(type: ApiErrorType.unknown, message: e.toString()),
      );
    }
  }

  /// Start PhonePe payment using the SDK
  static Future<PhonePeOfficialResponse> startPhonePePayment({
    required String orderId,
    required double amount,
    required String customerPhone,
    required String customerName,
    String? selectedUpiApp,
  }) async {
    // Use the PhonePeOfficialService for SDK integration
    return await PhonePeOfficialService.startUpiTransaction(
      orderId: orderId,
      amount: amount,
      customerPhone: customerPhone,
      customerName: customerName,
      selectedUpiApp: selectedUpiApp,
    ) ?? PhonePeOfficialResponse(
      status: 'ERROR',
      message: 'No response from PhonePe SDK',
      merchantTransactionId: orderId,
      amount: amount,
    );
  }

  /// Poll backend for payment status
  static Future<ApiResponse<Map<String, dynamic>>> pollPaymentStatus({
    required String orderId,
    int maxAttempts = 10,
    Duration interval = const Duration(seconds: 3),
  }) async {
    int attempts = 0;
    while (attempts < maxAttempts) {
      final response = await BaseApiService.get<Map<String, dynamic>>(
        ApiConfig.buildServiceUrl('payment', '/pg/v1/status?order_id=$orderId'),
        fromJson: (data) => data as Map<String, dynamic>,
      );
      if (response.success && response.data != null) {
        final status = response.data!['status']?.toString()?.toUpperCase() ?? '';
        if (status == 'SUCCESS' || status == 'FAILED' || status == 'ERROR') {
          return response;
        }
      }
      await Future.delayed(interval);
      attempts++;
    }
    return ApiResponse.error(
      message: 'Payment status polling timed out',
      error: ApiError(type: ApiErrorType.timeoutError, message: 'No final status after polling'),
    );
  }
} 