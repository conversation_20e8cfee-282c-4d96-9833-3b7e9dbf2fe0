import 'dart:convert';
import 'dart:io';
import 'dart:developer' as dev;
import 'package:http/http.dart' as http;
import 'api_config.dart';
import 'api_response.dart';

/// Base API service that provides standardized HTTP methods with:
/// - Authentication headers
/// - Retry logic with exponential backoff
/// - Comprehensive error handling
/// - Request/response logging
/// - Timeout handling
abstract class BaseApiService {
  /// HTTP client instance
  static final http.Client _client = http.Client();
  
  /// Authentication token storage
  static String? _authToken;
  static String? _refreshToken;
  
  /// Set authentication tokens
  static void setAuthTokens({String? accessToken, String? refreshToken}) {
    _authToken = accessToken;
    _refreshToken = refreshToken;
  }
  
  /// Clear authentication tokens
  static void clearAuthTokens() {
    _authToken = null;
    _refreshToken = null;
  }
  
  /// Get headers with authentication
  static Map<String, String> _getHeaders({Map<String, String>? additionalHeaders}) {
    final headers = Map<String, String>.from(ApiConfig.defaultHeaders);
    
    // Add authentication header if token exists
    if (_authToken != null) {
      headers['Authorization'] = 'Bearer $_authToken';
    }
    
    // Add any additional headers
    if (additionalHeaders != null) {
      headers.addAll(additionalHeaders);
    }
    
    return headers;
  }
  
  /// Generic HTTP request method with retry logic
  static Future<ApiResponse<T>> _makeRequest<T>(
    String method,
    String url,
    {
      Map<String, dynamic>? body,
      Map<String, String>? headers,
      T Function(dynamic)? fromJson,
      Duration? timeout,
      int maxRetries = ApiConfig.maxRetries,
    }
  ) async {
    int attempts = 0;
    Exception? lastException;
    
    while (attempts <= maxRetries) {
      try {
        dev.log('🚀 API Request [$method] $url', name: 'BaseApiService');
        if (body != null) {
          dev.log('📤 Request Body: ${jsonEncode(body)}', name: 'BaseApiService');
        }
        
        // Build request
        final requestHeaders = _getHeaders(additionalHeaders: headers);
        final uri = Uri.parse(url);
        
        late http.Response response;
        final requestTimeout = timeout ?? ApiConfig.connectTimeout;
        
        // Make HTTP request based on method
        switch (method.toUpperCase()) {
          case 'GET':
            response = await _client.get(uri, headers: requestHeaders)
                .timeout(requestTimeout);
            break;
          case 'POST':
            response = await _client.post(
              uri,
              headers: requestHeaders,
              body: body != null ? jsonEncode(body) : null,
            ).timeout(requestTimeout);
            break;
          case 'PUT':
            response = await _client.put(
              uri,
              headers: requestHeaders,
              body: body != null ? jsonEncode(body) : null,
            ).timeout(requestTimeout);
            break;
          case 'DELETE':
            response = await _client.delete(uri, headers: requestHeaders)
                .timeout(requestTimeout);
            break;
          default:
            throw ArgumentError('Unsupported HTTP method: $method');
        }
        
        dev.log('📥 Response [${response.statusCode}] ${response.body}', name: 'BaseApiService');
        
        // Handle response
        return _handleResponse<T>(response, fromJson);
        
      } on SocketException catch (e) {
        lastException = e;
        dev.log('🔌 Network Error (Attempt ${attempts + 1}): ${e.message}', name: 'BaseApiService');
        
        if (attempts >= maxRetries) {
          return ApiResponse.error(
            message: 'Network connection failed',
            error: ApiError(
              type: ApiErrorType.networkError,
              message: e.message,
            ),
          );
        }
        
      } on HttpException catch (e) {
        lastException = e;
        dev.log('🌐 HTTP Error (Attempt ${attempts + 1}): ${e.message}', name: 'BaseApiService');
        
        if (attempts >= maxRetries) {
          return ApiResponse.error(
            message: 'HTTP request failed',
            error: ApiError(
              type: ApiErrorType.clientError,
              message: e.message,
            ),
          );
        }
        
      } on FormatException catch (e) {
        // Don't retry format exceptions
        dev.log('🔍 Parse Error: ${e.message}', name: 'BaseApiService');
        return ApiResponse.error(
          message: 'Failed to parse response',
          error: ApiError(
            type: ApiErrorType.parseError,
            message: e.message,
          ),
        );
        
      } catch (e) {
        lastException = e is Exception ? e : Exception(e.toString());
        dev.log('⚠️ Unexpected Error (Attempt ${attempts + 1}): $e', name: 'BaseApiService');
        
        if (attempts >= maxRetries) {
          return ApiResponse.error(
            message: 'Unexpected error occurred',
            error: ApiError(
              type: ApiErrorType.unknown,
              message: e.toString(),
            ),
          );
        }
      }
      
      attempts++;
      if (attempts <= maxRetries) {
        // Exponential backoff
        final delay = ApiConfig.retryDelay * attempts;
        dev.log('⏳ Retrying in ${delay.inMilliseconds}ms...', name: 'BaseApiService');
        await Future.delayed(delay);
      }
    }
    
    // This should never be reached, but handle it just in case
    return ApiResponse.error(
      message: 'Request failed after $maxRetries retries',
      error: ApiError(
        type: ApiErrorType.unknown,
        message: lastException?.toString() ?? 'Unknown error',
      ),
    );
  }
  
  /// Handle HTTP response and convert to ApiResponse
  static ApiResponse<T> _handleResponse<T>(
    http.Response response,
    T Function(dynamic)? fromJson,
  ) {
    try {
      // Handle different status codes
      switch (response.statusCode) {
        case 200:
        case 201:
          // Success - parse JSON response
          final jsonResponse = jsonDecode(response.body) as Map<String, dynamic>;
          return ApiResponse.fromJson(jsonResponse, fromJson);
          
        case 401:
          return ApiResponse.error(
            message: 'Authentication required',
            statusCode: response.statusCode,
            error: ApiError(
              type: ApiErrorType.authenticationError,
              message: 'Invalid or expired authentication token',
            ),
          );
          
        case 403:
          return ApiResponse.error(
            message: 'Access forbidden',
            statusCode: response.statusCode,
            error: ApiError(
              type: ApiErrorType.authorizationError,
              message: 'Insufficient permissions for this operation',
            ),
          );
          
        case 404:
          return ApiResponse.error(
            message: 'Resource not found',
            statusCode: response.statusCode,
            error: ApiError(
              type: ApiErrorType.clientError,
              message: 'The requested resource was not found',
            ),
          );
          
        case 422:
          // Validation error - try to parse error details
          try {
            final errorData = jsonDecode(response.body) as Map<String, dynamic>;
            return ApiResponse.error(
              message: errorData['message']?.toString() ?? 'Validation failed',
              statusCode: response.statusCode,
              error: ApiError(
                type: ApiErrorType.validationError,
                message: errorData['message']?.toString() ?? 'Validation failed',
                details: errorData['errors'] as Map<String, dynamic>?,
              ),
            );
          } catch (e) {
            return ApiResponse.error(
              message: 'Validation failed',
              statusCode: response.statusCode,
              error: ApiError(
                type: ApiErrorType.validationError,
                message: 'Request validation failed',
              ),
            );
          }
          
        case 429:
          return ApiResponse.error(
            message: 'Too many requests',
            statusCode: response.statusCode,
            error: ApiError(
              type: ApiErrorType.clientError,
              message: 'Rate limit exceeded. Please try again later.',
            ),
          );
          
        case 500:
        case 502:
        case 503:
        case 504:
          return ApiResponse.error(
            message: 'Server error',
            statusCode: response.statusCode,
            error: ApiError(
              type: ApiErrorType.serverError,
              message: 'Server encountered an error. Please try again later.',
            ),
          );
          
        default:
          // Try to parse error message from response
          try {
            final errorData = jsonDecode(response.body) as Map<String, dynamic>;
            return ApiResponse.error(
              message: errorData['message']?.toString() ?? 'Request failed',
              statusCode: response.statusCode,
              error: ApiError.fromJson(errorData),
            );
          } catch (e) {
            return ApiResponse.error(
              message: 'Request failed with status ${response.statusCode}',
              statusCode: response.statusCode,
              error: ApiError(
                type: response.statusCode >= 500 
                    ? ApiErrorType.serverError 
                    : ApiErrorType.clientError,
                message: 'HTTP ${response.statusCode}: ${response.reasonPhrase}',
              ),
            );
          }
      }
    } catch (e) {
      return ApiResponse.error(
        message: 'Failed to process response',
        error: ApiError(
          type: ApiErrorType.parseError,
          message: e.toString(),
        ),
      );
    }
  }
  
  /// GET request
  static Future<ApiResponse<T>> get<T>(
    String url, {
    Map<String, String>? headers,
    T Function(dynamic)? fromJson,
    Duration? timeout,
  }) {
    return _makeRequest<T>('GET', url, headers: headers, fromJson: fromJson, timeout: timeout);
  }
  
  /// POST request
  static Future<ApiResponse<T>> post<T>(
    String url, {
    Map<String, dynamic>? body,
    Map<String, String>? headers,
    T Function(dynamic)? fromJson,
    Duration? timeout,
  }) {
    return _makeRequest<T>('POST', url, body: body, headers: headers, fromJson: fromJson, timeout: timeout);
  }
  
  /// PUT request
  static Future<ApiResponse<T>> put<T>(
    String url, {
    Map<String, dynamic>? body,
    Map<String, String>? headers,
    T Function(dynamic)? fromJson,
    Duration? timeout,
  }) {
    return _makeRequest<T>('PUT', url, body: body, headers: headers, fromJson: fromJson, timeout: timeout);
  }
  
  /// DELETE request
  static Future<ApiResponse<T>> delete<T>(
    String url, {
    Map<String, String>? headers,
    T Function(dynamic)? fromJson,
    Duration? timeout,
  }) {
    return _makeRequest<T>('DELETE', url, headers: headers, fromJson: fromJson, timeout: timeout);
  }
  
  /// Dispose of HTTP client
  static void dispose() {
    _client.close();
  }
} 