import 'dart:convert';
import 'dart:developer' as dev;
import 'package:shared_preferences/shared_preferences.dart';
import 'base_api_service.dart';
import 'api_config.dart';
import 'api_response.dart';

/// Authentication service for Kong API Gateway with Keycloak integration
class AuthService {
  static const String _accessTokenKey = 'access_token';
  static const String _refreshTokenKey = 'refresh_token';
  static const String _tokenExpiryKey = 'token_expiry';
  static const String _userDataKey = 'user_data';

  /// Current user data
  static Map<String, dynamic>? _currentUser;
  
  /// Get current user data
  static Map<String, dynamic>? get currentUser => _currentUser;
  
  /// Check if user is authenticated
  static bool get isAuthenticated => _currentUser != null;

  /// Initialize authentication service - check for existing tokens
  static Future<bool> initialize() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // Check if we have stored tokens
      final accessToken = prefs.getString(_accessTokenKey);
      final refreshToken = prefs.getString(_refreshTokenKey);
      final expiryTimestamp = prefs.getInt(_tokenExpiryKey);
      final userData = prefs.getString(_userDataKey);
      
      if (accessToken != null && refreshToken != null && userData != null) {
        // Check if token is still valid
        if (expiryTimestamp != null) {
          final expiryDate = DateTime.fromMillisecondsSinceEpoch(expiryTimestamp);
          final now = DateTime.now();
          
          if (now.isBefore(expiryDate)) {
            // Token is still valid
            BaseApiService.setAuthTokens(
              accessToken: accessToken,
              refreshToken: refreshToken,
            );
            _currentUser = jsonDecode(userData);
            dev.log('✅ Authentication restored from storage', name: 'AuthService');
            return true;
          } else {
            // Token expired, try to refresh
            dev.log('🔄 Token expired, attempting refresh', name: 'AuthService');
            return await _refreshAccessToken(refreshToken);
          }
        }
      }
      
      dev.log('❌ No valid authentication found', name: 'AuthService');
      return false;
    } catch (e) {
      dev.log('⚠️ Error initializing auth: $e', name: 'AuthService');
      return false;
    }
  }

  /// Login with username/password via Keycloak
  static Future<ApiResponse<Map<String, dynamic>>> login({
    required String username,
    required String password,
  }) async {
    try {
      dev.log('🔐 Attempting login for user: $username', name: 'AuthService');
      
      final response = await BaseApiService.post<Map<String, dynamic>>(
        '${ApiConfig.keycloakAuthUrl}/auth/realms/startwell/protocol/openid-connect/token',
        body: {
          'grant_type': 'password',
          'client_id': 'startwell-app',
          'username': username,
          'password': password,
        },
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
      );

      if (response.success && response.data != null) {
        await _handleSuccessfulAuth(response.data!);
        return ApiResponse.success(
          data: _currentUser!,
          message: 'Login successful',
        );
      } else {
        return ApiResponse.error(
          message: response.message,
          error: response.error,
        );
      }
    } catch (e) {
      dev.log('❌ Login error: $e', name: 'AuthService');
      return ApiResponse.error(
        message: 'Login failed',
        error: ApiError(
          type: ApiErrorType.authenticationError,
          message: e.toString(),
        ),
      );
    }
  }

  /// Refresh access token using refresh token
  static Future<bool> _refreshAccessToken(String refreshToken) async {
    try {
      dev.log('🔄 Refreshing access token', name: 'AuthService');
      
      final response = await BaseApiService.post<Map<String, dynamic>>(
        '${ApiConfig.keycloakAuthUrl}/auth/realms/startwell/protocol/openid-connect/token',
        body: {
          'grant_type': 'refresh_token',
          'client_id': 'startwell-app',
          'refresh_token': refreshToken,
        },
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
      );

      if (response.success && response.data != null) {
        await _handleSuccessfulAuth(response.data!);
        dev.log('✅ Token refreshed successfully', name: 'AuthService');
        return true;
      } else {
        dev.log('❌ Token refresh failed: ${response.message}', name: 'AuthService');
        await logout();
        return false;
      }
    } catch (e) {
      dev.log('❌ Token refresh error: $e', name: 'AuthService');
      await logout();
      return false;
    }
  }

  /// Handle successful authentication response
  static Future<void> _handleSuccessfulAuth(Map<String, dynamic> authData) async {
    try {
      final accessToken = authData['access_token'] as String;
      final refreshToken = authData['refresh_token'] as String;
      final expiresIn = authData['expires_in'] as int; // seconds
      
      // Calculate expiry time
      final expiryDate = DateTime.now().add(Duration(seconds: expiresIn));
      
      // Decode JWT to get user info (basic decode - in production use a proper JWT library)
      final userInfo = _decodeJwt(accessToken);
      
      // Store tokens and user data
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_accessTokenKey, accessToken);
      await prefs.setString(_refreshTokenKey, refreshToken);
      await prefs.setInt(_tokenExpiryKey, expiryDate.millisecondsSinceEpoch);
      await prefs.setString(_userDataKey, jsonEncode(userInfo));
      
      // Set tokens in base service
      BaseApiService.setAuthTokens(
        accessToken: accessToken,
        refreshToken: refreshToken,
      );
      
      // Store user data
      _currentUser = userInfo;
      
      dev.log('✅ Authentication data stored successfully', name: 'AuthService');
    } catch (e) {
      dev.log('❌ Error storing auth data: $e', name: 'AuthService');
      rethrow;
    }
  }

  /// Basic JWT decode (for demo - use proper JWT library in production)
  static Map<String, dynamic> _decodeJwt(String token) {
    try {
      final parts = token.split('.');
      if (parts.length != 3) {
        throw FormatException('Invalid JWT format');
      }
      
      final payload = parts[1];
      final normalized = base64Url.normalize(payload);
      final decoded = utf8.decode(base64Url.decode(normalized));
      
      return jsonDecode(decoded) as Map<String, dynamic>;
    } catch (e) {
      dev.log('⚠️ Error decoding JWT: $e', name: 'AuthService');
      return {
        'sub': 'unknown',
        'name': 'Unknown User',
        'email': '<EMAIL>',
      };
    }
  }

  /// Logout and clear all authentication data
  static Future<void> logout() async {
    try {
      dev.log('👋 Logging out user', name: 'AuthService');
      
      // Clear tokens from base service
      BaseApiService.clearAuthTokens();
      
      // Clear user data
      _currentUser = null;
      
      // Clear stored data
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_accessTokenKey);
      await prefs.remove(_refreshTokenKey);
      await prefs.remove(_tokenExpiryKey);
      await prefs.remove(_userDataKey);
      
      dev.log('✅ Logout completed', name: 'AuthService');
    } catch (e) {
      dev.log('⚠️ Error during logout: $e', name: 'AuthService');
    }
  }

  /// Check if current token is valid and refresh if needed
  static Future<bool> ensureValidToken() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final expiryTimestamp = prefs.getInt(_tokenExpiryKey);
      final refreshToken = prefs.getString(_refreshTokenKey);
      
      if (expiryTimestamp != null) {
        final expiryDate = DateTime.fromMillisecondsSinceEpoch(expiryTimestamp);
        final now = DateTime.now();
        
        // Check if token will expire in the next 5 minutes
        final fiveMinutesFromNow = now.add(Duration(minutes: 5));
        
        if (fiveMinutesFromNow.isAfter(expiryDate)) {
          // Token is expiring soon, refresh it
          if (refreshToken != null) {
            return await _refreshAccessToken(refreshToken);
          } else {
            await logout();
            return false;
          }
        }
        
        return true; // Token is still valid
      }
      
      return false; // No expiry data found
    } catch (e) {
      dev.log('⚠️ Error checking token validity: $e', name: 'AuthService');
      return false;
    }
  }

  /// Get current access token
  static Future<String?> getAccessToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_accessTokenKey);
  }

  /// Get user profile information
  static Future<ApiResponse<Map<String, dynamic>>> getUserProfile() async {
    try {
      if (!isAuthenticated) {
        return ApiResponse.error(
          message: 'User not authenticated',
          error: ApiError(
            type: ApiErrorType.authenticationError,
            message: 'Please log in to continue',
          ),
        );
      }

      // Make sure token is valid
      if (!await ensureValidToken()) {
        return ApiResponse.error(
          message: 'Session expired',
          error: ApiError(
            type: ApiErrorType.authenticationError,
            message: 'Please log in again',
          ),
        );
      }

      final response = await BaseApiService.get<Map<String, dynamic>>(
        ApiConfig.buildServiceUrl('users', '/profile'),
        fromJson: (data) => data as Map<String, dynamic>,
      );

      return response;
    } catch (e) {
      dev.log('❌ Error getting user profile: $e', name: 'AuthService');
      return ApiResponse.error(
        message: 'Failed to get user profile',
        error: ApiError(
          type: ApiErrorType.unknown,
          message: e.toString(),
        ),
      );
    }
  }

  /// Update user profile
  static Future<ApiResponse<Map<String, dynamic>>> updateUserProfile({
    required Map<String, dynamic> profileData,
  }) async {
    try {
      if (!isAuthenticated) {
        return ApiResponse.error(
          message: 'User not authenticated',
          error: ApiError(
            type: ApiErrorType.authenticationError,
            message: 'Please log in to continue',
          ),
        );
      }

      // Make sure token is valid
      if (!await ensureValidToken()) {
        return ApiResponse.error(
          message: 'Session expired',
          error: ApiError(
            type: ApiErrorType.authenticationError,
            message: 'Please log in again',
          ),
        );
      }

      final response = await BaseApiService.put<Map<String, dynamic>>(
        ApiConfig.buildServiceUrl('users', '/profile'),
        body: profileData,
        fromJson: (data) => data as Map<String, dynamic>,
      );

      return response;
    } catch (e) {
      dev.log('❌ Error updating user profile: $e', name: 'AuthService');
      return ApiResponse.error(
        message: 'Failed to update user profile',
        error: ApiError(
          type: ApiErrorType.unknown,
          message: e.toString(),
        ),
      );
    }
  }
} 