class ApiConfig {
  // Base URLs for different environments
  static const String _baseUrlDev = 'http://192.168.1.167';
  static const String _baseUrlStaging = 'https://staging-api.startwell.com';
  static const String _baseUrlProd = 'https://api.startwell.com';
  
  // Current environment
  static const ApiEnvironment currentEnvironment = ApiEnvironment.development;
  
  // Get base URL based on environment
  static String get baseUrl {
    switch (currentEnvironment) {
      case ApiEnvironment.development:
        return _baseUrlDev;
      case ApiEnvironment.staging:
        return _baseUrlStaging;
      case ApiEnvironment.production:
        return _baseUrlProd;
    }
  }
  
  // API Version
  static const String apiVersion = 'v2';
  
  // Service ports (for development)
  static const Map<String, int> servicePorts = {
    'promo': 8002,
    'admin': 8005,
    'students': 8003,
    'payment': 8001,
    'plans': 8004,
  };
  
  // Common headers
  static Map<String, String> get defaultHeaders => {
    'accept': 'application/json',
    'Content-Type': 'application/json',
  };
  
  // Timeouts
  static const Duration connectTimeout = Duration(seconds: 30);
  static const Duration receiveTimeout = Duration(seconds: 30);
  
  // Retry configuration
  static const int maxRetries = 3;
  static const Duration retryDelay = Duration(seconds: 1);
  
  // Kong API Gateway settings
  static const String kongGatewayUrl = 'https://api-gateway.startwell.com';
  static const String keycloakAuthUrl = 'https://auth.startwell.com';
  
  // Build service URL
  static String buildServiceUrl(String service, String endpoint) {
    if (currentEnvironment == ApiEnvironment.development) {
      final port = servicePorts[service] ?? 8000;
      return '$baseUrl:$port/api/$apiVersion$endpoint';
    } else {
      return '$baseUrl/api/$apiVersion$endpoint';
    }
  }
}

enum ApiEnvironment {
  development,
  staging,
  production,
} 