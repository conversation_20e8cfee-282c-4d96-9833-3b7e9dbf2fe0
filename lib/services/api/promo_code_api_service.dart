import '../models/promo_code_response.dart';
import 'base_api_service.dart';
import 'api_config.dart';
import 'api_response.dart';

/// Modernized promo code service using the new API architecture
class PromoCodeApiService {
  /// Apply promo code with comprehensive validation and error handling
  static Future<ApiResponse<PromoCodeResponse>> applyPromoCode({
    required String promoCode,
    required int orderId,
    required double orderAmount,
    required List<int> productCodes,
    required int companyId,
    required int unitId,
    required int customerId,
    required int locationId,
    required List<int> planQuantities,
    required List<int> cartQuantities,
    String? planType,
  }) async {
    try {
      // Validate input parameters
      if (promoCode.trim().isEmpty) {
        return ApiResponse.error(
          message: 'Promo code is required',
          error: ApiError(
            type: ApiErrorType.validationError,
            message: 'Please enter a valid promo code',
          ),
        );
      }

      if (orderAmount <= 0) {
        return ApiResponse.error(
          message: 'Invalid order amount',
          error: ApiError(
            type: ApiErrorType.validationError,
            message: 'Order amount must be greater than zero',
          ),
        );
      }

      if (productCodes.isEmpty) {
        return ApiResponse.error(
          message: 'No products selected',
          error: ApiError(
            type: ApiErrorType.validationError,
            message: 'At least one product must be selected',
          ),
        );
      }

      // Build request body
      final requestBody = {
        'promo_code': promoCode.trim().toUpperCase(),
        'order_id': orderId,
        'order_amount': orderAmount,
        'product_codes': productCodes,
        'company_id': companyId,
        'unit_id': unitId,
        'customer_id': customerId,
        'location_id': locationId,
        'plan_quantity': planQuantities.map((q) => {'quantity': q}).toList(),
        'cart_items': cartQuantities.map((q) => {'quantity': q}).toList(),
      };

      // Add plan type if provided
      if (planType != null && planType.isNotEmpty) {
        requestBody['plan_type'] = planType;
      }

      // Make API call using the standardized base service
      final response = await BaseApiService.post<PromoCodeResponse>(
        ApiConfig.buildServiceUrl('promo', '/quickserve/orders/apply-coupon'),
        body: requestBody,
        fromJson: (data) => PromoCodeResponse.fromJson(data),
      );

      return response;
    } catch (e) {
      return ApiResponse.error(
        message: 'Failed to apply promo code',
        error: ApiError(
          type: ApiErrorType.unknown,
          message: e.toString(),
        ),
      );
    }
  }

  /// Get available promo codes for user
  static Future<ApiResponse<List<PromoCodes>>> getAvailablePromoCodes({
    required int customerId,
    int? locationId,
  }) async {
    try {
      final response = await BaseApiService.get<List<PromoCodes>>(
        ApiConfig.buildServiceUrl('promo', '/codes/available?customer_id=$customerId${locationId != null ? '&location_id=$locationId' : ''}'),
        fromJson: (data) {
          final List<dynamic> codesList = data as List<dynamic>;
          return codesList.map((item) => PromoCodes.fromJson(item)).toList();
        },
      );

      return response;
    } catch (e) {
      return ApiResponse.error(
        message: 'Failed to fetch available promo codes',
        error: ApiError(
          type: ApiErrorType.unknown,
          message: e.toString(),
        ),
      );
    }
  }

  /// Validate promo code before applying (lightweight check)
  static Future<ApiResponse<PromoCodeValidation>> validatePromoCode({
    required String promoCode,
    required int customerId,
    int? locationId,
  }) async {
    try {
      if (promoCode.trim().isEmpty) {
        return ApiResponse.error(
          message: 'Promo code is required',
          error: ApiError(
            type: ApiErrorType.validationError,
            message: 'Please enter a valid promo code',
          ),
        );
      }

      final requestBody = {
        'promo_code': promoCode.trim().toUpperCase(),
        'customer_id': customerId,
        if (locationId != null) 'location_id': locationId,
      };

      final response = await BaseApiService.post<PromoCodeValidation>(
        ApiConfig.buildServiceUrl('promo', '/codes/validate'),
        body: requestBody,
        fromJson: (data) => PromoCodeValidation.fromJson(data),
      );

      return response;
    } catch (e) {
      return ApiResponse.error(
        message: 'Failed to validate promo code',
        error: ApiError(
          type: ApiErrorType.unknown,
          message: e.toString(),
        ),
      );
    }
  }

  /// Get promo code usage history for customer
  static Future<ApiResponse<List<PromoCodeUsage>>> getPromoCodeHistory({
    required int customerId,
    int? limit = 10,
  }) async {
    try {
      final response = await BaseApiService.get<List<PromoCodeUsage>>(
        ApiConfig.buildServiceUrl('promo', '/codes/history?customer_id=$customerId&limit=$limit'),
        fromJson: (data) {
          final List<dynamic> usageList = data as List<dynamic>;
          return usageList.map((item) => PromoCodeUsage.fromJson(item)).toList();
        },
      );

      return response;
    } catch (e) {
      return ApiResponse.error(
        message: 'Failed to fetch promo code history',
        error: ApiError(
          type: ApiErrorType.unknown,
          message: e.toString(),
        ),
      );
    }
  }
}

/// Model classes for extended promo code functionality

class PromoCodes {
  final String code;
  final String description;
  final double discountAmount;
  final double? discountPercentage;
  final double? minimumOrderAmount;
  final DateTime validFrom;
  final DateTime validUntil;
  final bool isActive;

  PromoCodes({
    required this.code,
    required this.description,
    required this.discountAmount,
    this.discountPercentage,
    this.minimumOrderAmount,
    required this.validFrom,
    required this.validUntil,
    required this.isActive,
  });

  factory PromoCodes.fromJson(Map<String, dynamic> json) {
    return PromoCodes(
      code: json['code']?.toString() ?? '',
      description: json['description']?.toString() ?? '',
      discountAmount: (json['discount_amount'] as num?)?.toDouble() ?? 0.0,
      discountPercentage: (json['discount_percentage'] as num?)?.toDouble(),
      minimumOrderAmount: (json['minimum_order_amount'] as num?)?.toDouble(),
      validFrom: DateTime.parse(json['valid_from']),
      validUntil: DateTime.parse(json['valid_until']),
      isActive: json['is_active'] == true,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'code': code,
      'description': description,
      'discount_amount': discountAmount,
      'discount_percentage': discountPercentage,
      'minimum_order_amount': minimumOrderAmount,
      'valid_from': validFrom.toIso8601String(),
      'valid_until': validUntil.toIso8601String(),
      'is_active': isActive,
    };
  }
}

class PromoCodeValidation {
  final bool isValid;
  final String message;
  final double? estimatedDiscount;
  final Map<String, dynamic>? restrictions;

  PromoCodeValidation({
    required this.isValid,
    required this.message,
    this.estimatedDiscount,
    this.restrictions,
  });

  factory PromoCodeValidation.fromJson(Map<String, dynamic> json) {
    return PromoCodeValidation(
      isValid: json['is_valid'] == true,
      message: json['message']?.toString() ?? '',
      estimatedDiscount: (json['estimated_discount'] as num?)?.toDouble(),
      restrictions: json['restrictions'] as Map<String, dynamic>?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'is_valid': isValid,
      'message': message,
      'estimated_discount': estimatedDiscount,
      'restrictions': restrictions,
    };
  }
}

class PromoCodeUsage {
  final String promoCode;
  final DateTime usedAt;
  final double discountAmount;
  final int orderId;
  final String orderStatus;

  PromoCodeUsage({
    required this.promoCode,
    required this.usedAt,
    required this.discountAmount,
    required this.orderId,
    required this.orderStatus,
  });

  factory PromoCodeUsage.fromJson(Map<String, dynamic> json) {
    return PromoCodeUsage(
      promoCode: json['promo_code']?.toString() ?? '',
      usedAt: DateTime.parse(json['used_at']),
      discountAmount: (json['discount_amount'] as num?)?.toDouble() ?? 0.0,
      orderId: json['order_id'] as int? ?? 0,
      orderStatus: json['order_status']?.toString() ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'promo_code': promoCode,
      'used_at': usedAt.toIso8601String(),
      'discount_amount': discountAmount,
      'order_id': orderId,
      'order_status': orderStatus,
    };
  }
} 