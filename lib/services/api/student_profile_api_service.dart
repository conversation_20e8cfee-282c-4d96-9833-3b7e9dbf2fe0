import 'base_api_service.dart';
import 'api_config.dart';
import 'api_response.dart';

/// Student Profile API Service
class StudentProfileApiService {
  /// Save or update student profile
  static Future<ApiResponse<Map<String, dynamic>>> saveStudentProfile({
    required Map<String, dynamic> profileData,
  }) async {
    try {
      final response = await BaseApiService.post<Map<String, dynamic>>(
        ApiConfig.buildServiceUrl('students', '/students/save'),
        body: profileData,
        fromJson: (data) => data as Map<String, dynamic>,
      );
      return response;
    } catch (e) {
      return ApiResponse.error(
        message: 'Failed to save student profile',
        error: ApiError(type: ApiErrorType.unknown, message: e.toString()),
      );
    }
  }

  /// Fetch student profile by ID
  static Future<ApiResponse<Map<String, dynamic>>> getStudentProfile({
    required String studentId,
  }) async {
    try {
      final response = await BaseApiService.get<Map<String, dynamic>>(
        ApiConfig.buildServiceUrl('students', '/students/$studentId'),
        fromJson: (data) => data as Map<String, dynamic>,
      );
      return response;
    } catch (e) {
      return ApiResponse.error(
        message: 'Failed to fetch student profile',
        error: ApiError(type: ApiErrorType.unknown, message: e.toString()),
      );
    }
  }
} 