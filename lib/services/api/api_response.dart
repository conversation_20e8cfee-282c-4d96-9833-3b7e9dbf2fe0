/// Standardized API response wrapper for all StartWell APIs
class ApiResponse<T> {
  final bool success;
  final String message;
  final T? data;
  final int? statusCode;
  final ApiError? error;

  const ApiResponse({
    required this.success,
    required this.message,
    this.data,
    this.statusCode,
    this.error,
  });

  /// Success response factory
  factory ApiResponse.success({
    required T data,
    String message = 'Success',
    int? statusCode,
  }) {
    return ApiResponse<T>(
      success: true,
      message: message,
      data: data,
      statusCode: statusCode,
    );
  }

  /// Error response factory
  factory ApiResponse.error({
    required String message,
    int? statusCode,
    ApiError? error,
  }) {
    return ApiResponse<T>(
      success: false,
      message: message,
      statusCode: statusCode,
      error: error,
    );
  }

  /// Parse from JSON response (follows StartWell API standard)
  factory ApiResponse.fromJson(
    Map<String, dynamic> json,
    T Function(dynamic)? fromJsonT,
  ) {
    try {
      final success = json['success'] == true;
      final message = json['message']?.toString() ?? '';
      
      if (success && json['data'] != null && fromJsonT != null) {
        final data = fromJsonT(json['data']);
        return ApiResponse.success(
          data: data,
          message: message,
          statusCode: json['status_code'],
        );
      } else if (success && fromJsonT == null) {
        // For responses that don't need data parsing
        final data = json['data'];
        if (data == null) {
          // If T is non-nullable, throw or provide a default value
          throw Exception('API response data is null but non-nullable type expected');
        }
        return ApiResponse.success(
          data: data as T,
          message: message,
          statusCode: json['status_code'],
        );
      } else {
        return ApiResponse.error(
          message: message.isEmpty ? 'Unknown error occurred' : message,
          statusCode: json['status_code'],
          error: ApiError.fromJson(json),
        );
      }
    } catch (e) {
      return ApiResponse.error(
        message: 'Failed to parse response: ${e.toString()}',
        error: ApiError(
          type: ApiErrorType.parseError,
          message: e.toString(),
        ),
      );
    }
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'message': message,
      'data': data,
      'status_code': statusCode,
      'error': error?.toJson(),
    };
  }
}

/// API Error model
class ApiError {
  final ApiErrorType type;
  final String message;
  final String? code;
  final Map<String, dynamic>? details;
  final String? stackTrace;

  const ApiError({
    required this.type,
    required this.message,
    this.code,
    this.details,
    this.stackTrace,
  });

  factory ApiError.fromJson(Map<String, dynamic> json) {
    return ApiError(
      type: _getErrorTypeFromJson(json),
      message: json['message']?.toString() ?? 'Unknown error',
      code: json['error_code']?.toString(),
      details: json['details'] as Map<String, dynamic>?,
    );
  }

  static ApiErrorType _getErrorTypeFromJson(Map<String, dynamic> json) {
    final statusCode = json['status_code'] as int?;
    
    if (statusCode != null) {
      if (statusCode >= 400 && statusCode < 500) {
        return ApiErrorType.clientError;
      } else if (statusCode >= 500) {
        return ApiErrorType.serverError;
      }
    }
    
    return ApiErrorType.unknown;
  }

  Map<String, dynamic> toJson() {
    return {
      'type': type.toString(),
      'message': message,
      'code': code,
      'details': details,
      'stack_trace': stackTrace,
    };
  }
}

/// Types of API errors
enum ApiErrorType {
  networkError,
  timeoutError,
  authenticationError,
  authorizationError,
  clientError,
  serverError,
  parseError,
  validationError,
  unknown,
}

/// Extension for error type handling
extension ApiErrorTypeExtension on ApiErrorType {
  String get userFriendlyMessage {
    switch (this) {
      case ApiErrorType.networkError:
        return 'Please check your internet connection and try again.';
      case ApiErrorType.timeoutError:
        return 'Request timed out. Please try again.';
      case ApiErrorType.authenticationError:
        return 'Please log in to continue.';
      case ApiErrorType.authorizationError:
        return 'You don\'t have permission to perform this action.';
      case ApiErrorType.clientError:
        return 'Invalid request. Please check your input.';
      case ApiErrorType.serverError:
        return 'Server error. Please try again later.';
      case ApiErrorType.parseError:
        return 'Failed to process response. Please try again.';
      case ApiErrorType.validationError:
        return 'Please check your input and try again.';
      case ApiErrorType.unknown:
        return 'Something went wrong. Please try again.';
    }
  }

  bool get shouldRetry {
    switch (this) {
      case ApiErrorType.networkError:
      case ApiErrorType.timeoutError:
      case ApiErrorType.serverError:
        return true;
      case ApiErrorType.authenticationError:
      case ApiErrorType.authorizationError:
      case ApiErrorType.clientError:
      case ApiErrorType.parseError:
      case ApiErrorType.validationError:
      case ApiErrorType.unknown:
        return false;
    }
  }
} 