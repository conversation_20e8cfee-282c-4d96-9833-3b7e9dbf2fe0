import 'dart:convert';
import 'package:http/http.dart' as http;

class PlanDuration {
  final int id;
  final String planName;
  final int planQuantity;
  final double? discountPercentage;

  PlanDuration({
    required this.id,
    required this.planName,
    required this.planQuantity,
    this.discountPercentage,
  });

  factory PlanDuration.fromJson(Map<String, dynamic> json) {
    return PlanDuration(
      id: json['id'],
      planName: json['plan_name'] ?? '',
      planQuantity: json['plan_quantity'] ?? 0,
      discountPercentage: json['discount_percentage'] != null ? (json['discount_percentage'] as num?)?.toDouble() : null,
    );
  }
}

class PlanDurationService {
  static Future<List<PlanDuration>> fetchDurations() async {
    print('PlanDurationService.fetchDurations called'); // Debug print
    final response = await http.get(
      Uri.parse('http://192.168.1.167:8001/api/v2/catalogue/meal-plan-durations'),
    );
    print('Meal Plan Durations API response: ' + response.body); // Debug print
    if (response.statusCode == 200) {
      final List data = json.decode(response.body)['data'];
      return data.map((e) => PlanDuration.fromJson(e)).toList();
    } else {
      throw Exception('Failed to load durations');
    }
  }
} 