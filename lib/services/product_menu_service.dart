import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:startwell/models/product_menu_model.dart';

class ProductMenuService {
  static const String baseUrl = 'http://192.168.1.167:8002';
  
  Future<ProductMenuResponse> getProductMenu({int deliveryLocationId = 1}) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/api/test/product-menu?delivery_location_id=$deliveryLocationId'),
        headers: {
          'accept': 'application/json',
          'Content-Type': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonData = json.decode(response.body);
        return ProductMenuResponse.fromJson(jsonData);
      } else {
        throw Exception('Failed to load product menu: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error fetching product menu: $e');
    }
  }

  // Helper method to get breakfast products
  List<Product> getBreakfastProducts(ProductMenuResponse response) {
    final breakfastMealType = response.data.mealTypes
        .where((mealType) => mealType.mealType == 'breakfast')
        .firstOrNull;
    return breakfastMealType?.products ?? [];
  }

  // Helper method to get lunch products
  List<Product> getLunchProducts(ProductMenuResponse response) {
    final lunchMealType = response.data.mealTypes
        .where((mealType) => mealType.mealType == 'lunch')
        .firstOrNull;
    return lunchMealType?.products ?? [];
  }

  // Helper method to convert Product to Map for UI compatibility
  Map<String, dynamic> productToMap(Product product) {
    return {
      'id': product.id,
      'name': product.name,
      'description': product.description,
      'price': product.price,
      'category': product.category,
      'foodType': product.foodType,
      'imagePath': product.imagePath,
      'isSelected': false,
    };
  }
} 