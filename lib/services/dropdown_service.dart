import 'dart:convert';
import 'package:http/http.dart' as http;

class DropdownService {
  static Future<Map<String, List<Map<String, String>>>> fetchDropdowns() async {
    final url = Uri.parse('http://192.168.1.167:8005/api/v2/admin/settings/dropdowns');
    final response = await http.get(
      url,
      headers: {'accept': 'application/json'},
    );
    print('DROPDOWN API STATUS: ' + response.statusCode.toString());
    print('DROPDOWN API RESPONSE: ' + response.body);
    if (response.statusCode == 200) {
      final data = json.decode(response.body);
      final d = data['data'] ?? {};
      return {
        'classes': List<Map<String, String>>.from((d['classes'] ?? []).map((e) => {'label': e['label']?.toString() ?? '', 'value': e['value']?.toString() ?? ''})),
        'divisions': List<Map<String, String>>.from((d['divisions'] ?? []).map((e) => {'label': e['label']?.toString() ?? '', 'value': e['value']?.toString() ?? ''})),
        'floors': List<Map<String, String>>.from((d['floors'] ?? []).map((e) => {'label': e['label']?.toString() ?? '', 'value': e['value']?.toString() ?? ''})),
        'delivery_days': List<Map<String, String>>.from((d['delivery_days'] ?? []).map((e) => {
          'id': e['id']?.toString() ?? '',
          'name': e['name']?.toString() ?? '',
          'short_name': e['short_name']?.toString() ?? '',
          'value': e['value']?.toString() ?? '',
        })),
      };
    } else {
      print('DROPDOWN API ERROR: ' + response.body);
      throw Exception('Failed to load dropdowns');
    }
  }
} 