import 'dart:convert';
import 'package:http/http.dart' as http;
import '../models/promo_code_response.dart';

class PromoCodeService {
  static const String _baseUrl = 'http://192.168.1.167:8002/api/v2/quickserve/orders/apply-coupon';

  Future<PromoCodeResponse> applyPromoCode({
    required String promoCode,
    required int orderId,
    required double orderAmount,
    required List<int> productCodes,
    required int companyId,
    required int unitId,
    required int customerId,
    required int locationId,
    required List<int> planQuantities,
    required List<int> cartQuantities,
    String? planType, // Add plan type parameter
  }) async {
    final requestBody = {
      'promo_code': promoCode,
      'order_id': orderId,
      'order_amount': orderAmount,
      'product_codes': productCodes,
      'company_id': companyId,
      'unit_id': unitId,
      'customer_id': customerId,
      'location_id': locationId,
      'plan_quantity': planQuantities.map((q) => {'quantity': q}).toList(),
      'cart_items': cartQuantities.map((q) => {'quantity': q}).toList(),
    };
    
    // Add plan_type if provided
    if (planType != null) {
      requestBody['plan_type'] = planType;
    }

    final response = await http.post(
      Uri.parse(_baseUrl),
      headers: {
        'accept': 'application/json',
        'Content-Type': 'application/json',
      },
      body: jsonEncode(requestBody),
    );

    if (response.statusCode == 200) {
      return PromoCodeResponse.fromJson(jsonDecode(response.body));
    } else {
      throw Exception('Failed to apply promo code: ${response.body}');
    }
  }
} 