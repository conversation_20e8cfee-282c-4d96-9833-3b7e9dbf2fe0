import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:startwell/models/delivery_location_model.dart';

class DeliveryLocationService {
  static const String baseUrl = 'http://192.168.1.167:8002';
  
  Future<DeliveryLocationsResponse> getDeliveryLocations({
    int perPage = 15,
    int page = 1,
  }) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/api/test/delivery-locations?per_page=$perPage&page=$page'),
        headers: {
          'accept': 'application/json',
          'Content-Type': 'application/json',
        },
      );

      print('Delivery Locations API Response Status: ${response.statusCode}');
      print('Delivery Locations API Response Body: ${response.body}');

      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonData = json.decode(response.body);
        return DeliveryLocationsResponse.fromJson(jsonData);
      } else {
        throw Exception('Failed to load delivery locations: ${response.statusCode} - ${response.body}');
      }
    } catch (e) {
      throw Exception('Error fetching delivery locations: $e');
    }
  }

  // Get all delivery locations (handles pagination automatically)
  Future<List<DeliveryLocation>> getAllDeliveryLocations() async {
    List<DeliveryLocation> allLocations = [];
    int currentPage = 1;
    bool hasMorePages = true;

    while (hasMorePages) {
      try {
        final response = await getDeliveryLocations(perPage: 50, page: currentPage);
        allLocations.addAll(response.data);
        
        if (currentPage >= response.lastPage) {
          hasMorePages = false;
        } else {
          currentPage++;
        }
      } catch (e) {
        print('Error fetching page $currentPage: $e');
        hasMorePages = false;
      }
    }

    return allLocations;
  }

  // Get only active delivery locations
  Future<List<DeliveryLocation>> getActiveDeliveryLocations({
    int perPage = 15,
    int page = 1,
  }) async {
    try {
      final response = await getDeliveryLocations(perPage: perPage, page: page);
      return response.data.where((location) => location.isActive).toList();
    } catch (e) {
      throw Exception('Error fetching active delivery locations: $e');
    }
  }

  // Get all active delivery locations (handles pagination automatically)
  Future<List<DeliveryLocation>> getAllActiveDeliveryLocations() async {
    try {
      final allLocations = await getAllDeliveryLocations();
      return allLocations.where((location) => location.isActive).toList();
    } catch (e) {
      throw Exception('Error fetching all active delivery locations: $e');
    }
  }

  // Search delivery locations by name
  Future<List<DeliveryLocation>> searchDeliveryLocations({
    required String searchTerm,
    int perPage = 15,
    int page = 1,
  }) async {
    try {
      final response = await getDeliveryLocations(perPage: perPage, page: page);
      return response.data.where((location) => 
        location.name.toLowerCase().contains(searchTerm.toLowerCase())
      ).toList();
    } catch (e) {
      throw Exception('Error searching delivery locations: $e');
    }
  }

  // Get delivery location by ID
  Future<DeliveryLocation?> getDeliveryLocationById(int id) async {
    try {
      final response = await getDeliveryLocations(perPage: 1000); // Get a large number to find the specific ID
      return response.data.firstWhere((location) => location.id == id);
    } catch (e) {
      print('Error fetching delivery location by ID $id: $e');
      return null;
    }
  }

  // Helper method to get school names as a simple list of strings
  Future<List<String>> getSchoolNames() async {
    try {
      final locations = await getAllActiveDeliveryLocations();
      return locations.map((location) => location.name).toList();
    } catch (e) {
      throw Exception('Error fetching school names: $e');
    }
  }

  // Helper method to get school names with loading state
  Future<List<String>> getSchoolNamesWithLoading({
    Function(bool)? onLoadingChanged,
  }) async {
    try {
      onLoadingChanged?.call(true);
      final locations = await getAllActiveDeliveryLocations();
      final schoolNames = locations.map((location) => location.name).toList();
      onLoadingChanged?.call(false);
      return schoolNames;
    } catch (e) {
      onLoadingChanged?.call(false);
      throw Exception('Error fetching school names: $e');
    }
  }
} 