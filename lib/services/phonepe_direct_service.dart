import 'package:flutter/services.dart';
import 'dart:developer' as dev;
import 'dart:convert';
import 'dart:math';

class PhonePeDirectService {
  static const MethodChannel _channel = MethodChannel('com.startwell.phonepe/checkout');

  // PhonePe configuration
  static const String merchantId = "STARTWELL001"; // Replace with your merchant ID
  static const String saltKey = "your-salt-key-here"; // Replace with your salt key
  static const int saltIndex = 1;

  /// Check if PhonePe app is installed on the device
  static Future<bool> isPhonePeInstalled() async {
    try {
      final bool isInstalled = await _channel.invokeMethod("isPhonePeInstalled");
      dev.log('PhonePe installed: $isInstalled');
      return isInstalled;
    } on PlatformException catch (e) {
              dev.log("PhonePe installation check error: ${e.message}");
      return false;
    }
  }

  /// Start PhonePe transaction directly without backend
  static Future<PhonePeDirectResponse?> startDirectTransaction({
    required String orderId,
    required double amount,
    required String customerPhone,
    required String customerName,
    String environment = 'UAT', // 'UAT' for testing, 'PROD' for production
  }) async {
    try {
      dev.log('Starting direct PhonePe transaction...');
      dev.log('Order ID: $orderId, Amount: ₹$amount');

      // Generate unique merchant transaction ID
      final String merchantTransactionId = 'MT${DateTime.now().millisecondsSinceEpoch}';

      // Create payment payload
      final Map<String, dynamic> paymentPayload = {
        "merchantId": merchantId,
        "merchantTransactionId": merchantTransactionId,
        "merchantUserId": customerPhone,
        "amount": (amount * 100).toInt(), // Convert to paise
        "redirectUrl": "https://webhook.site/redirect-url",
        "redirectMode": "REDIRECT",
        "callbackUrl": "https://webhook.site/callback-url",
        "mobileNumber": customerPhone,
        "paymentInstrument": {
          "type": "PAY_PAGE"
        }
      };

      // Convert payload to base64
      final String base64Payload = base64Encode(utf8.encode(json.encode(paymentPayload)));
      
      // Create payment URL (simplified for direct integration)
      final String paymentUrl = 'upi://pay?pa=merchant@payu&pn=$customerName&am=$amount&cu=INR&tn=Payment for $orderId';

      // Generate simple checksum (Note: In production, use proper HMAC-SHA256)
      final String checksum = _generateSimpleChecksum(base64Payload, merchantTransactionId);

              dev.log('Payment URL: $paymentUrl');
        dev.log('Merchant Transaction ID: $merchantTransactionId');

      // Start PhonePe transaction
      final Map<dynamic, dynamic> response = await _channel.invokeMethod(
        "startPhonePeTransaction",
        {
          "url": paymentUrl,
          "checksum": checksum,
          "packageName": "com.phonepe.app",
          "environment": environment,
          "merchantTransactionId": merchantTransactionId,
        },
      );

              dev.log('PhonePe direct response: $response');

      return PhonePeDirectResponse.fromMap(response, merchantTransactionId, amount);
    } on PlatformException catch (e) {
              dev.log("PhonePe direct transaction error: ${e.message}");
      return PhonePeDirectResponse(
        status: 'ERROR',
        message: e.message ?? 'Unknown error occurred',
        merchantTransactionId: '',
        amount: amount,
      );
    } catch (e) {
              dev.log("PhonePe direct unexpected error: $e");
      return PhonePeDirectResponse(
        status: 'ERROR',
        message: 'Unexpected error: $e',
        merchantTransactionId: '',
        amount: amount,
      );
    }
  }

  /// Generate simple checksum (replace with proper HMAC-SHA256 in production)
  static String _generateSimpleChecksum(String payload, String merchantTransactionId) {
    // This is a simplified checksum generation
    // In production, implement proper HMAC-SHA256 with your salt key
    final String data = payload + merchantTransactionId + DateTime.now().millisecondsSinceEpoch.toString();
    return data.hashCode.abs().toString();
  }

  /// Generate random order ID
  static String generateOrderId() {
    return 'SW_${DateTime.now().millisecondsSinceEpoch}_${Random().nextInt(1000)}';
  }

  /// Create UPI deep link for direct payment
  static String createUPILink({
    required String payeeVPA,
    required String payeeName,
    required double amount,
    required String transactionNote,
    required String merchantTransactionId,
  }) {
    return 'upi://pay'
        '?pa=$payeeVPA'
        '&pn=${Uri.encodeComponent(payeeName)}'
        '&am=${amount.toStringAsFixed(2)}'
        '&cu=INR'
        '&tn=${Uri.encodeComponent(transactionNote)}'
        '&tr=$merchantTransactionId';
  }
}

class PhonePeDirectResponse {
  final String status;
  final String message;
  final String merchantTransactionId;
  final double amount;
  final String? transactionId;
  final DateTime timestamp;

  PhonePeDirectResponse({
    required this.status,
    required this.message,
    required this.merchantTransactionId,
    required this.amount,
    this.transactionId,
    DateTime? timestamp,
  }) : timestamp = timestamp ?? DateTime.now();

  factory PhonePeDirectResponse.fromMap(
    Map<dynamic, dynamic> map,
    String merchantTransactionId,
    double amount,
  ) {
    return PhonePeDirectResponse(
      status: map['status']?.toString() ?? 'UNKNOWN',
      message: map['message']?.toString() ?? 'No message',
      merchantTransactionId: merchantTransactionId,
      amount: amount,
      transactionId: map['transactionId']?.toString(),
      timestamp: DateTime.now(),
    );
  }

  bool get isSuccess => status == 'PAYMENT_SUCCESS' || status == 'PAYMENT_INITIATED' || status == 'SUCCESS';
  bool get isFailure => status == 'PAYMENT_FAILED' || status == 'ERROR' || status == 'FAILED';
  bool get isPending => status == 'PAYMENT_PENDING' || status == 'PENDING';

  Map<String, dynamic> toJson() {
    return {
      'status': status,
      'message': message,
      'merchantTransactionId': merchantTransactionId,
      'amount': amount,
      'transactionId': transactionId,
      'timestamp': timestamp.toIso8601String(),
    };
  }

  @override
  String toString() {
    return 'PhonePeDirectResponse(status: $status, merchantTransactionId: $merchantTransactionId, amount: ₹$amount)';
  }
}

// PhonePe Configuration class
class PhonePeConfig {
  static const String merchantId = "STARTWELL001"; // Your PhonePe merchant ID
  static const String merchantVPA = "startwell@payu"; // Your UPI VPA
  static const String merchantName = "Startwell";
  
  // Environment URLs
  static const String uatBaseUrl = "https://api-preprod.phonepe.com/apis/hermes";
  static const String prodBaseUrl = "https://api.phonepe.com/apis/hermes";
  
  static String getBaseUrl(String environment) {
    return environment == 'PROD' ? prodBaseUrl : uatBaseUrl;
  }
} 