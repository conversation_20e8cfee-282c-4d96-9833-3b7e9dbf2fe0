/// PhonePe Configuration for different environments
class PhonePeConfig {
  // UAT Environment Configuration
  static const String uatHost = "https://mercury-uat.phonepe.com";
  static const String uatMerchantId = "UATMERCHANT";
  
  // UAT Keys (as provided)
  static const Map<int, String> uatKeys = {
    1: "8289e078-be0b-484d-ae60-052f117f8deb",
    2: "48b7228c-605e-4bab-9632-5bf9e61e8b75",
  };
  
  // Current configuration (using Key Index 1 by default)
  static const String currentMerchantId = uatMerchantId;
  static const String currentSaltKey = "8289e078-be0b-484d-ae60-052f117f8deb";
  static const int currentSaltIndex = 1;
  static const bool isUATEnvironment = true;
  
  // Production configuration (to be updated when going live)
  static const String prodMerchantId = "YOUR_PROD_MERCHANT_ID";
  static const String prodSaltKey = "YOUR_PROD_SALT_KEY";
  static const int prodSaltIndex = 1;
  
  /// Get current environment configuration
  static Map<String, dynamic> getCurrentConfig() {
    return {
      'merchantId': isUATEnvironment ? currentMerchantId : prodMerchantId,
      'saltKey': isUATEnvironment ? currentSaltKey : prodSaltKey,
      'saltIndex': isUATEnvironment ? currentSaltIndex : prodSaltIndex,
      'isUAT': isUATEnvironment,
      'environment': isUATEnvironment ? 'UAT' : 'Production',
    };
  }
} 