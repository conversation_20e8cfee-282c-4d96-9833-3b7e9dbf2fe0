import 'dart:convert';
import 'dart:developer';
import 'package:http/http.dart' as http;

class PaymentGatewayService {
  static const String baseUrl = 'http://192.168.1.167:8005/api/v2/admin/settings';

  // Model for Payment Gateway
  static Future<List<PaymentGateway>> getPaymentGateways() async {
    final stopwatch = Stopwatch()..start();
    const endpoint = '/payment-gateways';
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/payment-gateways'),
        headers: {
          'Content-Type': 'application/json',
        },
      );
      stopwatch.stop();
      log('[API] $endpoint | status: ${response.statusCode} | duration: ${stopwatch.elapsedMilliseconds}ms');
      log('Payment Gateways API Response: ${response.statusCode}');
      log('Payment Gateways Response Body: ${response.body}');

      if (response.statusCode == 200) {
        final Map<String, dynamic> data = json.decode(response.body);
        
        // Handle new response structure with enabled_gateways and gateways
        if (data.containsKey('data')) {
          final responseData = data['data'] as Map<String, dynamic>;
          
          if (responseData.containsKey('enabled_gateways') && responseData.containsKey('gateways')) {
            final List<dynamic> enabledGateways = responseData['enabled_gateways'] as List<dynamic>;
            final Map<String, dynamic> gatewaysConfig = responseData['gateways'] as Map<String, dynamic>;
            
            List<PaymentGateway> gateways = [];
            
            for (String gatewayName in enabledGateways) {
              final config = gatewaysConfig[gatewayName];
              
              // Use the raw response data exactly as it comes from API
              gateways.add(PaymentGateway(
                id: gatewayName,
                name: gatewayName,
                description: gatewayName,
                logoUrl: '',
                isActive: true,
                gatewayType: gatewayName,
                configuration: config is Map<String, dynamic> ? config : {},
              ));
            }
            
            return gateways;
          }
        }
        
        // Handle old response structures as fallback
        List<dynamic> gatewaysData;
        if (data.containsKey('data') && data['data'] is List) {
          gatewaysData = data['data'] as List<dynamic>;
        } else if (data.containsKey('payment_gateways')) {
          gatewaysData = data['payment_gateways'] as List<dynamic>;
        } else if (data is List) {
          gatewaysData = data as List<dynamic>;
        } else {
          throw Exception('Unexpected response structure');
        }

        return gatewaysData
            .map((gateway) => PaymentGateway.fromJson(gateway))
            .where((gateway) => gateway.isActive) // Only return active gateways
            .toList();
      } else {
        log('[API][ERROR] $endpoint | status: ${response.statusCode} | duration: ${stopwatch.elapsedMilliseconds}ms | error: ${response.body}');
        throw Exception('Failed to load payment gateways: ${response.statusCode}');
      }
    } catch (e) {
      stopwatch.stop();
      log('[API][ERROR] $endpoint | duration: ${stopwatch.elapsedMilliseconds}ms | exception: $e');
      // Return empty list if API fails - no fallback defaults
      return [];
    }
  }

  // Use gateway names as-is from API without any derivation
}

class PaymentGateway {
  final String id;
  final String name;
  final String description;
  final String logoUrl;
  final bool isActive;
  final String gatewayType;
  final Map<String, dynamic>? configuration;

  PaymentGateway({
    required this.id,
    required this.name,
    required this.description,
    required this.logoUrl,
    required this.isActive,
    required this.gatewayType,
    this.configuration,
  });

  factory PaymentGateway.fromJson(Map<String, dynamic> json) {
    return PaymentGateway(
      id: json['id']?.toString() ?? '',
      name: json['name'] ?? '',
      description: json['description'] ?? '',
      logoUrl: json['logo_url'] ?? json['logo'] ?? '',
      isActive: json['is_active'] ?? json['active'] ?? true,
      gatewayType: json['gateway_type'] ?? json['type'] ?? 'gateway',
      configuration: json['configuration'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'logo_url': logoUrl,
      'is_active': isActive,
      'gateway_type': gatewayType,
      'configuration': configuration,
    };
  }
} 