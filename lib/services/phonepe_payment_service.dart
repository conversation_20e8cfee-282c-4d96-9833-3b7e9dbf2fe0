import 'dart:convert';
import 'dart:developer';
import 'package:http/http.dart' as http;

class PhonePePaymentService {
  static const String baseUrl = 'http://192.168.1.167:8005/api/v2/payment';

  /// Initiate PhonePe payment through backend
  static Future<PhonePePaymentResponse> initiatePayment({
    required String orderId,
    required double amount,
    required int customerId,
    required String customerPhone,
    required String customerName,
    String currency = 'INR',
  }) async {
    try {
      log('Initiating PhonePe payment for order: $orderId, amount: $amount');

      final Map<String, dynamic> requestBody = {
        'orderId': orderId,
        'amount': (amount * 100).toInt(), // Convert to paise
        'currency': currency,
        'customerId': customerId.toString(),
        'customerPhone': customerPhone,
        'customerName': customerName,
        'paymentGateway': 'phonepe',
        'callbackUrl': 'https://webhook.site/callback-url', // Replace with your callback URL
        'redirectUrl': 'https://your-app.com/payment-success', // Replace with your redirect URL
      };

      log('PhonePe payment request: $requestBody');

      final response = await http.post(
        Uri.parse('$baseUrl/phonepe/initiate'),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: json.encode(requestBody),
      );

      log('PhonePe API response status: ${response.statusCode}');
      log('PhonePe API response body: ${response.body}');

      if (response.statusCode == 200) {
        final Map<String, dynamic> responseData = json.decode(response.body);
        
        if (responseData['success'] == true) {
          return PhonePePaymentResponse.fromJson(responseData['data']);
        } else {
          throw Exception(responseData['message'] ?? 'Payment initiation failed');
        }
      } else {
        throw Exception('HTTP ${response.statusCode}: ${response.body}');
      }
    } catch (e) {
      log('PhonePe payment initiation error: $e');
      throw Exception('Failed to initiate PhonePe payment: $e');
    }
  }

  /// Check payment status
  static Future<PhonePePaymentStatus> checkPaymentStatus({
    required String merchantTransactionId,
  }) async {
    try {
      log('Checking PhonePe payment status for: $merchantTransactionId');

      final response = await http.get(
        Uri.parse('$baseUrl/phonepe/status/$merchantTransactionId'),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
      );

      log('PhonePe status check response: ${response.statusCode}');
      log('PhonePe status response body: ${response.body}');

      if (response.statusCode == 200) {
        final Map<String, dynamic> responseData = json.decode(response.body);
        return PhonePePaymentStatus.fromJson(responseData['data']);
      } else {
        throw Exception('HTTP ${response.statusCode}: ${response.body}');
      }
    } catch (e) {
      log('PhonePe status check error: $e');
      throw Exception('Failed to check payment status: $e');
    }
  }
}

class PhonePePaymentResponse {
  final String merchantTransactionId;
  final String paymentUrl;
  final String checksum;
  final String status;
  final String? message;

  PhonePePaymentResponse({
    required this.merchantTransactionId,
    required this.paymentUrl,
    required this.checksum,
    required this.status,
    this.message,
  });

  factory PhonePePaymentResponse.fromJson(Map<String, dynamic> json) {
    return PhonePePaymentResponse(
      merchantTransactionId: json['merchantTransactionId'] ?? '',
      paymentUrl: json['paymentUrl'] ?? json['url'] ?? '',
      checksum: json['checksum'] ?? json['xVerify'] ?? '',
      status: json['status'] ?? 'PENDING',
      message: json['message'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'merchantTransactionId': merchantTransactionId,
      'paymentUrl': paymentUrl,
      'checksum': checksum,
      'status': status,
      'message': message,
    };
  }

  @override
  String toString() {
    return 'PhonePePaymentResponse(merchantTransactionId: $merchantTransactionId, paymentUrl: $paymentUrl, status: $status)';
  }
}

class PhonePePaymentStatus {
  final String merchantTransactionId;
  final String transactionId;
  final String status;
  final double amount;
  final String currency;
  final DateTime? paymentDate;
  final String? message;

  PhonePePaymentStatus({
    required this.merchantTransactionId,
    required this.transactionId,
    required this.status,
    required this.amount,
    required this.currency,
    this.paymentDate,
    this.message,
  });

  factory PhonePePaymentStatus.fromJson(Map<String, dynamic> json) {
    return PhonePePaymentStatus(
      merchantTransactionId: json['merchantTransactionId'] ?? '',
      transactionId: json['transactionId'] ?? json['phonepeTransactionId'] ?? '',
      status: json['status'] ?? 'UNKNOWN',
      amount: (json['amount'] ?? 0).toDouble() / 100, // Convert from paise to rupees
      currency: json['currency'] ?? 'INR',
      paymentDate: json['paymentDate'] != null 
          ? DateTime.tryParse(json['paymentDate'].toString())
          : null,
      message: json['message'],
    );
  }

  bool get isSuccess => status == 'PAYMENT_SUCCESS' || status == 'SUCCESS';
  bool get isFailure => status == 'PAYMENT_FAILED' || status == 'FAILED';
  bool get isPending => status == 'PAYMENT_PENDING' || status == 'PENDING';

  @override
  String toString() {
    return 'PhonePePaymentStatus(transactionId: $transactionId, status: $status, amount: $amount)';
  }
} 