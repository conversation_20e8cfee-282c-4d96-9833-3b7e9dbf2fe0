import 'dart:developer' as dev;
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'package:intl/intl.dart';

class WorkingDaysService {
  static const String baseUrl =
      'http://192.168.1.167:8005/api/v2/admin/settings';

  // Static values for now, will be dynamic later
  static const int companyId = 8163;
  static const int kitchenId = 1;

  /// Get working days for a specific month and meal type
  /// Returns a list of enabled dates for the calendar
  static Future<WorkingDaysResponse> getWorkingDays({
    required String mealType, // 'breakfast', 'lunch', 'dinner'
    DateTime? monthSelected,
    List<int>? customDays, // Optional custom days (1-7, where 1=Monday)
  }) async {
    try {
      // Use current month if not specified
      monthSelected ??= DateTime.now();

      // Format month as YYYY-MM
      final String formattedMonth = DateFormat('yyyy-MM').format(monthSelected);

      // Build base URL
      String url = '$baseUrl/working-days';

      // Build query parameters
      final List<String> queryParts = [
        'company_id=$companyId',
        'month_selected=$formattedMonth',
        'kitchen_id=$kitchenId',
        'meal_type=${mealType.toLowerCase()}',
      ];

      // Add custom days if provided - using the correct format days[]=1&days[]=3
      if (customDays != null && customDays.isNotEmpty) {
        for (int day in customDays) {
          queryParts.add('days[]=$day');
        }
      }

      // Combine URL with query parameters
      final String fullUrl = '$url?${queryParts.join('&')}';
      final Uri uri = Uri.parse(fullUrl);

      dev.log('🌐 Fetching working days from: $uri');

      final response = await http.get(
        uri,
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
      );

      dev.log('📡 Working days API response status: ${response.statusCode}');
      dev.log('📡 Working days API response body: ${response.body}');

      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonData = json.decode(response.body);
        return WorkingDaysResponse.fromJson(jsonData);
      } else {
        throw Exception(
            'Failed to load working days: ${response.statusCode} - ${response.body}');
      }
    } catch (e) {
      dev.log('❌ Error fetching working days: $e');
      throw Exception('Error fetching working days: $e');
    }
  }

  /// Get working days for current month
  static Future<WorkingDaysResponse> getCurrentMonthWorkingDays({
    required String mealType,
    List<int>? customDays,
  }) async {
    return getWorkingDays(
      mealType: mealType,
      monthSelected: DateTime.now(),
      customDays: customDays,
    );
  }

  /// Get working days for next month
  static Future<WorkingDaysResponse> getNextMonthWorkingDays({
    required String mealType,
    List<int>? customDays,
  }) async {
    final now = DateTime.now();
    final nextMonth = DateTime(now.year, now.month + 1, 1);

    return getWorkingDays(
      mealType: mealType,
      monthSelected: nextMonth,
      customDays: customDays,
    );
  }

  /// Convert selected weekdays boolean array to custom days list
  /// selectedWeekdays[0] = Monday, selectedWeekdays[1] = Tuesday, etc.
  static List<int> convertSelectedWeekdaysToCustomDays(
      List<bool> selectedWeekdays) {
    List<int> customDays = [];

    for (int i = 0; i < selectedWeekdays.length && i < 7; i++) {
      if (selectedWeekdays[i]) {
        customDays.add(i + 1); // Convert 0-based to 1-based (1=Monday)
      }
    }

    return customDays;
  }

  /// Check if a specific date is a working day
  static bool isWorkingDay(DateTime date, WorkingDaysResponse workingDays) {
    final dateOnly = DateTime(date.year, date.month, date.day);
    return workingDays.enabledDates.any((enabledDate) {
      final enabledDateOnly =
          DateTime(enabledDate.year, enabledDate.month, enabledDate.day);
      return enabledDateOnly == dateOnly;
    });
  }

  /// Get working days for a specific date range
  static Future<WorkingDaysResponse> getWorkingDaysForDateRange({
    required String mealType,
    required DateTime startDate,
    required DateTime endDate,
    List<int>? customDays,
  }) async {
    try {
      final List<DateTime> allEnabledDates = [];

      // Get working days for each month in the range
      DateTime currentMonth = DateTime(startDate.year, startDate.month, 1);
      final DateTime endMonth = DateTime(endDate.year, endDate.month, 1);

      while (currentMonth.isBefore(endMonth) ||
          currentMonth.isAtSameMomentAs(endMonth)) {
        final response = await getWorkingDays(
          mealType: mealType,
          monthSelected: currentMonth,
          customDays: customDays,
        );

        // Filter dates to only include those in our range
        final filteredDates = response.enabledDates.where((date) {
          final dateOnly = DateTime(date.year, date.month, date.day);
          final startOnly =
              DateTime(startDate.year, startDate.month, startDate.day);
          final endOnly = DateTime(endDate.year, endDate.month, endDate.day);

          return (dateOnly.isAfter(startOnly) ||
                  dateOnly.isAtSameMomentAs(startOnly)) &&
              (dateOnly.isBefore(endOnly) ||
                  dateOnly.isAtSameMomentAs(endOnly));
        }).toList();

        allEnabledDates.addAll(filteredDates);

        // Move to next month
        currentMonth = DateTime(currentMonth.year, currentMonth.month + 1, 1);
      }

      return WorkingDaysResponse(
        success: true,
        message: 'Working days loaded for date range',
        enabledDates: allEnabledDates,
        disabledDates: [],
        metadata: {
          'start_date': startDate.toIso8601String(),
          'end_date': endDate.toIso8601String(),
          'meal_type': mealType,
          'custom_days': customDays,
        },
      );
    } catch (e) {
      dev.log('❌ Error getting working days for date range: $e');
      throw Exception('Error getting working days for date range: $e');
    }
  }

  /// Validate if the API configuration is correct
  static bool validateApiConfiguration() {
    try {
      final uri = Uri.parse('$baseUrl/working-days');
      return uri.isAbsolute && companyId > 0 && kitchenId > 0;
    } catch (e) {
      dev.log('❌ Invalid API configuration: $e');
      return false;
    }
  }
}

/// Response model for working days API
class WorkingDaysResponse {
  final bool success;
  final String message;
  final List<DateTime> enabledDates;
  final List<DateTime> disabledDates;
  final Map<String, dynamic>? metadata;

  WorkingDaysResponse({
    required this.success,
    required this.message,
    required this.enabledDates,
    required this.disabledDates,
    this.metadata,
  });

  factory WorkingDaysResponse.fromJson(Map<String, dynamic> json) {
    try {
      dev.log('🔍 Parsing WorkingDaysResponse from JSON: $json');

      // Handle different possible API response structures
      final data = json['data'] as Map<String, dynamic>?;
      final workingDays = data?['working_days'] as List<dynamic>? ??
          json['working_days'] as List<dynamic>? ??
          data?['enabled_dates'] as List<dynamic>? ??
          json['enabled_dates'] as List<dynamic>? ??
          [];

      final success = json['success'] ?? json['status'] == 'success' ?? true;
      final message = json['message']?.toString() ??
          json['msg']?.toString() ??
          'Working days loaded successfully';

      final enabledDates = _parseDates(workingDays);
      dev.log(
          '✅ Parsed ${enabledDates.length} enabled dates from API response');

      return WorkingDaysResponse(
        success: success,
        message: message,
        enabledDates: enabledDates,
        disabledDates: [], // API doesn't provide disabled dates in current format
        metadata: data ?? json,
      );
    } catch (e) {
      dev.log('❌ Error parsing WorkingDaysResponse: $e');
      // Return a fallback response
      return WorkingDaysResponse(
        success: false,
        message: 'Error parsing API response: $e',
        enabledDates: [],
        disabledDates: [],
        metadata: json,
      );
    }
  }

  static List<DateTime> _parseDates(dynamic dates) {
    if (dates == null) return [];

    try {
      if (dates is List) {
        final List<DateTime> parsedDates = [];

        for (var date in dates) {
          DateTime? parsedDate;

          if (date is String) {
            // Try different date formats
            parsedDate =
                DateTime.tryParse(date) ?? _tryParseCustomDateFormat(date);
          } else if (date is Map<String, dynamic>) {
            // Handle object format like {"date": "2025-07-22", "enabled": true}
            final dateStr = date['date']?.toString() ?? date['day']?.toString();
            if (dateStr != null) {
              parsedDate = DateTime.tryParse(dateStr) ??
                  _tryParseCustomDateFormat(dateStr);
            }
          } else {
            // Try to parse as string
            parsedDate = DateTime.tryParse(date.toString());
          }

          if (parsedDate != null) {
            parsedDates.add(parsedDate);
          } else {
            dev.log('⚠️ Could not parse date: $date');
          }
        }

        dev.log(
            '📅 Successfully parsed ${parsedDates.length} dates from ${dates.length} items');
        return parsedDates;
      }
      return [];
    } catch (e) {
      dev.log('❌ Error parsing dates: $e');
      return [];
    }
  }

  /// Try to parse custom date formats that might be used by the API
  static DateTime? _tryParseCustomDateFormat(String dateStr) {
    try {
      // Try common date formats
      final formats = [
        'yyyy-MM-dd',
        'dd/MM/yyyy',
        'MM/dd/yyyy',
        'dd-MM-yyyy',
        'yyyy/MM/dd',
      ];

      for (String format in formats) {
        try {
          final formatter = DateFormat(format);
          return formatter.parse(dateStr);
        } catch (e) {
          // Continue to next format
        }
      }

      return null;
    } catch (e) {
      return null;
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'message': message,
      'enabled_dates':
          enabledDates.map((date) => date.toIso8601String()).toList(),
      'disabled_dates':
          disabledDates.map((date) => date.toIso8601String()).toList(),
      'metadata': metadata,
    };
  }

  @override
  String toString() {
    return 'WorkingDaysResponse(success: $success, message: $message, enabledDates: ${enabledDates.length}, disabledDates: ${disabledDates.length})';
  }
}
