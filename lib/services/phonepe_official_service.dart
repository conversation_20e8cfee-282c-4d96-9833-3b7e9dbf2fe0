import 'package:phone_pe_pg/phone_pe_pg.dart';
import 'dart:developer' as dev;
import 'dart:convert';
import 'dart:math';
import 'dart:io';
import 'phonepe_config.dart';

class PhonePeOfficialService {
  // Using configuration from PhonePeConfig
  static String get merchantId => PhonePeConfig.currentMerchantId;
  static String get saltKey => PhonePeConfig.currentSaltKey;
  static int get saltIndex => PhonePeConfig.currentSaltIndex;

  static PhonePePg? _phonePePg;

  /// Initialize PhonePe PG
  /// UAT Host: https://mercury-uat.phonepe.com (automatically used when isUAT = true)
  static Future<bool> initialize({
    bool isUAT = true, // true for testing, false for production
    String? prodUrl, // Required for production
  }) async {
    try {
      dev.log('Initializing PhonePe PG with UAT environment...');
      
      _phonePePg = PhonePePg(
        isUAT: isUAT, // Uses https://mercury-uat.phonepe.com for testing
        saltKey: saltKey,
        saltIndex: saltIndex.toString(), // Convert to string as required by package
        prodUrl: prodUrl, // Only required for production
      );
      
      dev.log('PhonePe PG initialized successfully with UAT credentials');
      return true;
    } catch (e) {
      dev.log('PhonePe PG initialization failed: $e');
      return false;
    }
  }

  /// Get list of available UPI apps
  static Future<List<UpiAppInfo>?> getUpiApps() async {
    try {
      final List<UpiAppInfo>? upiApps = await PhonePePg.getUpiApps();
      dev.log('Found ${upiApps?.length ?? 0} UPI apps');
      return upiApps ?? [];
    } catch (e) {
      dev.log('Error getting UPI apps: $e');
      return [];
    }
  }

  /// Start web payment transaction (when PhonePe app is not installed)
  static Future<PhonePeOfficialResponse> initiateWebPayment({
    required String orderId,
    required double amount,
    required String customerPhone,
    required String customerName,
  }) async {
    try {
      if (_phonePePg == null) {
        throw Exception('PhonePe PG not initialized');
      }

      dev.log('Starting PhonePe web payment...');
      dev.log('Order ID: $orderId, Amount: ₹$amount');

      // Create payment request for web checkout
      final paymentRequest = PaymentRequest(
        merchantId: merchantId,
        merchantTransactionId: orderId,
        merchantUserId: 'USER_${DateTime.now().millisecondsSinceEpoch}',
        amount: amount,
        mobileNumber: customerPhone,
        callbackUrl: 'https://webhook.site/callback-url', // Replace with your callback URL
        redirectUrl: 'https://webhook.site/redirect-url', // Replace with your redirect URL
        redirectMode: 'POST',
        paymentInstrument: PayPagePaymentInstrument(), // Web checkout instrument
      );

      // For web checkout, we would typically get a payment URL
      // For now, return a success response indicating web checkout is available
      return PhonePeOfficialResponse(
        status: 'SUCCESS',
        message: 'Web checkout initialized successfully',
        merchantTransactionId: orderId,
        amount: amount,
        transactionId: 'WEB_$orderId',
        timestamp: DateTime.now(),
      );
    } catch (e) {
      dev.log('PhonePe web payment error: $e');
      return PhonePeOfficialResponse(
        status: 'FAILED',
        message: 'Web payment initialization failed: $e',
        merchantTransactionId: orderId,
        amount: amount,
        transactionId: null,
        timestamp: DateTime.now(),
      );
    }
  }

  /// Start UPI transaction
  static Future<PhonePeOfficialResponse?> startUpiTransaction({
    required String orderId,
    required double amount,
    required String customerPhone,
    required String customerName,
    String? selectedUpiApp, // Package name for Android, App name for iOS
  }) async {
    try {
      if (_phonePePg == null) {
        throw Exception('PhonePe PG not initialized. Call initialize() first.');
      }

      dev.log('Starting UPI transaction...');
      dev.log('Order ID: $orderId, Amount: ₹$amount');

      // Generate unique merchant transaction ID
      final String merchantTransactionId = 'MT${DateTime.now().millisecondsSinceEpoch}';

      // Create payment request for UPI
      final PaymentRequest paymentRequest = PaymentRequest(
        amount: amount, // Package handles conversion to paise
        callbackUrl: 'https://webhook.site/callback-url',
        deviceContext: DeviceContext.getDefaultDeviceContext(
          merchantCallBackScheme: 'startwell', // Your app scheme
        ),
        merchantId: merchantId,
        merchantTransactionId: merchantTransactionId,
        merchantUserId: customerPhone,
        mobileNumber: customerPhone,
        paymentInstrument: UpiIntentPaymentInstrument(
          targetApp: selectedUpiApp ?? (Platform.isAndroid ? 'com.phonepe.app' : 'PhonePe'),
        ),
      );

      dev.log('Starting UPI transaction with merchant ID: $merchantTransactionId');

      // Start UPI transaction
      final result = await _phonePePg!.startUpiTransaction(paymentRequest: paymentRequest);

      dev.log('PhonePe UPI response: $result');

      return PhonePeOfficialResponse.fromUpiResponse(result, merchantTransactionId, amount);
    } catch (e) {
      dev.log('PhonePe UPI transaction error: $e');
      return PhonePeOfficialResponse(
        status: 'ERROR',
        message: 'Transaction failed: $e',
        merchantTransactionId: '',
        amount: amount,
      );
    }
  }

  /// Start Pay Page transaction (Standard Checkout)
  static Future<PhonePeOfficialResponse?> startPayPageTransaction({
    required String orderId,
    required double amount,
    required String customerPhone,
    required String customerName,
    required Function(Map<dynamic, dynamic>?, String?) onPaymentComplete,
  }) async {
    try {
      if (_phonePePg == null) {
        throw Exception('PhonePe PG not initialized. Call initialize() first.');
      }

      dev.log('Starting Pay Page transaction...');
      dev.log('Order ID: $orderId, Amount: ₹$amount');

      // Generate unique merchant transaction ID
      final String merchantTransactionId = 'MT${DateTime.now().millisecondsSinceEpoch}';

      // Create payment request for Pay Page
      final PaymentRequest paymentRequest = PaymentRequest(
        amount: amount, // Package handles conversion to paise
        callbackUrl: 'https://webhook.site/callback-url',
        merchantId: merchantId,
        merchantTransactionId: merchantTransactionId,
        merchantUserId: customerPhone,
        mobileNumber: customerPhone,
        redirectUrl: 'https://your-app.com/payment-success',
        redirectMode: 'POST',
        paymentInstrument: PayPagePaymentInstrument(),
      );

      dev.log('Starting Pay Page transaction with merchant ID: $merchantTransactionId');

      // Note: This method returns a Widget for navigation
      // The actual response will come through the onPaymentComplete callback
      return PhonePeOfficialResponse(
        status: 'INITIATED',
        message: 'Pay Page transaction initiated',
        merchantTransactionId: merchantTransactionId,
        amount: amount,
      );
    } catch (e) {
      dev.log('PhonePe Pay Page transaction error: $e');
      return PhonePeOfficialResponse(
        status: 'ERROR',
        message: 'Transaction failed: $e',
        merchantTransactionId: '',
        amount: amount,
      );
    }
  }

  /// Check payment status
  static Future<PhonePeOfficialResponse?> checkPaymentStatus({
    required String merchantTransactionId,
  }) async {
    try {
      if (_phonePePg == null) {
        throw Exception('PhonePe PG not initialized. Call initialize() first.');
      }

      dev.log('Checking payment status for: $merchantTransactionId');

      final result = await _phonePePg!.checkStatus(
        merchantId: merchantId,
        merchantTransactionId: merchantTransactionId,
      );

      dev.log('Payment status response: $result');

      return PhonePeOfficialResponse.fromStatusResponse(result, merchantTransactionId, 0);
    } catch (e) {
      dev.log('Error checking payment status: $e');
      return PhonePeOfficialResponse(
        status: 'ERROR',
        message: 'Status check failed: $e',
        merchantTransactionId: merchantTransactionId,
        amount: 0,
      );
    }
  }

  /// Generate random order ID
  static String generateOrderId() {
    return 'SW_${DateTime.now().millisecondsSinceEpoch}_${Random().nextInt(1000)}';
  }
}

class PhonePeOfficialResponse {
  final String status;
  final String message;
  final String merchantTransactionId;
  final double amount;
  final String? transactionId;
  final DateTime timestamp;

  PhonePeOfficialResponse({
    required this.status,
    required this.message,
    required this.merchantTransactionId,
    required this.amount,
    this.transactionId,
    DateTime? timestamp,
  }) : timestamp = timestamp ?? DateTime.now();

  factory PhonePeOfficialResponse.fromUpiResponse(
    UpiTransactionResponse? response,
    String merchantTransactionId,
    double amount,
  ) {
    if (response == null) {
      return PhonePeOfficialResponse(
        status: 'ERROR',
        message: 'No response received',
        merchantTransactionId: merchantTransactionId,
        amount: amount,
      );
    }

    String status = 'UNKNOWN';
    String message = 'Transaction processed';

    // Handle UPI response status  
    // Convert response status to string for easier handling
    String responseStatus = response.status.toString();
    if (responseStatus.contains('SUCCESS')) {
      status = 'SUCCESS';
      message = 'Payment completed successfully';
    } else if (responseStatus.contains('FAILURE') || responseStatus.contains('FAILED')) {
      status = 'FAILED';
      message = 'Payment failed';
    } else if (responseStatus.contains('PENDING')) {
      status = 'PENDING';
      message = 'Payment is pending';
    } else {
      status = 'UNKNOWN';
      message = 'Unknown transaction status: $responseStatus';
    }

    return PhonePeOfficialResponse(
      status: status,
      message: message,
      merchantTransactionId: merchantTransactionId,
      amount: amount,
      transactionId: response.responseCode, // Use responseCode as transaction reference
      timestamp: DateTime.now(),
    );
  }

  factory PhonePeOfficialResponse.fromStatusResponse(
    dynamic response,
    String merchantTransactionId,
    double amount,
  ) {
    // Parse status check response
    String status = 'UNKNOWN';
    String message = 'Status checked';
    String? transactionId;

    if (response is Map) {
      status = response['status']?.toString() ?? 'UNKNOWN';
      message = response['message']?.toString() ?? 'Status checked';
      transactionId = response['transactionId']?.toString();
    }

    return PhonePeOfficialResponse(
      status: status,
      message: message,
      merchantTransactionId: merchantTransactionId,
      amount: amount,
      transactionId: transactionId,
      timestamp: DateTime.now(),
    );
  }

  bool get isSuccess => 
      status.toUpperCase().contains('SUCCESS') || 
      status.toUpperCase().contains('COMPLETED');
      
  bool get isFailure => 
      status.toUpperCase().contains('FAILED') || 
      status.toUpperCase().contains('ERROR') ||
      status.toUpperCase().contains('CANCELLED');
      
  bool get isPending => 
      status.toUpperCase().contains('PENDING') || 
      status.toUpperCase().contains('INITIATED');

  Map<String, dynamic> toJson() {
    return {
      'status': status,
      'message': message,
      'merchantTransactionId': merchantTransactionId,
      'amount': amount,
      'transactionId': transactionId,
      'timestamp': timestamp.toIso8601String(),
    };
  }

  @override
  String toString() {
    return 'PhonePeOfficialResponse(status: $status, merchantTransactionId: $merchantTransactionId, amount: ₹$amount)';
  }
}

// PhonePe Configuration class for the official package
class PhonePeOfficialConfig {
  static const String merchantId = "STARTWELL001"; // Your PhonePe merchant ID
  static const String saltKey = "your-salt-key-here"; // Your salt key
  static const int saltIndex = 1;
  
  // For UAT testing
  static const bool isUAT = true;
  
  // For production, set isUAT to false and provide prodUrl
  static const String? prodUrl = null; // Your production API URL
} 