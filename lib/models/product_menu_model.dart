class ProductMenuResponse {
  final bool success;
  final String message;
  final ProductMenuData data;

  ProductMenuResponse({
    required this.success,
    required this.message,
    required this.data,
  });

  factory ProductMenuResponse.fromJson(Map<String, dynamic> json) {
    return ProductMenuResponse(
      success: json['success'] ?? false,
      message: json['message'] ?? '',
      data: ProductMenuData.fromJson(json['data'] ?? {}),
    );
  }
}

class ProductMenuData {
  final DeliveryLocation deliveryLocation;
  final Kitchen kitchen;
  final String menuType;
  final List<ProductMealType> mealTypes;
  final Summary summary;

  ProductMenuData({
    required this.deliveryLocation,
    required this.kitchen,
    required this.menuType,
    required this.mealTypes,
    required this.summary,
  });

  factory ProductMenuData.fromJson(Map<String, dynamic> json) {
    return ProductMenuData(
      deliveryLocation: DeliveryLocation.fromJson(json['delivery_location'] ?? {}),
      kitchen: Kitchen.fromJson(json['kitchen'] ?? {}),
      menuType: json['menu_type'] ?? '',
      mealTypes: (json['meal_types'] as List<dynamic>?)
              ?.map((e) => ProductMealType.fromJson(e))
              .toList() ??
          [],
      summary: Summary.fromJson(json['summary'] ?? {}),
    );
  }
}

class DeliveryLocation {
  final int id;
  final String name;
  final String city;
  final int fkKitchenCode;

  DeliveryLocation({
    required this.id,
    required this.name,
    required this.city,
    required this.fkKitchenCode,
  });

  factory DeliveryLocation.fromJson(Map<String, dynamic> json) {
    return DeliveryLocation(
      id: json['id'] ?? 0,
      name: json['name'] ?? '',
      city: json['city'] ?? '',
      fkKitchenCode: json['fk_kitchen_code'] ?? 0,
    );
  }
}

class Kitchen {
  final int code;
  final String name;
  final String alias;

  Kitchen({
    required this.code,
    required this.name,
    required this.alias,
  });

  factory Kitchen.fromJson(Map<String, dynamic> json) {
    return Kitchen(
      code: json['code'] ?? 0,
      name: json['name'] ?? '',
      alias: json['alias'] ?? '',
    );
  }
}

class ProductMealType {
  final String mealType;
  final String mealTypeDisplay;
  final int productCount;
  final List<Product> products;

  ProductMealType({
    required this.mealType,
    required this.mealTypeDisplay,
    required this.productCount,
    required this.products,
  });

  factory ProductMealType.fromJson(Map<String, dynamic> json) {
    return ProductMealType(
      mealType: json['meal_type'] ?? '',
      mealTypeDisplay: json['meal_type_display'] ?? '',
      productCount: json['product_count'] ?? 0,
      products: (json['products'] as List<dynamic>?)
              ?.map((e) => Product.fromJson(e))
              .toList() ??
          [],
    );
  }
}

class Product {
  final int id;
  final String name;
  final String description;
  final String unitPrice;
  final String category;
  final String productType;
  final String foodType;
  final String imagePath;
  final int isCustom;
  final int? sequence;
  final String productCategoryName;

  Product({
    required this.id,
    required this.name,
    required this.description,
    required this.unitPrice,
    required this.category,
    required this.productType,
    required this.foodType,
    required this.imagePath,
    required this.isCustom,
    this.sequence,
    required this.productCategoryName,
  });

  factory Product.fromJson(Map<String, dynamic> json) {
    return Product(
      id: json['id'] ?? 0,
      name: json['name'] ?? '',
      description: json['description'] ?? '',
      unitPrice: json['unit_price'] ?? '0.00',
      category: json['category'] ?? '',
      productType: json['product_type'] ?? '',
      foodType: json['food_type'] ?? '',
      imagePath: json['image_path'] ?? '',
      isCustom: json['is_custom'] ?? 0,
      sequence: json['sequence'],
      productCategoryName: json['product_category_name'] ?? '',
    );
  }

  double get price => double.tryParse(unitPrice) ?? 0.0;
}

class Summary {
  final int totalMealTypes;
  final int totalProducts;

  Summary({
    required this.totalMealTypes,
    required this.totalProducts,
  });

  factory Summary.fromJson(Map<String, dynamic> json) {
    return Summary(
      totalMealTypes: json['total_meal_types'] ?? 0,
      totalProducts: json['total_products'] ?? 0,
    );
  }
} 