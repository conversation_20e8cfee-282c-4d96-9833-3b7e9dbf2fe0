class DeliveryLocation {
  final int pkLocationCode;
  final int companyId;
  final int unitId;
  final int? fkKitchenCode;
  final String location;
  final String city;
  final String? pin;
  final String? subCityArea;
  final String isDefault;
  final int status;
  final String deliveryCharges;
  final String deliveryTime;

  DeliveryLocation({
    required this.pkLocationCode,
    required this.companyId,
    required this.unitId,
    this.fkKitchenCode,
    required this.location,
    required this.city,
    this.pin,
    this.subCityArea,
    required this.isDefault,
    required this.status,
    required this.deliveryCharges,
    required this.deliveryTime,
  });

  factory DeliveryLocation.fromJson(Map<String, dynamic> json) {
    return DeliveryLocation(
      pkLocationCode: json['pk_location_code'] ?? 0,
      companyId: json['company_id'] ?? 0,
      unitId: json['unit_id'] ?? 0,
      fkKitchenCode: json['fk_kitchen_code'],
      location: json['location'] ?? '',
      city: json['city'] ?? '',
      pin: json['pin'],
      subCityArea: json['sub_city_area'],
      isDefault: json['is_default'] ?? '0',
      status: json['status'] ?? 0,
      deliveryCharges: json['delivery_charges'] ?? '0.00',
      deliveryTime: json['delivery_time'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'pk_location_code': pkLocationCode,
      'company_id': companyId,
      'unit_id': unitId,
      'fk_kitchen_code': fkKitchenCode,
      'location': location,
      'city': city,
      'pin': pin,
      'sub_city_area': subCityArea,
      'is_default': isDefault,
      'status': status,
      'delivery_charges': deliveryCharges,
      'delivery_time': deliveryTime,
    };
  }

  // Getter for school name (using location field)
  String get name => location;
  
  // Getter for ID (using pkLocationCode)
  int get id => pkLocationCode;
  
  // Getter for isActive (using status field)
  bool get isActive => status == 1;

  @override
  String toString() {
    return 'DeliveryLocation(id: $id, name: $name, isActive: $isActive)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is DeliveryLocation && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

class DeliveryLocationsResponse {
  final List<DeliveryLocation> data;
  final bool success;
  final DeliveryLocationsMeta meta;

  DeliveryLocationsResponse({
    required this.data,
    required this.success,
    required this.meta,
  });

  factory DeliveryLocationsResponse.fromJson(Map<String, dynamic> json) {
    return DeliveryLocationsResponse(
      data: (json['data'] as List<dynamic>?)
          ?.map((item) => DeliveryLocation.fromJson(item))
          .toList() ?? [],
      success: json['success'] ?? false,
      meta: DeliveryLocationsMeta.fromJson(json['meta'] ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'data': data.map((location) => location.toJson()).toList(),
      'success': success,
      'meta': meta.toJson(),
    };
  }

  // Convenience getters for backward compatibility
  int get total => meta.total;
  int get perPage => meta.perPage;
  int get currentPage => meta.currentPage;
  int get lastPage => meta.lastPage;
}

class DeliveryLocationsMeta {
  final int currentPage;
  final int perPage;
  final int total;
  final int lastPage;
  final String companyId;
  final String timestamp;
  final String apiVersion;

  DeliveryLocationsMeta({
    required this.currentPage,
    required this.perPage,
    required this.total,
    required this.lastPage,
    required this.companyId,
    required this.timestamp,
    required this.apiVersion,
  });

  factory DeliveryLocationsMeta.fromJson(Map<String, dynamic> json) {
    return DeliveryLocationsMeta(
      currentPage: json['current_page'] ?? 1,
      perPage: json['per_page'] ?? 15,
      total: json['total'] ?? 0,
      lastPage: json['last_page'] ?? 1,
      companyId: json['company_id'] ?? '',
      timestamp: json['timestamp'] ?? '',
      apiVersion: json['api_version'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'current_page': currentPage,
      'per_page': perPage,
      'total': total,
      'last_page': lastPage,
      'company_id': companyId,
      'timestamp': timestamp,
      'api_version': apiVersion,
    };
  }
}