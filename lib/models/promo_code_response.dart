class PromoCodeResponse {
  final bool success;
  final String? message;
  final double? discountAmount;
  // Add more fields as per actual API response

  PromoCodeResponse({
    required this.success,
    this.message,
    this.discountAmount,
  });

  factory PromoCodeResponse.fromJson(Map<String, dynamic> json) {
    return PromoCodeResponse(
      success: json['success'] ?? false,
      message: json['message'],
      discountAmount: (json['discount_amount'] as num?)?.toDouble(),
    );
  }
} 