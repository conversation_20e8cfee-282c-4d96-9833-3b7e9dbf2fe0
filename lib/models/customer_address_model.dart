class CustomerAddressesResponse {
  final bool success;
  final List<CustomerAddress> data;
  final Meta meta;

  CustomerAddressesResponse({
    required this.success,
    required this.data,
    required this.meta,
  });

  factory CustomerAddressesResponse.fromJson(Map<String, dynamic> json) {
    return CustomerAddressesResponse(
      success: json['success'] ?? false,
      data: (json['data'] as List<dynamic>?)
              ?.map((e) => CustomerAddress.fromJson(e))
              .toList() ??
          [],
      meta: Meta.fromJson(json['meta'] ?? {}),
    );
  }
}

class CustomerAddress {
  final int pkCustomerAddressCode;
  final int companyId;
  final int unitId;
  final int fkCustomerCode;
  final String menuType;
  final String city;
  final int locationCode;
  final String locationName;
  final String locationAddress;
  final String locationZipcode;
  final int deliveryPersonId;
  final bool isDefault;
  final String dabbawalaCodeType;
  final String? dabbawalaCode;
  final String? dabbawalaImage;
  final String createdOn;
  final String modifiedOn;
  final StudentProfile studentProfile;
  final String childName;
  final String studentClass;
  final String division;
  final String floor;
  final List<String> allergies;
  final bool hasAllergies;

  CustomerAddress({
    required this.pkCustomerAddressCode,
    required this.companyId,
    required this.unitId,
    required this.fkCustomerCode,
    required this.menuType,
    required this.city,
    required this.locationCode,
    required this.locationName,
    required this.locationAddress,
    required this.locationZipcode,
    required this.deliveryPersonId,
    required this.isDefault,
    required this.dabbawalaCodeType,
    this.dabbawalaCode,
    this.dabbawalaImage,
    required this.createdOn,
    required this.modifiedOn,
    required this.studentProfile,
    required this.childName,
    required this.studentClass,
    required this.division,
    required this.floor,
    required this.allergies,
    required this.hasAllergies,
  });

  factory CustomerAddress.fromJson(Map<String, dynamic> json) {
    return CustomerAddress(
      pkCustomerAddressCode: json['pk_customer_address_code'] ?? 0,
      companyId: json['company_id'] ?? 0,
      unitId: json['unit_id'] ?? 0,
      fkCustomerCode: json['fk_customer_code'] ?? 0,
      menuType: json['menu_type'] ?? '',
      city: json['city'] ?? '',
      locationCode: json['location_code'] ?? 0,
      locationName: json['location_name'] ?? '',
      locationAddress: json['location_address'] ?? '',
      locationZipcode: json['location_zipcode'] ?? '',
      deliveryPersonId: json['delivery_person_id'] ?? 0,
      isDefault: json['default'] ?? false,
      dabbawalaCodeType: json['dabbawala_code_type'] ?? '',
      dabbawalaCode: json['dabbawala_code'],
      dabbawalaImage: json['dabbawala_image'],
      createdOn: json['created_on'] ?? '',
      modifiedOn: json['modified_on'] ?? '',
      studentProfile: StudentProfile.fromJson(json['student_profile'] ?? {}),
      childName: json['child_name'] ?? '',
      studentClass: json['class'] ?? '',
      division: json['division'] ?? '',
      floor: json['floor'] ?? '',
      allergies: (json['allergies'] as List<dynamic>?)
              ?.map((e) => e.toString())
              .toList() ??
          [],
      hasAllergies: json['has_allergies'] ?? false,
    );
  }
}

class StudentProfile {
  final String childName;
  final String studentClass;
  final String division;
  final String floor;
  final List<String> allergies;
  final String? rawAllergies;

  StudentProfile({
    required this.childName,
    required this.studentClass,
    required this.division,
    required this.floor,
    required this.allergies,
    this.rawAllergies,
  });

  factory StudentProfile.fromJson(Map<String, dynamic> json) {
    return StudentProfile(
      childName: json['child_name'] ?? '',
      studentClass: json['class'] ?? '',
      division: json['division'] ?? '',
      floor: json['floor'] ?? '',
      allergies: (json['allergies'] as List<dynamic>?)
              ?.map((e) => e.toString())
              .toList() ??
          [],
      rawAllergies: json['raw_allergies'],
    );
  }
}

class Meta {
  final int currentPage;
  final int perPage;
  final int total;
  final int lastPage;
  final String customerId;
  final String timestamp;
  final String apiVersion;

  Meta({
    required this.currentPage,
    required this.perPage,
    required this.total,
    required this.lastPage,
    required this.customerId,
    required this.timestamp,
    required this.apiVersion,
  });

  factory Meta.fromJson(Map<String, dynamic> json) {
    return Meta(
      currentPage: json['current_page'] ?? 1,
      perPage: json['per_page'] ?? 15,
      total: json['total'] ?? 0,
      lastPage: json['last_page'] ?? 1,
      customerId: json['customer_id'] ?? '',
      timestamp: json['timestamp'] ?? '',
      apiVersion: json['api_version'] ?? '',
    );
  }
} 