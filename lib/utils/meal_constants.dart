import 'package:flutter/material.dart';

/// Utility class that holds constants for meal styling throughout the app
class MealConstants {
  // Google Play Colors
  static const Color googleGreen = Color(0xFF0F9D58);
  static const Color googleBlue = Color(0xFF4285F4);
  static const Color googleYellow = Color(0xFFF4B400);
  static const Color googleRed = Color(0xFFDB4437);

  // Breakfast styling (using Google Green)
  static const Color breakfastIconColor = googleGreen;
  static Color breakfastBgColor = googleGreen.withOpacity(0.1);
  static Color breakfastBorderColor = googleGreen.withOpacity(0.2);
  static const IconData breakfastIcon = Icons.ramen_dining;

  // Lunch styling (using Google Blue)
  static const Color lunchIconColor = googleBlue;
  static Color lunchBgColor = googleBlue.withOpacity(0.1);
  static Color lunchBorderColor = googleBlue.withOpacity(0.2);
  static const IconData lunchIcon = Icons.flatware;

  // Express order styling (using Google Yellow)
  static const Color expressIconColor = googleYellow;
  static Color expressBgColor = googleYellow.withOpacity(0.1);
  static Color expressBorderColor = googleYellow.withOpacity(0.2);
  static const IconData expressIcon = Icons.delivery_dining;

  // Status colors
  static const Color cancelledColor = googleRed;
  static const Color swappedColor = googleYellow;

  /// Returns the appropriate icon color based on meal type
  static Color getIconColor(String mealType) {
    switch (mealType) {
      case 'breakfast':
        return breakfastIconColor;
      case 'express':
        return expressIconColor;
      case 'lunch':
      default:
        return lunchIconColor;
    }
  }

  /// Returns the appropriate background color based on meal type
  static Color getBgColor(String mealType) {
    switch (mealType) {
      case 'breakfast':
        return breakfastBgColor;
      case 'express':
        return expressBgColor;
      case 'lunch':
      default:
        return lunchBgColor;
    }
  }

  /// Returns the appropriate border color based on meal type
  static Color getBorderColor(String mealType) {
    switch (mealType) {
      case 'breakfast':
        return breakfastBorderColor;
      case 'express':
        return expressBorderColor;
      case 'lunch':
      default:
        return lunchBorderColor;
    }
  }

  /// Returns the appropriate icon based on meal type
  static IconData getIcon(String mealType) {
    switch (mealType) {
      case 'breakfast':
        return breakfastIcon;
      case 'express':
        return expressIcon;
      case 'lunch':
      default:
        return lunchIcon;
    }
  }

  /// Returns the appropriate status color
  static Color getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'cancelled':
        return cancelledColor;
      case 'swapped':
        return swappedColor;
      default:
        return lunchIconColor; // Use lunch color for default/scheduled status
    }
  }
}
