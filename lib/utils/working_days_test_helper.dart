import 'package:startwell/services/working_days_service.dart';
import 'dart:developer' as dev;

/// Helper class to test working days API integration
class WorkingDaysTestHelper {
  
  /// Test the working days API with different parameters
  static Future<void> testWorkingDaysAPI() async {
    dev.log('🧪 Starting Working Days API Tests...');
    
    try {
      // Test 1: Basic lunch request
      dev.log('📋 Test 1: Basic lunch request');
      final basicResponse = await WorkingDaysService.getCurrentMonthWorkingDays(
        mealType: 'lunch',
      );
      
      dev.log('✅ Basic test result: ${basicResponse.success}');
      dev.log('📅 Enabled dates count: ${basicResponse.enabledDates.length}');
      if (basicResponse.enabledDates.isNotEmpty) {
        dev.log('📅 First few dates: ${basicResponse.enabledDates.take(5).map((d) => "${d.day}/${d.month}/${d.year}").join(", ")}');
      }
      
      // Test 2: Custom days (Monday, Wednesday, Friday)
      dev.log('📋 Test 2: Custom days (Monday, Wednesday, Friday)');
      final customResponse = await WorkingDaysService.getCurrentMonthWorkingDays(
        mealType: 'lunch',
        customDays: [1, 3, 5], // Monday, Wednesday, Friday
      );
      
      dev.log('✅ Custom days test result: ${customResponse.success}');
      dev.log('📅 Custom enabled dates count: ${customResponse.enabledDates.length}');
      if (customResponse.enabledDates.isNotEmpty) {
        dev.log('📅 Custom dates: ${customResponse.enabledDates.take(5).map((d) => "${d.day}/${d.month}/${d.year} (${_getDayName(d.weekday)})").join(", ")}');
      }
      
      // Test 3: Breakfast request
      dev.log('📋 Test 3: Breakfast request');
      final breakfastResponse = await WorkingDaysService.getCurrentMonthWorkingDays(
        mealType: 'breakfast',
      );
      
      dev.log('✅ Breakfast test result: ${breakfastResponse.success}');
      dev.log('📅 Breakfast enabled dates count: ${breakfastResponse.enabledDates.length}');
      
      // Test 4: Specific month
      dev.log('📋 Test 4: Specific month (August 2025)');
      final specificMonthResponse = await WorkingDaysService.getWorkingDays(
        mealType: 'lunch',
        monthSelected: DateTime(2025, 8, 1),
      );
      
      dev.log('✅ Specific month test result: ${specificMonthResponse.success}');
      dev.log('📅 August enabled dates count: ${specificMonthResponse.enabledDates.length}');
      
      // Test 5: Date range
      dev.log('📋 Test 5: Date range (July 1 - August 31, 2025)');
      final dateRangeResponse = await WorkingDaysService.getWorkingDaysForDateRange(
        mealType: 'lunch',
        startDate: DateTime(2025, 7, 1),
        endDate: DateTime(2025, 8, 31),
        customDays: [1, 2, 3, 4, 5], // Monday to Friday
      );
      
      dev.log('✅ Date range test result: ${dateRangeResponse.success}');
      dev.log('📅 Date range enabled dates count: ${dateRangeResponse.enabledDates.length}');
      
      // Test 6: Validation
      dev.log('📋 Test 6: API Configuration validation');
      final isValidConfig = WorkingDaysService.validateApiConfiguration();
      dev.log('✅ API configuration valid: $isValidConfig');
      
      // Test 7: Working day check
      if (basicResponse.enabledDates.isNotEmpty) {
        dev.log('📋 Test 7: Working day check');
        final testDate = basicResponse.enabledDates.first;
        final isWorking = WorkingDaysService.isWorkingDay(testDate, basicResponse);
        dev.log('✅ Is ${testDate.day}/${testDate.month}/${testDate.year} a working day: $isWorking');
        
        // Test with a non-working day (assuming Sunday is not working)
        final sunday = DateTime(2025, 7, 6); // A Sunday
        final isSundayWorking = WorkingDaysService.isWorkingDay(sunday, basicResponse);
        dev.log('✅ Is Sunday (6/7/2025) a working day: $isSundayWorking');
      }
      
      dev.log('🎉 All Working Days API tests completed!');
      
    } catch (e) {
      dev.log('❌ Working Days API test failed: $e');
      dev.log('💡 Make sure the API server is running at http://192.168.1.167:8005');
    }
  }
  
  /// Test weekday conversion utility
  static void testWeekdayConversion() {
    dev.log('🧪 Testing weekday conversion...');
    
    // Test different weekday selections
    final testCases = [
      [true, false, false, false, false, false, false], // Monday only
      [true, false, true, false, true, false, false], // Monday, Wednesday, Friday
      [true, true, true, true, true, false, false], // Monday to Friday
      [false, false, false, false, false, true, true], // Weekend only
      [true, true, true, true, true, true, true], // All days
      [false, false, false, false, false, false, false], // No days
    ];
    
    for (int i = 0; i < testCases.length; i++) {
      final selectedWeekdays = testCases[i];
      final customDays = WorkingDaysService.convertSelectedWeekdaysToCustomDays(selectedWeekdays);
      
      final selectedDayNames = <String>[];
      for (int j = 0; j < selectedWeekdays.length; j++) {
        if (selectedWeekdays[j]) {
          selectedDayNames.add(_getDayName(j + 1));
        }
      }
      
      dev.log('Test ${i + 1}: ${selectedDayNames.join(", ")} → $customDays');
    }
    
    dev.log('✅ Weekday conversion tests completed!');
  }
  
  /// Get day name from weekday number (1=Monday, 7=Sunday)
  static String _getDayName(int weekday) {
    const dayNames = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
    return dayNames[weekday - 1];
  }
  
  /// Test API with current app parameters
  static Future<void> testWithCurrentParameters({
    required String mealType,
    required bool isCustomPlan,
    required List<bool> selectedWeekdays,
  }) async {
    dev.log('🧪 Testing with current app parameters...');
    dev.log('📋 Meal type: $mealType');
    dev.log('📋 Is custom plan: $isCustomPlan');
    dev.log('📋 Selected weekdays: ${selectedWeekdays.asMap().entries.where((e) => e.value).map((e) => _getDayName(e.key + 1)).join(", ")}');
    
    try {
      List<int>? customDays;
      if (isCustomPlan) {
        customDays = WorkingDaysService.convertSelectedWeekdaysToCustomDays(selectedWeekdays);
        dev.log('📋 Custom days: $customDays');
      }
      
      final response = await WorkingDaysService.getCurrentMonthWorkingDays(
        mealType: mealType,
        customDays: customDays,
      );
      
      dev.log('✅ Current parameters test result: ${response.success}');
      dev.log('📅 Enabled dates count: ${response.enabledDates.length}');
      
      if (response.enabledDates.isNotEmpty) {
        dev.log('📅 Available dates: ${response.enabledDates.map((d) => "${d.day}/${d.month}/${d.year} (${_getDayName(d.weekday)})").join(", ")}');
        
        // Verify that custom days are respected
        if (customDays != null && customDays.isNotEmpty) {
          final invalidDates = response.enabledDates.where((date) => !customDays!.contains(date.weekday)).toList();
          if (invalidDates.isEmpty) {
            dev.log('✅ All enabled dates match selected weekdays');
          } else {
            dev.log('⚠️ Found ${invalidDates.length} dates that don\'t match selected weekdays');
          }
        }
      }
      
    } catch (e) {
      dev.log('❌ Current parameters test failed: $e');
    }
  }
  
  /// Quick test to verify API is accessible
  static Future<bool> quickApiTest() async {
    try {
      dev.log('🔍 Quick API accessibility test...');
      
      final response = await WorkingDaysService.getCurrentMonthWorkingDays(
        mealType: 'lunch',
      );
      
      final isAccessible = response.success && response.enabledDates.isNotEmpty;
      dev.log(isAccessible ? '✅ API is accessible and working' : '❌ API returned empty or failed response');
      
      return isAccessible;
    } catch (e) {
      dev.log('❌ API is not accessible: $e');
      return false;
    }
  }
}
