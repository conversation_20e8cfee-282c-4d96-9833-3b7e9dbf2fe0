import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:startwell/models/delivery_location_model.dart';
import 'package:startwell/services/delivery_location_service.dart';
import 'package:startwell/themes/app_theme.dart';
import 'package:startwell/utils/toast_utils.dart';

class SchoolSelectorDropdown extends StatefulWidget {
  final String? selectedSchool;
  final Function(String?) onSchoolSelected;
  final bool isLoading;
  final String? error;
  final String labelText;
  final bool showRefreshButton;
  final Function()? onRefresh;

  const SchoolSelectorDropdown({
    Key? key,
    this.selectedSchool,
    required this.onSchoolSelected,
    this.isLoading = false,
    this.error,
    this.labelText = 'Select School Name',
    this.showRefreshButton = false,
    this.onRefresh,
  }) : super(key: key);

  @override
  State<SchoolSelectorDropdown> createState() => _SchoolSelectorDropdownState();
}

class _SchoolSelectorDropdownState extends State<SchoolSelectorDropdown> {
  final DeliveryLocationService _service = DeliveryLocationService();
  List<DeliveryLocation> _deliveryLocations = [];
  List<DeliveryLocation> _filteredLocations = [];
  bool _isLoadingSchools = false;
  String? _error;
  String _searchQuery = '';
  final TextEditingController _searchController = TextEditingController();
  bool _isDropdownOpen = false;

  @override
  void initState() {
    super.initState();
    _loadSchools();
  }

  @override
  void didUpdateWidget(SchoolSelectorDropdown oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.selectedSchool != widget.selectedSchool) {
      print('SchoolSelectorDropdown didUpdateWidget - old: ${oldWidget.selectedSchool}, new: ${widget.selectedSchool}');
      setState(() {
        // Force rebuild when selectedSchool changes
      });
    }
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadSchools() async {
    if (_isLoadingSchools) return;
    setState(() {
      _isLoadingSchools = true;
      _error = null;
    });
    try {
      final response = await _service.getDeliveryLocations(perPage: 50);
      if (response.success) {
        setState(() {
          _deliveryLocations = response.data.where((location) => location.isActive).toList();
          _filteredLocations = _deliveryLocations;
        });
      } else {
        throw Exception('Failed to load schools');
      }
    } catch (e) {
      setState(() {
        _error = e.toString();
      });
      if (mounted) {
        ToastUtils.showToast(
          context: context,
          message: 'Failed to load schools: ${e.toString()}',
          type: ToastType.error,
        );
      }
    } finally {
      setState(() {
        _isLoadingSchools = false;
      });
    }
  }

  void _filterSchools(String query) {
    setState(() {
      _searchQuery = query;
      if (query.isEmpty) {
        _filteredLocations = _deliveryLocations;
      } else {
        _filteredLocations = _deliveryLocations
            .where((location) => 
                location.location.toLowerCase().contains(query.toLowerCase()) ||
                location.city.toLowerCase().contains(query.toLowerCase()) ||
                (location.subCityArea?.toLowerCase().contains(query.toLowerCase()) ?? false))
            .toList();
      }
    });
  }

  Future<void> _refreshSchools() async {
    await _loadSchools();
    widget.onRefresh?.call();
  }

  void _showSchoolSelectionDialog() async {
    print('Opening school selection dialog...');
    setState(() {
      _isDropdownOpen = true;
    });
    final result = await showDialog<String>(
      context: context,
      builder: (context) => _SchoolSelectionDialog(
        schools: _filteredLocations,
        selectedSchool: widget.selectedSchool,
        onSchoolSelected: (school) {
          print('School selected in dialog: $school');
          Navigator.of(context).pop(school);
        },
        searchController: _searchController,
        onSearchChanged: _filterSchools,
        isLoading: _isLoadingSchools,
      ),
    );
    print('Dialog result: $result');
    setState(() {
      _isDropdownOpen = false;
      _searchController.clear();
      _searchQuery = '';
      _filteredLocations = _deliveryLocations;
    });
    if (result != null) {
      print('Calling widget.onSchoolSelected with: $result');
      widget.onSchoolSelected(result);
    }
  }

  String _getSchoolCity(String schoolName) {
    final school = _deliveryLocations.firstWhere(
      (location) => location.location == schoolName,
      orElse: () => DeliveryLocation(
        pkLocationCode: 0,
        companyId: 0,
        unitId: 0,
        location: '',
        city: '',
        isDefault: '0',
        status: 0,
        deliveryCharges: '',
        deliveryTime: '',
      ),
    );
    return school.city;
  }

  int _getSchoolCount() {
    return _deliveryLocations.length;
  }

  @override
  Widget build(BuildContext context) {
    print('SchoolSelectorDropdown build - selectedSchool: ${widget.selectedSchool}');
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.showRefreshButton)
          Row(
            children: [
              Expanded(
                child: Text(
                  widget.labelText,
                  style: GoogleFonts.poppins(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: AppTheme.textDark,
                  ),
                ),
              ),
              if (widget.showRefreshButton)
                Container(
                  decoration: BoxDecoration(
                    color: AppTheme.purple.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: IconButton(
                    onPressed: _isLoadingSchools ? null : _refreshSchools,
                    icon: _isLoadingSchools
                        ? const SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(AppTheme.purple),
                            ),
                          )
                        : const Icon(
                            Icons.refresh,
                            color: AppTheme.purple,
                            size: 20,
                          ),
                    tooltip: 'Refresh schools',
                    padding: const EdgeInsets.all(8),
                    constraints: const BoxConstraints(minWidth: 40, minHeight: 40),
                  ),
                ),
            ],
          ),
        const SizedBox(height: 8),
        Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: _error != null 
                  ? Colors.red.shade300 
                  : _isDropdownOpen 
                      ? AppTheme.purple 
                      : Colors.grey.shade200,
              width: _isDropdownOpen ? 2 : 1,
            ),
            boxShadow: [
              BoxShadow(
                color: _isDropdownOpen 
                    ? AppTheme.purple.withOpacity(0.15)
                    : Colors.grey.withOpacity(0.1),
                spreadRadius: 1,
                blurRadius: 8,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              borderRadius: BorderRadius.circular(16),
              onTap: () {
                if (!_isLoadingSchools && _error == null) {
                  _showSchoolSelectionDialog();
                }
              },
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
                child: Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: AppTheme.purple.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: const Icon(
                        Icons.school,
                        color: AppTheme.purple,
                        size: 20,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            widget.selectedSchool ?? 'Select a school',
                            style: GoogleFonts.poppins(
                              fontSize: 14,
                              fontWeight: FontWeight.w500,
                              color: widget.selectedSchool != null 
                                  ? AppTheme.textDark 
                                  : AppTheme.textMedium,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                          if (widget.selectedSchool != null)
                            Text(
                              '${_getSchoolCity(widget.selectedSchool!)} • ${_getSchoolCount()} schools available',
                              style: GoogleFonts.poppins(
                                fontSize: 11,
                                color: AppTheme.textMedium,
                              ),
                            ),
                        ],
                      ),
                    ),
                    const SizedBox(width: 8),
                    if (_isLoadingSchools)
                      const SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(AppTheme.purple),
                        ),
                      )
                    else if (_error != null)
                      Icon(
                        Icons.error_outline,
                        color: Colors.red.shade400,
                        size: 20,
                      )
                    else
                      Icon(
                        Icons.keyboard_arrow_down_rounded,
                        color: AppTheme.purple,
                        size: 24,
                      ),
                  ],
                ),
              ),
            ),
          ),
        ),
        if (_error != null)
          Padding(
            padding: const EdgeInsets.only(top: 8),
            child: Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.red.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.red.shade200),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.error_outline,
                    color: Colors.red.shade400,
                    size: 16,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      _error!,
                      style: GoogleFonts.poppins(
                        fontSize: 12,
                        color: Colors.red.shade600,
                      ),
                    ),
                  ),
                  TextButton(
                    onPressed: _loadSchools,
                    child: Text(
                      'Retry',
                      style: GoogleFonts.poppins(
                        fontSize: 12,
                        color: AppTheme.purple,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
      ],
    );
  }
}

class _SchoolSelectionDialog extends StatefulWidget {
  final List<DeliveryLocation> schools;
  final String? selectedSchool;
  final Function(String) onSchoolSelected;
  final TextEditingController searchController;
  final Function(String) onSearchChanged;
  final bool isLoading;

  const _SchoolSelectionDialog({
    required this.schools,
    required this.selectedSchool,
    required this.onSchoolSelected,
    required this.searchController,
    required this.onSearchChanged,
    required this.isLoading,
  });

  @override
  State<_SchoolSelectionDialog> createState() => _SchoolSelectionDialogState();
}

class _SchoolSelectionDialogState extends State<_SchoolSelectionDialog> {
  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
      child: Container(
        constraints: const BoxConstraints(maxHeight: 500),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                gradient: AppTheme.purpleToDeepPurple,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(20),
                  topRight: Radius.circular(20),
                ),
              ),
              child: Row(
                children: [
                  const Icon(
                    Icons.school,
                    color: Colors.white,
                    size: 24,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      'Select School',
                      style: GoogleFonts.poppins(
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                        color: Colors.white,
                      ),
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(
                      Icons.close,
                      color: Colors.white,
                      size: 24,
                    ),
                  ),
                ],
              ),
            ),
            
            // Search bar
            Padding(
              padding: const EdgeInsets.all(16),
              child: Container(
                decoration: BoxDecoration(
                  color: Colors.grey.shade50,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.grey.shade200),
                ),
                child: TextField(
                  controller: widget.searchController,
                  onChanged: widget.onSearchChanged,
                  decoration: InputDecoration(
                    hintText: 'Search schools...',
                    hintStyle: GoogleFonts.poppins(
                      fontSize: 14,
                      color: AppTheme.textMedium,
                    ),
                    prefixIcon: const Icon(
                      Icons.search,
                      color: AppTheme.purple,
                      size: 20,
                    ),
                    border: InputBorder.none,
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 12,
                    ),
                  ),
                ),
              ),
            ),
            
            // Schools list
            Flexible(
              child: widget.isLoading
                  ? const Center(
                      child: Padding(
                        padding: EdgeInsets.all(32),
                        child: CircularProgressIndicator(
                          valueColor: AlwaysStoppedAnimation<Color>(AppTheme.purple),
                        ),
                      ),
                    )
                  : widget.schools.isEmpty
                      ? Center(
                          child: Padding(
                            padding: const EdgeInsets.all(32),
                            child: Column(
                              children: [
                                Icon(
                                  Icons.school_outlined,
                                  size: 48,
                                  color: Colors.grey.shade400,
                                ),
                                const SizedBox(height: 16),
                                Text(
                                  'No schools found',
                                  style: GoogleFonts.poppins(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w500,
                                    color: Colors.grey.shade600,
                                  ),
                                ),
                                const SizedBox(height: 8),
                                Text(
                                  'Try adjusting your search',
                                  style: GoogleFonts.poppins(
                                    fontSize: 14,
                                    color: Colors.grey.shade500,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        )
                      : ListView.builder(
                          shrinkWrap: true,
                          itemCount: widget.schools.length,
                          itemBuilder: (context, index) {
                            final school = widget.schools[index];
                            final isSelected = school.location == widget.selectedSchool;
                            
                            return Container(
                              margin: const EdgeInsets.symmetric(
                                horizontal: 16,
                                vertical: 4,
                              ),
                              decoration: BoxDecoration(
                                color: isSelected 
                                    ? AppTheme.purple.withOpacity(0.1)
                                    : Colors.white,
                                borderRadius: BorderRadius.circular(12),
                                border: Border.all(
                                  color: isSelected 
                                      ? AppTheme.purple 
                                      : Colors.grey.shade200,
                                  width: isSelected ? 2 : 1,
                                ),
                              ),
                              child: ListTile(
                                contentPadding: const EdgeInsets.symmetric(
                                  horizontal: 16,
                                  vertical: 8,
                                ),
                                leading: Container(
                                  padding: const EdgeInsets.all(8),
                                  decoration: BoxDecoration(
                                    color: isSelected 
                                        ? AppTheme.purple 
                                        : AppTheme.purple.withOpacity(0.1),
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  child: Icon(
                                    Icons.school,
                                    color: isSelected 
                                        ? Colors.white 
                                        : AppTheme.purple,
                                    size: 20,
                                  ),
                                ),
                                title: Text(
                                  school.location,
                                  style: GoogleFonts.poppins(
                                    fontSize: 14,
                                    fontWeight: FontWeight.w500,
                                    color: AppTheme.textDark,
                                  ),
                                ),
                                // subtitle: Text(
                                //   '${school.city}${school.subCityArea != null && school.subCityArea!.isNotEmpty ? ' • ${school.subCityArea}' : ''}',
                                //   style: GoogleFonts.poppins(
                                //     fontSize: 12,
                                //     color: AppTheme.textMedium,
                                //   ),
                                // ),
                                trailing: isSelected
                                    ? Container(
                                        padding: const EdgeInsets.all(4),
                                        decoration: BoxDecoration(
                                          color: AppTheme.purple,
                                          shape: BoxShape.circle,
                                        ),
                                        child: const Icon(
                                          Icons.check,
                                          color: Colors.white,
                                          size: 16,
                                        ),
                                      )
                                    : null,
                                onTap: () => widget.onSchoolSelected(school.location),
                              ),
                            );
                          },
                        ),
            ),
            
            // Bottom padding
            const SizedBox(height: 16),
          ],
        ),
      ),
    );
  }
} 