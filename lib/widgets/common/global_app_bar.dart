import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:startwell/themes/app_theme.dart';
import 'package:startwell/screens/menu_page.dart';
import 'package:startwell/widgets/profile_avatar.dart';
import 'package:startwell/models/user_profile.dart';
import 'package:startwell/services/meal_selection_manager.dart';
import 'package:startwell/app/routes/app_routes.dart';
import 'package:get/get.dart';
import 'package:startwell/app/modules/menu/views/menu_view.dart';
import 'package:startwell/app/modules/weekly_planner/data/providers/weekly_planner_provider.dart';
import 'package:startwell/app/modules/weekly_planner/data/providers/weekly_planner_repo.dart';
import 'package:startwell/app/modules/menu/controllers/menu_controller.dart' as menu_controller;

class GlobalAppBar extends StatefulWidget implements PreferredSizeWidget {
  final String? title;
  final UserProfile? userProfile;
  final bool showBackButton;
  final VoidCallback? onBackPressed;
  final Widget? leading;
  final bool hideMenuAndCart;
  final bool hideProfileIcon;

  const GlobalAppBar({
    Key? key,
    this.title,
    this.userProfile,
    this.showBackButton = false,
    this.onBackPressed,
    this.leading,
    this.hideMenuAndCart = false,
    this.hideProfileIcon = false,
  }) : super(key: key);

  @override
  State<GlobalAppBar> createState() => _GlobalAppBarState();

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}

class _GlobalAppBarState extends State<GlobalAppBar>
    with SingleTickerProviderStateMixin {
  late AnimationController _menuIconController;
  late Animation<double> _menuFloatAnimation;
  late Animation<double> _menuFadeAnimation;

  @override
  void initState() {
    super.initState();
    _menuIconController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 2000),
    )..repeat(reverse: true);

    _menuFloatAnimation = Tween<double>(
      begin: 0.0,
      end: 3.0,
    ).animate(
      CurvedAnimation(
        parent: _menuIconController,
        curve: Curves.easeInOut,
      ),
    );

    _menuFadeAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(
      CurvedAnimation(
        parent: _menuIconController,
        curve: Curves.easeInOut,
      ),
    );
  }

  @override
  void dispose() {
    _menuIconController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isExtraSmall = screenWidth < 320;
    final isSmall = screenWidth >= 320 && screenWidth < 375;
    final isMedium = screenWidth >= 375 && screenWidth < 425;
    final isLarge = screenWidth >= 425 && screenWidth < 768;
    final isTablet = screenWidth >= 768;

    // Get the appropriate font size based on screen width
    double getFontSize() {
      if (isExtraSmall) return 14;
      if (isSmall) return 15;
      if (isMedium) return 16;
      if (isLarge) return 17;
      if (isTablet) return 18;
      return 16; // Default size
    }

    // Get the appropriate font weight based on screen width
    FontWeight getFontWeight() {
      if (isExtraSmall || isSmall) return FontWeight.w500;
      return FontWeight.w600;
    }

    return AppBar(
      title: widget.title != null
          ? Text(
              widget.title!,
              style: GoogleFonts.poppins(
                fontSize: getFontSize(),
                fontWeight: getFontWeight(),
                color: Colors.white,
                letterSpacing: isTablet ? 0.5 : 0.25,
              ),
              overflow: TextOverflow.ellipsis,
              maxLines: 1,
            )
          : null,
      backgroundColor: Colors.transparent,
      flexibleSpace: Container(
        decoration: BoxDecoration(gradient: AppTheme.purpleToDeepPurple),
      ),
      elevation: 0,
      leading: widget.showBackButton
          ? IconButton(
              icon: Icon(
                Icons.arrow_back,
                color: Colors.white,
                size: isExtraSmall
                    ? 20
                    : isSmall
                        ? 22
                        : 24,
              ),
              onPressed:
                  widget.onBackPressed ?? () => Navigator.of(context).pop(),
            )
          : widget.leading ??
              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  SizedBox(
                      width: isExtraSmall ? 4 : 6),
                  SizedBox(
                    width: isExtraSmall ? 32 : 40,
                    height: isExtraSmall ? 32 : 40,
                  ),
                ],
              ),
      actions: [
        if (!widget.hideMenuAndCart) ...[
          // Menu Button with Animation
          Material(
            color: Colors.transparent,
            child: InkWell(
              onTap: () {
                // Initialize MenuController and dependencies before showing dialog
                Get.lazyPut(() => WeeklyPlannerProvider());
                Get.lazyPut(() => WeeklyPlannerRepo(Get.find()));
                Get.lazyPut(() => menu_controller.MenuController(Get.find()));
                
                // Show the integrated MenuView in a dialog
                Get.dialog(
                  Dialog(
                    insetPadding: const EdgeInsets.all(16),
                    backgroundColor: Colors.transparent,
                    child: Container(
                      constraints: const BoxConstraints(maxWidth: 600),
                      child: Material(
                        borderRadius: BorderRadius.circular(16),
                        clipBehavior: Clip.antiAlias,
                        child: SizedBox(
                          height: MediaQuery.of(context).size.height * 0.85,
                          child: MenuView(),
                        ),
                      ),
                    ),
                  ),
                  barrierDismissible: true,
                );
              },
              borderRadius: BorderRadius.circular(12),
              splashColor: Colors.white.withOpacity(0.1),
              highlightColor: Colors.white.withOpacity(0.1),
              child: Container(
                margin: EdgeInsets.symmetric(
                  horizontal: isExtraSmall
                      ? 2
                      : isSmall
                          ? 3
                          : 4,
                ),
                padding: EdgeInsets.all(isExtraSmall ? 6 : 8),
                child: AnimatedBuilder(
                  animation: _menuIconController,
                  builder: (context, child) {
                    return Transform.translate(
                      offset: Offset(0, -_menuFloatAnimation.value),
                      child: Opacity(
                        opacity: _menuFadeAnimation.value,
                        child: ShaderMask(
                          shaderCallback: (Rect bounds) {
                            return LinearGradient(
                              colors: [
                                AppTheme.yellow,
                                Colors.orange.shade300,
                              ],
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                            ).createShader(bounds);
                          },
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                Icons.restaurant_menu,
                                color: Colors.white,
                                size: isExtraSmall
                                    ? 20
                                    : isSmall
                                        ? 22
                                        : isTablet
                                            ? 26
                                            : 24,
                              ),
                              const SizedBox(width: 4),
                              Text(
                                'Menu',
                                style: GoogleFonts.poppins(
                                  color: Colors.white,
                                  fontSize: isExtraSmall
                                      ? 10
                                      : isSmall
                                          ? 11
                                          : isTablet
                                              ? 14
                                              : 12,
                                  fontWeight: isTablet
                                      ? FontWeight.w600
                                      : FontWeight.w500,
                                  letterSpacing: isTablet ? 0.5 : 0.25,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    );
                  },
                ),
              ),
            ),
          ),

          // Cart Icon with Badge
          Material(
            color: Colors.transparent,
            child: InkWell(
              onTap: () {
                Navigator.pushNamed(context, Routes.cart);
                HapticFeedback.lightImpact();
              },
              borderRadius: BorderRadius.circular(12),
              splashColor: Colors.white.withOpacity(0.1),
              highlightColor: Colors.white.withOpacity(0.1),
              child: Padding(
                padding: EdgeInsets.symmetric(
                  horizontal: isExtraSmall
                      ? 2
                      : isSmall
                          ? 3
                          : 4,
                ),
                child: Stack(
                  alignment: Alignment.center,
                  children: [
                    Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: Icon(
                        Icons.shopping_cart_outlined,
                        color: Colors.white,
                        size: isExtraSmall
                            ? 20
                            : isSmall
                                ? 22
                                : isTablet
                                    ? 26
                                    : 24,
                      ),
                    ),
                    if (MealSelectionManager.hasBreakfastInCart ||
                        MealSelectionManager.hasLunchInCart)
                      Positioned(
                        right: isExtraSmall ? 6 : 8,
                        top: isExtraSmall ? 6 : 8,
                        child: Container(
                          padding: EdgeInsets.all(isExtraSmall ? 1.5 : 2),
                          decoration: const BoxDecoration(
                            color: Colors.red,
                            shape: BoxShape.circle,
                          ),
                          constraints: BoxConstraints(
                            minWidth: isExtraSmall ? 10 : 12,
                            minHeight: isExtraSmall ? 10 : 12,
                          ),
                          child: Text(
                            '${(MealSelectionManager.hasBreakfastInCart ? 1 : 0) + (MealSelectionManager.hasLunchInCart ? 1 : 0)}',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: isExtraSmall ? 8 : 9,
                              fontWeight: FontWeight.bold,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      ),
                  ],
                ),
              ),
            ),
          ),
        ],

        // Profile Avatar
        if (!widget.hideProfileIcon)
          Material(
            color: Colors.transparent,
            child: InkWell(
              onTap: () {
                Navigator.pushNamed(context, Routes.profileSettings);
                HapticFeedback.lightImpact();
              },
              borderRadius:
                  BorderRadius.circular(50), // Make it circular for avatar
              splashColor: Colors.white.withOpacity(0.1),
              highlightColor: Colors.white.withOpacity(0.1),
              child: Padding(
                padding: EdgeInsets.only(
                  right: isExtraSmall
                      ? 6
                      : isSmall
                          ? 8
                          : isTablet
                              ? 12
                              : 10,
                  left: isExtraSmall ? 2 : 4,
                ),
                child: ProfileAvatar(
                  userProfile: widget.userProfile,
                  radius: isExtraSmall
                      ? 12
                      : isSmall
                          ? 13
                          : isTablet
                              ? 16
                              : 14,
                  onAvatarTap: null, // We're handling tap at the InkWell level
                ),
              ),
            ),
          ),
      ],
    );
  }
}
