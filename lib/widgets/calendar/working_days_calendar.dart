import 'package:flutter/material.dart';
import 'package:table_calendar/table_calendar.dart';
import 'package:startwell/services/working_days_service.dart';
import 'package:startwell/themes/app_theme.dart';
import 'dart:developer' as dev;

/// A calendar widget that integrates with the working days API
/// to show enabled/disabled dates based on meal type and custom days
class WorkingDaysCalendar extends StatefulWidget {
  final String mealType;
  final List<int>? customDays;
  final DateTime? initialDate;
  final Function(DateTime)? onDateSelected;
  final Function(DateTime)? onPageChanged;
  final bool allowPastDates;
  final CalendarFormat initialFormat;

  const WorkingDaysCalendar({
    Key? key,
    required this.mealType,
    this.customDays,
    this.initialDate,
    this.onDateSelected,
    this.onPageChanged,
    this.allowPastDates = false,
    this.initialFormat = CalendarFormat.month,
  }) : super(key: key);

  @override
  State<WorkingDaysCalendar> createState() => _WorkingDaysCalendarState();
}

class _WorkingDaysCalendarState extends State<WorkingDaysCalendar> {
  late DateTime _focusedDay;
  DateTime? _selectedDay;
  CalendarFormat _calendarFormat = CalendarFormat.month;
  
  // Working days data
  Set<DateTime> _enabledDates = {};
  bool _isLoading = true;
  String? _errorMessage;
  WorkingDaysResponse? _workingDaysData;

  @override
  void initState() {
    super.initState();
    _focusedDay = widget.initialDate ?? DateTime.now();
    _selectedDay = widget.initialDate;
    _calendarFormat = widget.initialFormat;
    _loadWorkingDays();
  }

  @override
  void didUpdateWidget(WorkingDaysCalendar oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    // Reload working days if meal type or custom days changed
    if (oldWidget.mealType != widget.mealType ||
        oldWidget.customDays != widget.customDays) {
      _loadWorkingDays();
    }
  }

  /// Load working days from API
  Future<void> _loadWorkingDays() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      dev.log('🗓️ Loading working days for meal type: ${widget.mealType}');
      dev.log('🗓️ Custom days: ${widget.customDays}');

      // Load current month
      final currentMonthResponse = await WorkingDaysService.getCurrentMonthWorkingDays(
        mealType: widget.mealType,
        customDays: widget.customDays,
      );

      // Load next month for better UX
      final nextMonthResponse = await WorkingDaysService.getNextMonthWorkingDays(
        mealType: widget.mealType,
        customDays: widget.customDays,
      );

      setState(() {
        _workingDaysData = currentMonthResponse;
        _enabledDates = {
          ...currentMonthResponse.enabledDates,
          ...nextMonthResponse.enabledDates,
        };
        _isLoading = false;
      });

      dev.log('✅ Working days loaded: ${_enabledDates.length} enabled dates');
      
    } catch (e) {
      dev.log('❌ Error loading working days: $e');
      setState(() {
        _errorMessage = e.toString();
        _isLoading = false;
        // Fallback to weekdays only
        _enabledDates = _generateWeekdayFallback();
      });
    }
  }

  /// Generate fallback enabled dates (weekdays only) if API fails
  Set<DateTime> _generateWeekdayFallback() {
    final Set<DateTime> fallbackDates = {};
    final now = DateTime.now();
    
    // Generate weekdays for current and next month
    for (int month = 0; month < 2; month++) {
      final targetMonth = DateTime(now.year, now.month + month, 1);
      final daysInMonth = DateTime(targetMonth.year, targetMonth.month + 1, 0).day;
      
      for (int day = 1; day <= daysInMonth; day++) {
        final date = DateTime(targetMonth.year, targetMonth.month, day);
        if (date.weekday <= 5) { // Monday to Friday
          fallbackDates.add(date);
        }
      }
    }
    
    return fallbackDates;
  }

  /// Check if a date is enabled for selection
  bool _isDateEnabled(DateTime day) {
    // Check if date is in the past (unless allowed)
    if (!widget.allowPastDates) {
      final now = DateTime.now();
      final today = DateTime(now.year, now.month, now.day);
      final dayOnly = DateTime(day.year, day.month, day.day);
      
      if (dayOnly.isBefore(today)) {
        return false;
      }
    }
    
    // Use working days API data if available
    if (_enabledDates.isNotEmpty && !_isLoading) {
      return _enabledDates.any((enabledDate) {
        final enabledDateOnly = DateTime(enabledDate.year, enabledDate.month, enabledDate.day);
        final dayOnly = DateTime(day.year, day.month, day.day);
        return enabledDateOnly == dayOnly;
      });
    }
    
    // Fallback to weekdays only if API data is not available
    return day.weekday <= 5;
  }

  /// Handle day selection
  void _onDaySelected(DateTime selectedDay, DateTime focusedDay) {
    if (!_isDateEnabled(selectedDay)) {
      return; // Don't select disabled dates
    }
    
    setState(() {
      _selectedDay = selectedDay;
      _focusedDay = focusedDay;
    });
    
    // Call the callback if provided
    widget.onDateSelected?.call(selectedDay);
  }

  /// Handle page change
  void _onPageChanged(DateTime focusedDay) {
    setState(() {
      _focusedDay = focusedDay;
    });
    
    // Call the callback if provided
    widget.onPageChanged?.call(focusedDay);
    
    // Load working days for the new month if needed
    _loadWorkingDays();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Loading indicator
        if (_isLoading)
          const Padding(
            padding: EdgeInsets.all(16.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2),
                ),
                SizedBox(width: 8),
                Text('Loading available dates...'),
              ],
            ),
          ),
        
        // Error message
        if (_errorMessage != null)
          Container(
            margin: const EdgeInsets.all(16.0),
            padding: const EdgeInsets.all(12.0),
            decoration: BoxDecoration(
              color: Colors.red.shade50,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.red.shade200),
            ),
            child: Row(
              children: [
                Icon(Icons.warning, color: Colors.red.shade600, size: 20),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'Failed to load available dates. Using default weekdays.',
                    style: TextStyle(color: Colors.red.shade700, fontSize: 12),
                  ),
                ),
              ],
            ),
          ),
        
        // Calendar widget
        TableCalendar<String>(
          firstDay: DateTime.now().subtract(const Duration(days: 30)),
          lastDay: DateTime.now().add(const Duration(days: 365)),
          focusedDay: _focusedDay,
          selectedDayPredicate: (day) => _selectedDay != null && isSameDay(day, _selectedDay!),
          calendarFormat: _calendarFormat,
          startingDayOfWeek: StartingDayOfWeek.monday,
          onDaySelected: _onDaySelected,
          onPageChanged: _onPageChanged,
          onFormatChanged: (format) {
            setState(() {
              _calendarFormat = format;
            });
          },
          enabledDayPredicate: _isDateEnabled,
          calendarStyle: CalendarStyle(
            outsideDaysVisible: false,
            weekendTextStyle: TextStyle(color: Colors.grey.shade600),
            disabledTextStyle: TextStyle(color: Colors.grey.shade400),
            selectedDecoration: BoxDecoration(
              color: AppTheme.purple,
              shape: BoxShape.circle,
            ),
            todayDecoration: BoxDecoration(
              color: AppTheme.purple.withOpacity(0.3),
              shape: BoxShape.circle,
            ),
            defaultDecoration: const BoxDecoration(
              shape: BoxShape.circle,
            ),
            weekendDecoration: const BoxDecoration(
              shape: BoxShape.circle,
            ),
          ),
          headerStyle: const HeaderStyle(
            formatButtonVisible: true,
            titleCentered: true,
            formatButtonShowsNext: false,
          ),
        ),
        
        // Info text
        if (_workingDaysData != null)
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Text(
              'Showing ${_enabledDates.length} available dates for ${widget.mealType}',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.grey.shade600,
              ),
              textAlign: TextAlign.center,
            ),
          ),
      ],
    );
  }
}
