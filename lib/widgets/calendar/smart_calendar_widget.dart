import 'dart:developer' as dev;
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:table_calendar/table_calendar.dart';
import '../../services/api/calendar_api_service.dart';
import '../../services/api/api_response.dart';
import '../../themes/app_theme.dart';

/// Smart calendar widget with API-driven date restrictions and availability
class SmartCalendarWidget extends StatefulWidget {
  final String mealType;
  final String planType;
  final int? locationId;
  final int? customerId;
  final DateTime? initialStartDate;
  final DateTime? initialEndDate;
  final bool allowRangeSelection;
  final Function(DateTime startDate, DateTime? endDate)? onDateSelectionChanged;
  final Function(String error)? onError;

  const SmartCalendarWidget({
    Key? key,
    required this.mealType,
    required this.planType,
    this.locationId,
    this.customerId,
    this.initialStartDate,
    this.initialEndDate,
    this.allowRangeSelection = true,
    this.onDateSelectionChanged,
    this.onError,
  }) : super(key: key);

  @override
  State<SmartCalendarWidget> createState() => _SmartCalendarWidgetState();
}

class _SmartCalendarWidgetState extends State<SmartCalendarWidget> {
  late DateTime _focusedDay;
  DateTime? _selectedDay;
  DateTime? _rangeStart;
  DateTime? _rangeEnd;
  
  // API data
  CalendarConditions? _calendarConditions;
  Set<DateTime> _availableDates = {};
  Set<DateTime> _unavailableDates = {};
  Map<String, String> _unavailableReasons = {};
  List<BlackoutDate> _blackoutDates = [];
  
  // Loading states
  bool _isLoadingConditions = true;
  bool _isLoadingDates = false;
  bool _isValidatingDate = false;
  
  // Error state
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _focusedDay = widget.initialStartDate ?? DateTime.now();
    _selectedDay = widget.initialStartDate;
    _rangeStart = widget.initialStartDate;
    _rangeEnd = widget.initialEndDate;
    
    _initializeCalendar();
  }

  /// Initialize calendar with API data
  Future<void> _initializeCalendar() async {
    await _loadCalendarConditions();
    await _loadAvailableDates();
    await _loadBlackoutDates();
  }

  /// Load calendar conditions from API
  Future<void> _loadCalendarConditions() async {
    setState(() {
      _isLoadingConditions = true;
      _errorMessage = null;
    });

    try {
      final response = await CalendarApiService.getCalendarConditions(
        mealType: widget.mealType,
        planType: widget.planType,
        locationId: widget.locationId,
        customerId: widget.customerId,
      );

      if (response.success && response.data != null) {
        setState(() {
          _calendarConditions = response.data!;
          _isLoadingConditions = false;
        });
        dev.log('✅ Calendar conditions loaded: ${_calendarConditions!.maxAdvanceBookingDays} days max advance booking');
      } else {
        _handleError('Failed to load calendar conditions: ${response.message}');
      }
    } catch (e) {
      _handleError('Error loading calendar conditions: $e');
    }
  }

  /// Load available dates for the focused month
  Future<void> _loadAvailableDates() async {
    if (_calendarConditions == null) return;

    setState(() {
      _isLoadingDates = true;
    });

    try {
      // Get date range for current month view (with padding)
      final startOfMonth = DateTime(_focusedDay.year, _focusedDay.month, 1);
      final endOfMonth = DateTime(_focusedDay.year, _focusedDay.month + 1, 0);
      
      // Extend range to include next month for better UX
      final extendedEndDate = DateTime(_focusedDay.year, _focusedDay.month + 2, 0);

      final response = await CalendarApiService.getAvailableDates(
        startDate: startOfMonth,
        endDate: extendedEndDate,
        mealType: widget.mealType,
        planType: widget.planType,
        locationId: widget.locationId,
        customerId: widget.customerId,
      );

      if (response.success && response.data != null) {
        setState(() {
          _availableDates = response.data!.availableDates.map((date) => 
            DateTime(date.year, date.month, date.day)).toSet();
          _unavailableDates = response.data!.unavailableDates.map((date) => 
            DateTime(date.year, date.month, date.day)).toSet();
          _unavailableReasons = response.data!.unavailableReasons;
          _isLoadingDates = false;
        });
        dev.log('✅ Available dates loaded: ${_availableDates.length} available, ${_unavailableDates.length} unavailable');
      } else {
        _handleError('Failed to load available dates: ${response.message}');
      }
    } catch (e) {
      _handleError('Error loading available dates: $e');
    }
  }

  /// Load blackout dates (holidays, etc.)
  Future<void> _loadBlackoutDates() async {
    try {
      // Load next 3 months of blackout dates
      final startDate = DateTime.now();
      final endDate = DateTime(startDate.year, startDate.month + 3, 0);

      final response = await CalendarApiService.getBlackoutDates(
        startDate: startDate,
        endDate: endDate,
        locationId: widget.locationId,
      );

      if (response.success && response.data != null) {
        setState(() {
          _blackoutDates = response.data!;
        });
        dev.log('✅ Blackout dates loaded: ${_blackoutDates.length} dates');
      }
    } catch (e) {
      dev.log('⚠️ Error loading blackout dates: $e');
    }
  }

  /// Handle errors with user feedback
  void _handleError(String error) {
    setState(() {
      _errorMessage = error;
      _isLoadingConditions = false;
      _isLoadingDates = false;
    });
    
    widget.onError?.call(error);
    dev.log('❌ Calendar error: $error');
  }

  /// Check if a date is selectable
  bool _isDateSelectable(DateTime date) {
    // Remove time component for comparison
    final dateOnly = DateTime(date.year, date.month, date.day);
    
    // Check if date is in the past
    final today = DateTime.now();
    final todayOnly = DateTime(today.year, today.month, today.day);
    if (dateOnly.isBefore(todayOnly)) {
      return false;
    }

    // Check calendar conditions
    if (_calendarConditions != null) {
      // Check advance booking limits
      final daysDifference = dateOnly.difference(todayOnly).inDays;
      if (daysDifference < _calendarConditions!.minAdvanceBookingDays ||
          daysDifference > _calendarConditions!.maxAdvanceBookingDays) {
        return false;
      }

      // Check weekday restrictions
      final weekday = _getWeekdayName(dateOnly.weekday);
      if (!_calendarConditions!.allowedWeekdays.contains(weekday)) {
        return false;
      }

      // Check weekend restrictions
      if (!_calendarConditions!.allowWeekends && (dateOnly.weekday == 6 || dateOnly.weekday == 7)) {
        return false;
      }
    }

    // Check if date is explicitly unavailable
    if (_unavailableDates.contains(dateOnly)) {
      return false;
    }

    // Check blackout dates
    final isBlackout = _blackoutDates.any((blackout) {
      final blackoutDate = DateTime(blackout.date.year, blackout.date.month, blackout.date.day);
      return blackoutDate == dateOnly;
    });
    if (isBlackout && !(_calendarConditions?.allowHolidays ?? false)) {
      return false;
    }

    return true;
  }

  /// Get weekday name for API compatibility
  String _getWeekdayName(int weekday) {
    const weekdays = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
    return weekdays[weekday - 1];
  }

  /// Get date decoration based on its status
  BoxDecoration? _getDateDecoration(DateTime date) {
    final dateOnly = DateTime(date.year, date.month, date.day);
    
    // Selected date(s)
    if (_selectedDay != null && isSameDay(dateOnly, _selectedDay!)) {
      return BoxDecoration(
        color: AppTheme.purple,
        shape: BoxShape.circle,
      );
    }
    
    // Range selection
    if (_rangeStart != null && _rangeEnd != null) {
      if (isSameDay(dateOnly, _rangeStart!) || isSameDay(dateOnly, _rangeEnd!)) {
        return BoxDecoration(
          color: AppTheme.purple,
          shape: BoxShape.circle,
        );
      }
      if (dateOnly.isAfter(_rangeStart!) && dateOnly.isBefore(_rangeEnd!)) {
        return BoxDecoration(
          color: AppTheme.purple.withOpacity(0.3),
          borderRadius: BorderRadius.circular(4),
        );
      }
    }

    // Unavailable dates
    if (!_isDateSelectable(dateOnly)) {
      return BoxDecoration(
        color: Colors.grey.shade200,
        shape: BoxShape.circle,
      );
    }

    // Available dates
    if (_availableDates.contains(dateOnly)) {
      return BoxDecoration(
        border: Border.all(color: AppTheme.purple.withOpacity(0.5), width: 1),
        shape: BoxShape.circle,
      );
    }

    return null;
  }

  /// Get text style for date based on its status
  TextStyle _getDateTextStyle(DateTime date) {
    final dateOnly = DateTime(date.year, date.month, date.day);
    
    // Selected date
    if (_selectedDay != null && isSameDay(dateOnly, _selectedDay!)) {
      return GoogleFonts.poppins(
        color: Colors.white,
        fontWeight: FontWeight.w600,
      );
    }
    
    // Range selection
    if (_rangeStart != null && _rangeEnd != null) {
      if (isSameDay(dateOnly, _rangeStart!) || isSameDay(dateOnly, _rangeEnd!)) {
        return GoogleFonts.poppins(
          color: Colors.white,
          fontWeight: FontWeight.w600,
        );
      }
    }

    // Unavailable dates
    if (!_isDateSelectable(dateOnly)) {
      return GoogleFonts.poppins(
        color: Colors.grey.shade400,
        fontWeight: FontWeight.w400,
      );
    }

    // Available dates
    return GoogleFonts.poppins(
      color: AppTheme.textDark,
      fontWeight: FontWeight.w500,
    );
  }

  /// Handle date selection
  void _onDaySelected(DateTime selectedDay, DateTime focusedDay) {
    if (!_isDateSelectable(selectedDay)) {
      _showDateUnavailableDialog(selectedDay);
      return;
    }

    setState(() {
      _selectedDay = selectedDay;
      _focusedDay = focusedDay;

      if (widget.allowRangeSelection) {
        if (_rangeStart == null || _rangeEnd != null) {
          // Start new range
          _rangeStart = selectedDay;
          _rangeEnd = null;
        } else {
          // Complete range
          if (selectedDay.isBefore(_rangeStart!)) {
            _rangeEnd = _rangeStart;
            _rangeStart = selectedDay;
          } else {
            _rangeEnd = selectedDay;
          }
        }
      } else {
        _rangeStart = selectedDay;
        _rangeEnd = null;
      }
    });

    // Validate the selected date with API
    _validateSelectedDate(selectedDay);

    // Notify parent
    widget.onDateSelectionChanged?.call(_rangeStart!, _rangeEnd);
  }

  /// Validate selected date with API
  Future<void> _validateSelectedDate(DateTime date) async {
    setState(() {
      _isValidatingDate = true;
    });

    try {
      final response = await CalendarApiService.validateDate(
        date: date,
        mealType: widget.mealType,
        planType: widget.planType,
        locationId: widget.locationId,
        customerId: widget.customerId,
      );

      setState(() {
        _isValidatingDate = false;
      });

      if (response.success && response.data != null) {
        if (!response.data!.isValid) {
          _showDateValidationDialog(date, response.data!);
        }
      }
    } catch (e) {
      setState(() {
        _isValidatingDate = false;
      });
      dev.log('⚠️ Date validation error: $e');
    }
  }

  /// Show dialog when date is unavailable
  void _showDateUnavailableDialog(DateTime date) {
    final dateOnly = DateTime(date.year, date.month, date.day);
    final reason = _unavailableReasons[dateOnly.toIso8601String().split('T')[0]] ?? 
                   'This date is not available for delivery';

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Date Unavailable', style: GoogleFonts.poppins(fontWeight: FontWeight.w600)),
        content: Text(reason, style: GoogleFonts.poppins()),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('OK', style: GoogleFonts.poppins(color: AppTheme.purple)),
          ),
        ],
      ),
    );
  }

  /// Show dialog for date validation issues
  void _showDateValidationDialog(DateTime date, DateValidation validation) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Date Validation', style: GoogleFonts.poppins(fontWeight: FontWeight.w600)),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(validation.message, style: GoogleFonts.poppins()),
            if (validation.issues != null && validation.issues!.isNotEmpty) ...[
              const SizedBox(height: 8),
              Text('Issues:', style: GoogleFonts.poppins(fontWeight: FontWeight.w600)),
              ...validation.issues!.map((issue) => Text('• $issue', style: GoogleFonts.poppins(fontSize: 14))),
            ],
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('OK', style: GoogleFonts.poppins(color: AppTheme.purple)),
          ),
        ],
      ),
    );
  }

  /// Handle month changes to reload data
  void _onPageChanged(DateTime focusedDay) {
    setState(() {
      _focusedDay = focusedDay;
    });
    _loadAvailableDates();
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Icon(Icons.calendar_today_rounded, color: AppTheme.purple, size: 20),
                const SizedBox(width: 8),
                Text(
                  'Select Delivery Dates',
                  style: GoogleFonts.poppins(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: AppTheme.textDark,
                  ),
                ),
                const Spacer(),
                if (_isLoadingConditions || _isLoadingDates || _isValidatingDate)
                  SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(AppTheme.purple),
                    ),
                  ),
              ],
            ),

            // Error message with retry button
            if (_errorMessage != null) ...[
              const SizedBox(height: 12),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.red.shade50,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.red.shade200),
                ),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Icon(Icons.error_outline, color: Colors.red.shade700, size: 16),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        _errorMessage!,
                        style: GoogleFonts.poppins(
                          fontSize: 12,
                          color: Colors.red.shade700,
                        ),
                      ),
                    ),
                    TextButton.icon(
                      onPressed: () {
                        setState(() {
                          _errorMessage = null;
                        });
                        _initializeCalendar();
                      },
                      icon: Icon(Icons.refresh, color: AppTheme.purple, size: 16),
                      label: Text('Retry', style: GoogleFonts.poppins(color: AppTheme.purple)),
                    ),
                  ],
                ),
              ),
            ],

            const SizedBox(height: 16),

            // Calendar conditions info
            if (_calendarConditions != null) ...[
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: AppTheme.purple.withOpacity(0.05),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: AppTheme.purple.withOpacity(0.2)),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Delivery Information',
                      style: GoogleFonts.poppins(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color: AppTheme.textDark,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'You can book meals ${_calendarConditions!.minAdvanceBookingDays} to ${_calendarConditions!.maxAdvanceBookingDays} days in advance',
                      style: GoogleFonts.poppins(
                        fontSize: 12,
                        color: AppTheme.textMedium,
                      ),
                    ),
                    if (_calendarConditions!.allowedWeekdays.isNotEmpty)
                      Text(
                        'Delivery days: ${_calendarConditions!.allowedWeekdays.map((day) => day.capitalize).join(", ")}',
                        style: GoogleFonts.poppins(
                          fontSize: 12,
                          color: AppTheme.textMedium,
                        ),
                      ),
                  ],
                ),
              ),
              const SizedBox(height: 16),
            ],

            // Calendar widget
            TableCalendar<String>(
              firstDay: DateTime.now(),
              lastDay: DateTime.now().add(Duration(days: _calendarConditions?.maxAdvanceBookingDays ?? 90)),
              focusedDay: _focusedDay,
              selectedDayPredicate: (day) => _selectedDay != null && isSameDay(day, _selectedDay!),
              rangeStartDay: _rangeStart,
              rangeEndDay: _rangeEnd,
              rangeSelectionMode: widget.allowRangeSelection ? RangeSelectionMode.toggledOn : RangeSelectionMode.disabled,
              onDaySelected: _onDaySelected,
              onPageChanged: _onPageChanged,
              calendarBuilders: CalendarBuilders<String>(
                defaultBuilder: (context, date, _) {
                  return Container(
                    margin: const EdgeInsets.all(2),
                    decoration: _getDateDecoration(date),
                    child: Center(
                      child: Text(
                        '${date.day}',
                        style: _getDateTextStyle(date),
                      ),
                    ),
                  );
                },
                outsideBuilder: (context, date, _) {
                  return Container(
                    margin: const EdgeInsets.all(2),
                    child: Center(
                      child: Text(
                        '${date.day}',
                        style: GoogleFonts.poppins(
                          color: Colors.grey.shade400,
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                    ),
                  );
                },
              ),
              calendarStyle: CalendarStyle(
                outsideDaysVisible: false,
                weekendTextStyle: GoogleFonts.poppins(
                  color: AppTheme.textMedium,
                ),
                holidayTextStyle: GoogleFonts.poppins(
                  color: Colors.red.shade600,
                ),
              ),
              headerStyle: HeaderStyle(
                formatButtonVisible: false,
                titleCentered: true,
                titleTextStyle: GoogleFonts.poppins(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: AppTheme.textDark,
                ),
                leftChevronIcon: Icon(Icons.chevron_left, color: AppTheme.purple),
                rightChevronIcon: Icon(Icons.chevron_right, color: AppTheme.purple),
              ),
              daysOfWeekStyle: DaysOfWeekStyle(
                weekdayStyle: GoogleFonts.poppins(
                  fontWeight: FontWeight.w600,
                  color: AppTheme.textMedium,
                ),
                weekendStyle: GoogleFonts.poppins(
                  fontWeight: FontWeight.w600,
                  color: AppTheme.textMedium,
                ),
              ),
            ),

            // Legend
            const SizedBox(height: 16),
            _buildLegend(),
          ],
        ),
      ),
    );
  }

  /// Build legend for calendar colors
  Widget _buildLegend() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Legend',
          style: GoogleFonts.poppins(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: AppTheme.textDark,
          ),
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 16,
          runSpacing: 8,
          children: [
            _buildLegendItem(
              color: AppTheme.purple,
              label: 'Selected',
            ),
            _buildLegendItem(
              color: AppTheme.purple.withOpacity(0.3),
              label: 'Range',
            ),
            _buildLegendItem(
              color: Colors.transparent,
              borderColor: AppTheme.purple.withOpacity(0.5),
              label: 'Available',
            ),
            _buildLegendItem(
              color: Colors.grey.shade200,
              label: 'Unavailable',
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildLegendItem({
    required Color color,
    required String label,
    Color? borderColor,
  }) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 12,
          height: 12,
          decoration: BoxDecoration(
            color: color,
            border: borderColor != null ? Border.all(color: borderColor, width: 1) : null,
            shape: BoxShape.circle,
          ),
        ),
        const SizedBox(width: 4),
        Text(
          label,
          style: GoogleFonts.poppins(
            fontSize: 12,
            color: AppTheme.textMedium,
          ),
        ),
      ],
    );
  }
} 