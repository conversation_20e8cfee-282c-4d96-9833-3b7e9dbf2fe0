import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class FooterNote extends StatefulWidget {
  const FooterNote({super.key});

  @override
  State<FooterNote> createState() => _FooterNoteState();
}

class _FooterNoteState extends State<FooterNote>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 2),
    )..repeat(reverse: true);

    _fadeAnimation = Tween<double>(
      begin: 0.6,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Define the grey color as specified
    const Color greyColor = Color(0xFF7F8285);
    const Color heartColor = Color(0xFFE63946);

    return Container(
      height: 200,
      width: double.infinity,
      alignment: Alignment.center,
      decoration: BoxDecoration(
        image: const DecorationImage(
          image: AssetImage('assets/images/background_footer.png'),
          fit: BoxFit.cover,
          alignment: Alignment.bottomCenter,
        ),
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Colors.white.withOpacity(0.9),
            Colors.white.withOpacity(0.7),
            Colors.white.withOpacity(0.4),
          ],
        ),
      ),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 24.0, horizontal: 16.0),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Colors.white.withOpacity(0.3),
              Colors.white.withOpacity(0.1),
            ],
          ),
        ),
        child: AnimatedBuilder(
          animation: _fadeAnimation,
          builder: (context, child) {
            return Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisSize: MainAxisSize.min,
              children: [
                // First line: Trusted by parents
                Opacity(
                  opacity: _fadeAnimation.value,
                  child: Text(
                    'Trusted by parents',
                    textAlign: TextAlign.center,
                    style: GoogleFonts.poppins(
                      fontSize: 28,
                      fontWeight: FontWeight.bold,
                      color: greyColor,
                      shadows: [
                        Shadow(
                          offset: const Offset(0, 1),
                          blurRadius: 2.0,
                          color: Colors.white.withOpacity(0.5),
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 12),
                // Second line: Loved by kids!
                Opacity(
                  opacity: _fadeAnimation.value,
                  child: Text(
                    'Loved by kids!',
                    textAlign: TextAlign.center,
                    style: GoogleFonts.poppins(
                      fontSize: 28,
                      fontWeight: FontWeight.bold,
                      color: greyColor,
                      shadows: [
                        Shadow(
                          offset: const Offset(0, 1),
                          blurRadius: 2.0,
                          color: Colors.white.withOpacity(0.5),
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 20),
                // Third line: Crafted with ❤️ in Navi Mumbai, India - HIDDEN
                // Row(
                //   mainAxisAlignment: MainAxisAlignment.center,
                //   children: [
                //     Text(
                //       'Crafted with ',
                //       textAlign: TextAlign.center,
                //       style: GoogleFonts.poppins(
                //         fontSize: 14,
                //         fontWeight: FontWeight.normal,
                //         color: greyColor,
                //       ),
                //     ),
                //     const Icon(
                //       Icons.favorite,
                //       color: heartColor,
                //       size: 20,
                //     ),
                //     Text(
                //       ' in Navi Mumbai, India',
                //       textAlign: TextAlign.center,
                //       style: GoogleFonts.poppins(
                //         fontSize: 14,
                //         fontWeight: FontWeight.normal,
                //         color: greyColor,
                //       ),
                //     ),
                //   ],
                // ),
              ],
            );
          },
        ),
      ),
    );
  }
}
