import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:startwell/themes/app_theme.dart';
import 'package:cached_network_image/cached_network_image.dart';

class TestimonialsSection extends StatefulWidget {
  const TestimonialsSection({Key? key}) : super(key: key);

  @override
  State<TestimonialsSection> createState() => _TestimonialsSectionState();
}

class _TestimonialsSectionState extends State<TestimonialsSection> {
  final PageController _pageController = PageController();
  Timer? _autoScrollTimer;
  int _currentPage = 0;

  final List<Map<String, dynamic>> _testimonials = const [
    {
      'name': '<PERSON><PERSON>',
      'role': 'Parent of 2 kids',
      'rating': 5.0,
      'review':
          'The meals are always fresh and my kids love them! Great variety and excellent service.',
      'imageUrl':
          'https://images.unsplash.com/photo-1494790108377-be9c29b29330?w=150&h=150&fit=crop',
    },
    {
      'name': '<PERSON><PERSON>',
      'role': 'Parent',
      'rating': 4.8,
      'review':
          'Nutritious meals delivered on time. My daughter enjoys her lunch every day.',
      'imageUrl':
          'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop',
    },
    {
      'name': 'Meera Patel',
      'role': 'Parent of 3 kids',
      'rating': 4.9,
      'review':
          'Excellent food quality and my kids are always excited about their meals.',
      'imageUrl':
          'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop',
    },
  ];

  @override
  void initState() {
    super.initState();
    _startAutoScroll();
  }

  @override
  void dispose() {
    _autoScrollTimer?.cancel();
    _pageController.dispose();
    super.dispose();
  }

  void _startAutoScroll() {
    _autoScrollTimer = Timer.periodic(const Duration(seconds: 5), (timer) {
      if (_currentPage < _testimonials.length - 1) {
        _currentPage++;
      } else {
        _currentPage = 0;
      }

      if (_pageController.hasClients) {
        _pageController.animateToPage(
          _currentPage,
          duration: const Duration(milliseconds: 500),
          curve: Curves.easeInOut,
        );
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmall = screenWidth < 360;
    final isTablet = screenWidth > 600;

    return Container(
      padding: EdgeInsets.symmetric(
        vertical: isSmall ? 20 : 24,
        horizontal: isSmall ? 16 : 20,
      ),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppTheme.purple.withOpacity(0.05),
            AppTheme.deepPurple.withOpacity(0.1),
          ],
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section Title
          Padding(
            padding: const EdgeInsets.only(bottom: 16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'What Parents Say',
                  style: GoogleFonts.poppins(
                    fontSize: isSmall ? 18 : (isTablet ? 24 : 20),
                    fontWeight: FontWeight.w600,
                    color: AppTheme.textDark,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'Trusted by thousands of happy parents',
                  style: GoogleFonts.poppins(
                    fontSize: isSmall ? 12 : (isTablet ? 16 : 14),
                    color: AppTheme.textMedium,
                  ),
                ),
              ],
            ),
          ),

          // Testimonials List
          SizedBox(
            height: isSmall ? 200 : (isTablet ? 240 : 220),
            child: PageView.builder(
              controller: _pageController,
              onPageChanged: (index) {
                setState(() {
                  _currentPage = index;
                });
              },
              itemCount: _testimonials.length,
              itemBuilder: (context, index) {
                final testimonial = _testimonials[index];
                return Container(
                  width: screenWidth * (isTablet ? 0.4 : 0.75),
                  margin: const EdgeInsets.symmetric(horizontal: 16),
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.05),
                        blurRadius: 10,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          ClipRRect(
                            borderRadius: BorderRadius.circular(25),
                            child: CachedNetworkImage(
                              imageUrl: testimonial['imageUrl'],
                              width: isSmall ? 40 : 50,
                              height: isSmall ? 40 : 50,
                              fit: BoxFit.cover,
                              placeholder: (context, url) => Container(
                                color: AppTheme.purple.withOpacity(0.1),
                                child: Icon(
                                  Icons.person,
                                  color: AppTheme.purple,
                                  size: isSmall ? 20 : 24,
                                ),
                              ),
                              errorWidget: (context, url, error) => Container(
                                color: AppTheme.purple.withOpacity(0.1),
                                child: Icon(
                                  Icons.person,
                                  color: AppTheme.purple,
                                  size: isSmall ? 20 : 24,
                                ),
                              ),
                            ),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  testimonial['name'],
                                  style: GoogleFonts.poppins(
                                    fontSize: isSmall ? 14 : 16,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                                Text(
                                  testimonial['role'],
                                  style: GoogleFonts.poppins(
                                    fontSize: isSmall ? 12 : 14,
                                    color: Colors.grey[600],
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 12),
                      Row(
                        children: List.generate(5, (index) {
                          return Icon(
                            index < testimonial['rating']
                                ? Icons.star
                                : Icons.star_border,
                            color: Colors.amber,
                            size: isSmall ? 16 : 20,
                          );
                        }),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        testimonial['review'],
                        style: GoogleFonts.poppins(
                          fontSize: isSmall ? 13 : 15,
                          height: 1.5,
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),

          // Page Indicators
          Padding(
            padding: const EdgeInsets.only(top: 16.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: List.generate(
                _testimonials.length,
                (index) => Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 4.0),
                  child: AnimatedContainer(
                    duration: const Duration(milliseconds: 300),
                    height: 8,
                    width: _currentPage == index ? 24 : 8,
                    decoration: BoxDecoration(
                      color: _currentPage == index
                          ? AppTheme.purple
                          : Colors.grey[300],
                      borderRadius: BorderRadius.circular(4),
                    ),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
