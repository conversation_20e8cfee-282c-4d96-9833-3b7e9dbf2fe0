import 'package:flutter/material.dart';
import 'package:startwell/screens/main_screen.dart';
import 'package:startwell/screens/subscription_selection_screen.dart';
import 'package:startwell/services/meal_selection_manager.dart';

class DashboardScreen extends StatefulWidget {
  // ... (existing code)
  @override
  _DashboardScreenState createState() => _DashboardScreenState();
}

class _DashboardScreenState extends State<DashboardScreen> {
  // ... (existing code)

  // Navigate to different tabs or screens
  void _navigateToTab(int index) {
    // Try to find MainScreen ancestor
    final MainScreenState? mainScreenState =
        context.findAncestorStateOfType<MainScreenState>();

    if (mainScreenState != null) {
      // If found, switch tab
      mainScreenState.switchToTab(index);
    } else {
      // Fallback to direct navigation
      if (index == 3) {
        // Navigate directly to Choose Your Plan screen (formerly Order Meal)
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => SubscriptionSelectionScreen(
              selectionManager: MealSelectionManager(),
              userProfile: null, // Will be loaded if needed
            ),
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // ... (existing code)
    return Scaffold(
      body: Center(
        child: Text('Dashboard'),
      ),
    );
  }
}
