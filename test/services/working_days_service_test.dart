import 'package:flutter_test/flutter_test.dart';
import 'package:startwell/services/working_days_service.dart';
import 'dart:developer' as dev;

void main() {
  group('WorkingDaysService Tests', () {
    
    test('should validate API configuration', () {
      expect(WorkingDaysService.validateApiConfiguration(), isTrue);
    });
    
    test('should convert selected weekdays to custom days correctly', () {
      // Test Monday, Wednesday, Friday selection
      final selectedWeekdays = [true, false, true, false, true, false, false];
      final customDays = WorkingDaysService.convertSelectedWeekdaysToCustomDays(selectedWeekdays);
      
      expect(customDays, equals([1, 3, 5])); // Monday=1, Wednesday=3, Friday=5
    });
    
    test('should handle empty weekdays selection', () {
      final selectedWeekdays = [false, false, false, false, false, false, false];
      final customDays = WorkingDaysService.convertSelectedWeekdaysToCustomDays(selectedWeekdays);
      
      expect(customDays, isEmpty);
    });
    
    test('should handle all weekdays selection', () {
      final selectedWeekdays = [true, true, true, true, true, false, false];
      final customDays = WorkingDaysService.convertSelectedWeekdaysToCustomDays(selectedWeekdays);
      
      expect(customDays, equals([1, 2, 3, 4, 5])); // Monday to Friday
    });
    
    group('WorkingDaysResponse', () {
      test('should parse JSON response correctly', () {
        final jsonResponse = {
          'success': true,
          'message': 'Working days loaded successfully',
          'data': {
            'working_days': [
              '2025-07-22',
              '2025-07-23',
              '2025-07-24',
            ]
          }
        };
        
        final response = WorkingDaysResponse.fromJson(jsonResponse);
        
        expect(response.success, isTrue);
        expect(response.message, equals('Working days loaded successfully'));
        expect(response.enabledDates.length, equals(3));
        expect(response.enabledDates.first.day, equals(22));
      });
      
      test('should handle alternative JSON structure', () {
        final jsonResponse = {
          'status': 'success',
          'msg': 'Data loaded',
          'working_days': [
            '2025-07-22',
            '2025-07-23',
          ]
        };
        
        final response = WorkingDaysResponse.fromJson(jsonResponse);
        
        expect(response.success, isTrue);
        expect(response.message, equals('Data loaded'));
        expect(response.enabledDates.length, equals(2));
      });
      
      test('should handle object format dates', () {
        final jsonResponse = {
          'success': true,
          'message': 'Success',
          'data': {
            'working_days': [
              {'date': '2025-07-22', 'enabled': true},
              {'date': '2025-07-23', 'enabled': true},
            ]
          }
        };
        
        final response = WorkingDaysResponse.fromJson(jsonResponse);
        
        expect(response.success, isTrue);
        expect(response.enabledDates.length, equals(2));
      });
      
      test('should handle parsing errors gracefully', () {
        final jsonResponse = {
          'invalid': 'data'
        };
        
        final response = WorkingDaysResponse.fromJson(jsonResponse);
        
        expect(response.success, isFalse);
        expect(response.enabledDates, isEmpty);
        expect(response.message.contains('Error parsing'), isTrue);
      });
    });
    
    group('Date Parsing', () {
      test('should parse different date formats', () {
        final testDates = [
          '2025-07-22',
          '22/07/2025',
          '07/22/2025',
          '22-07-2025',
          '2025/07/22',
        ];
        
        for (String dateStr in testDates) {
          final parsed = WorkingDaysResponse._tryParseCustomDateFormat(dateStr);
          expect(parsed, isNotNull, reason: 'Failed to parse: $dateStr');
          expect(parsed!.year, equals(2025));
          expect(parsed.month, equals(7));
          expect(parsed.day, equals(22));
        }
      });
      
      test('should return null for invalid date formats', () {
        final invalidDates = [
          'invalid-date',
          '2025-13-45', // Invalid month and day
          '32/07/2025', // Invalid day
          '',
          'null',
        ];
        
        for (String dateStr in invalidDates) {
          final parsed = WorkingDaysResponse._tryParseCustomDateFormat(dateStr);
          expect(parsed, isNull, reason: 'Should not parse: $dateStr');
        }
      });
    });
    
    test('should check working day correctly', () {
      final workingDays = WorkingDaysResponse(
        success: true,
        message: 'Test',
        enabledDates: [
          DateTime(2025, 7, 22),
          DateTime(2025, 7, 23),
          DateTime(2025, 7, 24),
        ],
        disabledDates: [],
      );
      
      expect(WorkingDaysService.isWorkingDay(DateTime(2025, 7, 22), workingDays), isTrue);
      expect(WorkingDaysService.isWorkingDay(DateTime(2025, 7, 25), workingDays), isFalse);
      
      // Should work with time components
      expect(WorkingDaysService.isWorkingDay(DateTime(2025, 7, 22, 14, 30), workingDays), isTrue);
    });
  });
  
  group('Integration Tests', () {
    // Note: These tests require network access and a running API server
    // They should be run separately or mocked for CI/CD
    
    testWidgets('should fetch working days from API', (WidgetTester tester) async {
      // Skip this test if not in integration test mode
      if (!const bool.fromEnvironment('INTEGRATION_TESTS', defaultValue: false)) {
        return;
      }
      
      try {
        final response = await WorkingDaysService.getCurrentMonthWorkingDays(
          mealType: 'lunch',
        );
        
        expect(response.success, isTrue);
        expect(response.enabledDates, isNotEmpty);
        
        dev.log('✅ Integration test passed: ${response.enabledDates.length} dates loaded');
      } catch (e) {
        dev.log('⚠️ Integration test failed (expected if API is not available): $e');
        // Don't fail the test if API is not available
      }
    });
    
    testWidgets('should fetch working days with custom days', (WidgetTester tester) async {
      if (!const bool.fromEnvironment('INTEGRATION_TESTS', defaultValue: false)) {
        return;
      }
      
      try {
        final response = await WorkingDaysService.getCurrentMonthWorkingDays(
          mealType: 'lunch',
          customDays: [1, 3, 5], // Monday, Wednesday, Friday
        );
        
        expect(response.success, isTrue);
        
        dev.log('✅ Custom days integration test passed: ${response.enabledDates.length} dates loaded');
      } catch (e) {
        dev.log('⚠️ Custom days integration test failed: $e');
      }
    });
  });
}
