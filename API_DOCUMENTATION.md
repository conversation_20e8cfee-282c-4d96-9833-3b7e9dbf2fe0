# StartWell API Documentation

This document lists all API endpoints used by the StartWell app, with descriptions, parameters, and sample usage for frontend integration.

---

## Authentication

### Login
- **POST** `${ApiConfig.keycloakAuthUrl}/auth/realms/startwell/protocol/openid-connect/token`
- **Description:** Authenticate user and obtain access/refresh tokens.
- **Parameters:**
  - `grant_type` (string, required)
  - `client_id` (string, required)
  - `username` (string, required)
  - `password` (string, required)
- **Notes:** Use `application/x-www-form-urlencoded` headers.

---

## Student Profile

### Save/Update Student Profile
- **POST** `/api/v2/students/students/save`
- **Description:** Save or update a student profile.
- **Body:** JSON object with student profile fields.

### Get Student Profile
- **GET** `/api/v2/students/students/{studentId}`
- **Description:** Fetch a student profile by ID.

---

## Payment

### Get Payment Gateways
- **GET** `/api/v2/admin/settings/payment-gateways`
- **Description:** Fetch available payment gateways for the user.
- **Response:**
  - `enabled_gateways`: List of enabled gateway names
  - `gateways`: Gateway configuration map

### Create Payment Order
- **POST** `/api/v2/payment/pg/v1/pay`
- **Description:** Create a backend order before starting payment.
- **Body:**
  - `amount` (number, required)
  - `customer_id` (string, required)
  - `plan_type` (string, required)
  - `payment_mode` (string, required)
  - `extraData` (object, optional)

### Poll Payment Status
- **GET** `/api/v2/payment/pg/v1/status?order_id={orderId}`
- **Description:** Poll backend for payment status.

---

## Promo Codes

### Apply Promo Code
- **POST** `/api/v2/quickserve/orders/apply-coupon`
- **Description:** Apply a promo code to an order.
- **Body:**
  - `promo_code` (string, required)
  - `order_id` (number, required)
  - `order_amount` (number, required)
  - `product_codes` (list, required)
  - `company_id` (number, required)
  - `unit_id` (number, required)
  - `customer_id` (number, required)
  - `location_id` (number, required)
  - `plan_quantity` (list, required)
  - `cart_items` (list, required)
  - `plan_type` (string, optional)

---

## Delivery Locations

### Get Delivery Locations
- **GET** `/api/test/delivery-locations?per_page={perPage}&page={page}`
- **Description:** Fetch paginated delivery locations.

---

## Dropdowns

### Get Dropdown Data
- **GET** `/api/v2/admin/settings/dropdowns`
- **Description:** Fetch dropdown data for classes, divisions, floors, and delivery days.

---

## Product Menu

### Get Product Menu
- **GET** `/api/test/product-menu?delivery_location_id={id}`
- **Description:** Fetch product menu for a delivery location.

---

## Plan Durations

### Get Meal Plan Durations
- **GET** `/api/v2/catalogue/meal-plan-durations`
- **Description:** Fetch available meal plan durations.

---

## Calendar

### Get Calendar Date Conditions
- **GET** `/api/v2/plans/calendar-conditions?meal_type={type}&plan_type={type}`
- **Description:** Get calendar date restrictions for meal plan selection.

### Get Available Dates
- **POST** `/api/v2/plans/available-dates`
- **Description:** Get available dates for a specific range.
- **Body:**
  - `start_date` (string, required)
  - `end_date` (string, required)
  - `meal_type` (string, required)
  - `plan_type` (string, required)
  - `location_id` (number, optional)
  - `customer_id` (number, optional)

### Validate Date
- **POST** `/api/v2/plans/validate-date`
- **Description:** Validate a specific date for meal delivery.
- **Body:**
  - `date` (string, required)
  - `meal_type` (string, required)
  - `plan_type` (string, required)
  - `location_id` (number, optional)
  - `customer_id` (number, optional)

---

## Notes
- All endpoints require `Content-Type: application/json` unless otherwise specified.
- Some endpoints may require authentication (Bearer token).
- For more details or sample responses, refer to the service layer code or contact the backend team. 