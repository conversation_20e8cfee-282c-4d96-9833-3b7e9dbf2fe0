# Working Days API Integration

This document explains how to use the working days API integration in the StartWell Flutter app.

## Overview

The working days API integration allows the app to fetch enabled/disabled dates from the backend based on:
- Company ID
- Kitchen ID  
- Meal type (breakfast, lunch, dinner)
- Selected month
- Custom weekdays (optional)

## API Endpoint

```
GET http://*************:8005/api/v2/admin/settings/working-days
```

### Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `company_id` | int | Yes | Company identifier (currently: 8163) |
| `month_selected` | string | Yes | Month in YYYY-MM format (e.g., "2025-07") |
| `kitchen_id` | int | Yes | Kitchen identifier (currently: 1) |
| `meal_type` | string | Yes | Meal type: "breakfast", "lunch", or "dinner" |
| `days[]` | int[] | No | Custom weekdays (1=Monday, 2=Tuesday, ..., 7=Sunday) |

### Example URLs

**Regular request (all weekdays):**
```
http://*************:8005/api/v2/admin/settings/working-days?company_id=8163&month_selected=2025-07&kitchen_id=1&meal_type=lunch
```

**Custom days request (Monday, Wednesday, Friday):**
```
http://*************:8005/api/v2/admin/settings/working-days?company_id=8163&month_selected=2025-07&kitchen_id=1&meal_type=lunch&days[]=1&days[]=3&days[]=5
```

## Usage in Flutter

### 1. Basic Usage

```dart
import 'package:startwell/services/working_days_service.dart';

// Get working days for current month
final response = await WorkingDaysService.getCurrentMonthWorkingDays(
  mealType: 'lunch',
);

// Check if successful
if (response.success) {
  print('Enabled dates: ${response.enabledDates.length}');
  for (DateTime date in response.enabledDates) {
    print('Available: ${date.day}/${date.month}/${date.year}');
  }
}
```

### 2. With Custom Days

```dart
// Define custom weekdays (Monday, Wednesday, Friday)
final List<bool> selectedWeekdays = [true, false, true, false, true, false, false];

// Convert to API format
final customDays = WorkingDaysService.convertSelectedWeekdaysToCustomDays(selectedWeekdays);

// Fetch working days
final response = await WorkingDaysService.getCurrentMonthWorkingDays(
  mealType: 'lunch',
  customDays: customDays, // [1, 3, 5]
);
```

### 3. For Specific Month

```dart
// Get working days for July 2025
final response = await WorkingDaysService.getWorkingDays(
  mealType: 'breakfast',
  monthSelected: DateTime(2025, 7, 1),
  customDays: [1, 2, 3, 4, 5], // Monday to Friday
);
```

### 4. Date Range

```dart
// Get working days for a date range
final response = await WorkingDaysService.getWorkingDaysForDateRange(
  mealType: 'lunch',
  startDate: DateTime(2025, 7, 1),
  endDate: DateTime(2025, 8, 31),
  customDays: [1, 3, 5],
);
```

## Integration with Calendar

### Using WorkingDaysCalendar Widget

```dart
import 'package:startwell/widgets/calendar/working_days_calendar.dart';

WorkingDaysCalendar(
  mealType: 'lunch',
  customDays: [1, 3, 5], // Monday, Wednesday, Friday
  onDateSelected: (DateTime selectedDate) {
    print('User selected: $selectedDate');
  },
  onPageChanged: (DateTime focusedMonth) {
    print('Calendar moved to: $focusedMonth');
  },
)
```

### Manual Calendar Integration

```dart
// In your calendar widget
enabledDayPredicate: (day) {
  // Check if date is in the past
  final now = DateTime.now();
  final today = DateTime(now.year, now.month, now.day);
  final dayOnly = DateTime(day.year, day.month, day.day);
  
  if (dayOnly.isBefore(today)) {
    return false;
  }
  
  // Use working days API data
  if (_enabledDates.isNotEmpty && !_isLoadingWorkingDays) {
    return _enabledDates.any((enabledDate) {
      final enabledDateOnly = DateTime(enabledDate.year, enabledDate.month, enabledDate.day);
      return enabledDateOnly == dayOnly;
    });
  }
  
  // Fallback to weekdays only
  return day.weekday <= 5;
},
```

## Error Handling

The service includes comprehensive error handling:

```dart
try {
  final response = await WorkingDaysService.getCurrentMonthWorkingDays(
    mealType: 'lunch',
  );
  
  if (response.success) {
    // Use response.enabledDates
  } else {
    // Handle API error
    print('API Error: ${response.message}');
  }
} catch (e) {
  // Handle network/parsing errors
  print('Network Error: $e');
  
  // Use fallback dates (weekdays only)
  final fallbackDates = _generateWeekdayFallback();
}
```

## Configuration

Update the configuration in `lib/services/working_days_service.dart`:

```dart
class WorkingDaysService {
  static const String baseUrl = 'http://*************:8005/api/v2/admin/settings';
  static const int companyId = 8163;  // Update for your company
  static const int kitchenId = 1;     // Update for your kitchen
}
```

## Response Format

The API response is parsed into a `WorkingDaysResponse` object:

```dart
class WorkingDaysResponse {
  final bool success;
  final String message;
  final List<DateTime> enabledDates;
  final List<DateTime> disabledDates;
  final Map<String, dynamic>? metadata;
}
```

## Utility Methods

### Check if Date is Working Day

```dart
final isWorking = WorkingDaysService.isWorkingDay(
  DateTime(2025, 7, 22),
  workingDaysResponse,
);
```

### Convert Weekday Selection

```dart
final selectedWeekdays = [true, false, true, false, true, false, false];
final customDays = WorkingDaysService.convertSelectedWeekdaysToCustomDays(selectedWeekdays);
// Returns: [1, 3, 5] for Monday, Wednesday, Friday
```

### Validate Configuration

```dart
final isValid = WorkingDaysService.validateApiConfiguration();
```

## Integration Points

The working days API is already integrated in:

1. **Subscription Selection Screen** (`lib/screens/subscription_selection_screen.dart`)
   - Loads working days when meal type or custom days change
   - Updates calendar `enabledDayPredicate`

2. **Calendar Widgets** (`lib/widgets/calendar/`)
   - Smart calendar widget with working days support
   - Custom calendar builders for enabled/disabled dates

3. **Date Utilities** (`lib/utils/date_utils.dart`)
   - Date calculation helpers
   - Delivery date validation

## Testing

Run the working days integration:

1. Navigate to the demo screen (if created)
2. Select different meal types
3. Toggle custom weekdays
4. Test API calls with different parameters
5. Verify calendar shows correct enabled/disabled dates

## Testing the Integration

### Quick API Test

Use the test helper to verify the API is working:

```dart
import 'package:startwell/utils/working_days_test_helper.dart';

// Quick test
final isWorking = await WorkingDaysTestHelper.quickApiTest();

// Full test suite
await WorkingDaysTestHelper.testWorkingDaysAPI();

// Test weekday conversion
WorkingDaysTestHelper.testWeekdayConversion();

// Test with current app parameters
await WorkingDaysTestHelper.testWithCurrentParameters(
  mealType: 'lunch',
  isCustomPlan: true,
  selectedWeekdays: [true, false, true, false, true, false, false],
);
```

### Debug Logging

The integration includes comprehensive debug logging:

```dart
// In subscription_selection_screen.dart, you'll see:
🗓️ Loading working days for meal type: lunch
🗓️ Custom days: [1, 3, 5]
🗓️ Is custom plan: true
✅ Working days loaded successfully: 12 enabled dates
📅 Enabled dates: 21/7/2025, 23/7/2025, 25/7/2025, ...

// When change date button is pressed:
Change date button pressed. _enabledDates: 12 dates
DEBUG: _enabledDates count: 12
DEBUG: Enabled dates: 21/7/2025, 23/7/2025, 25/7/2025, ...
```

## Troubleshooting

### Common Issues

1. **Network Error**: Check if API server is running on `http://*************:8005`
2. **Empty Response**: Verify company_id and kitchen_id are correct
3. **Date Parsing Error**: Check API response format matches expected structure
4. **Calendar Not Updating**: Ensure `_loadWorkingDays()` is called when parameters change
5. **Literal String in Logs**: Fixed - now shows actual enabled dates count and list

### Debug Steps

1. **Check API Configuration**:
   ```dart
   final isValid = WorkingDaysService.validateApiConfiguration();
   print('API config valid: $isValid');
   ```

2. **Test API Directly**:
   ```dart
   await WorkingDaysTestHelper.quickApiTest();
   ```

3. **Check Network Connectivity**:
   - Ensure device can reach `http://*************:8005`
   - Verify API server is running
   - Check firewall settings

4. **Verify Parameters**:
   - company_id: 8163
   - kitchen_id: 1
   - meal_type: 'breakfast', 'lunch', or 'dinner'
   - Custom days format: `days[]=1&days[]=3&days[]=5`

### Debug Logging

All API requests and responses are logged:

```dart
// Check Flutter console for:
🌐 Fetching working days from: [URL]
📡 Working days API response status: [STATUS]
📡 Working days API response body: [JSON]
✅ Working days loaded successfully: [COUNT] enabled dates
📅 Enabled dates: [DATE_LIST]
```
