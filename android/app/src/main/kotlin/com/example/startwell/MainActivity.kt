package com.example.startwell

import io.flutter.embedding.android.FlutterActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodChannel

class MainActivity : FlutterActivity() {
    private val CHANNEL = "com.startwell.phonepe/checkout"

    override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)
        
        MethodChannel(flutterEngine.dartExecutor.binaryMessenger, CHANNEL).setMethodCallHandler { call, result ->
            when (call.method) {
                "startPhonePeTransaction" -> {
                    try {
                        val url = call.argument<String>("url")
                        val packageName = call.argument<String>("packageName") ?: "com.phonepe.app"
                        val checksum = call.argument<String>("checksum")
                        val environment = call.argument<String>("environment") ?: "UAT"
                        
                        if (url == null || checksum == null) {
                            result.error("INVALID_ARGUMENTS", "URL and checksum are required", null)
                            return@setMethodCallHandler
                        }

                        // For now, we'll return success - actual PhonePe SDK integration will be added when AAR is downloaded
                        result.success(mapOf(
                            "status" to "PAYMENT_INITIATED",
                            "message" to "PhonePe payment initiated successfully"
                        ))
                        
                    } catch (e: Exception) {
                        result.error("PHONEPE_ERROR", "Failed to start PhonePe transaction: ${e.message}", null)
                    }
                }
                "isPhonePeInstalled" -> {
                    try {
                        val packageManager = applicationContext.packageManager
                        val isInstalled = try {
                            packageManager.getPackageInfo("com.phonepe.app", 0)
                            true
                        } catch (e: Exception) {
                            false
                        }
                        result.success(isInstalled)
                    } catch (e: Exception) {
                        result.error("CHECK_ERROR", "Failed to check PhonePe installation", null)
                    }
                }
                else -> {
                    result.notImplemented()
                }
            }
        }
    }
}
