
# 📄 Product Requirements Document (PRD)

**Project Name:** StartWell – API Integration Status
**Prepared by:** \[Your Name]
**Date:** July 15, 2025
**Version:** 1.1

---

## 🎯 Objective

Track the implementation status of all backend APIs required for the **StartWell** module inside the `new_one_app` Flutter application, including what has been integrated and what is still pending.

---

## ✅ APIs Already Integrated

| Feature/Module         | API Endpoint                            | Description                                          | Status |
| ---------------------- | --------------------------------------- | ---------------------------------------------------- | ------ |
| Promo Code Validation  | `/api/v2/promocode/validate`            | Validates applied promo codes                        | ✅ Done |
| School List            | `/api/v2/schools/list`                  | Fetches school list to populate dropdown             | ✅ Done |
| Plan Duration          | `/api/v2/plans/duration`                | Fetches available durations for meal plans           | ✅ Done |
| Delivery Days          | `/api/v2/plans/delivery-days`           | Returns available delivery options (weekdays/custom) | ✅ Done |
| Class, Division, Floor | `/api/v2/students/class-division-floor` | Fetches dropdown values for student profile form     | ✅ Done |
| Payment Modes          | `/api/v2/payment/modes`                 | Lists all available payment options                  | ✅ Done |

---

## ⏳ APIs Pending Integration

| Feature/Module             | API Endpoint                                   | Description                                               | Target Date | Priority |
| -------------------------- | ---------------------------------------------- | --------------------------------------------------------- | ----------- | -------- |
| Calendar Date Conditions   | `/api/v2/plans/calendar-conditions` (expected) | Used to show selectable dates conditionally               | TBD         | High     |
| Student Profile Submission | `/api/v2/students/save` (expected)             | To save selected class/division/floor for student profile | TBD         | Medium   |
| Final Payment Integration  | `/pg/v1/pay` (PhonePe / Razorpay)              | Triggers payment with selected payment mode               | In Progress | High     |

---

## 🧩 Assumptions

* All APIs are REST-based and respond in JSON format
* All endpoints are accessible through a Kong API Gateway
* Authentication is handled via Keycloak
* Backend responses follow this structure:

  ```json
  {
    "success": true,
    "message": "Data fetched successfully",
    "data": { ... }
  }
  ```

---

## 🔧 Action Items

* [ ] Integrate calendar condition API and bind logic to date selection
* [ ] Implement student profile submission logic
* [ ] Complete PhonePe / Razorpay payment SDK integration and order placement
* [ ] Perform functional and API QA on completed modules

---

Let me know if you want this exported to Markdown, PDF, or Notion-style format.
