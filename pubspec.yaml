name: startwell
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: '>=3.2.3 <4.0.0'

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.2
  google_fonts: ^5.1.0
  shimmer: ^3.0.0
  provider: ^6.1.1
  table_calendar: ^3.0.9
  intl: ^0.18.1
  shared_preferences: ^2.5.3
  image_picker: ^1.0.5
  path_provider: ^2.1.1
  path: ^1.8.3
  package_info_plus: ^4.2.0
  google_maps_flutter: ^2.5.3
  url_launcher: ^6.2.2
  carousel_slider: ^5.0.0
  smooth_page_indicator: ^1.1.0
  font_awesome_flutter: ^10.4.0
  visibility_detector: ^0.4.0+2
  fluttertoast: ^8.2.12
  cached_network_image: ^3.3.1
  get: ^4.6.6
  lottie: ^3.1.2
  phone_pe_pg: ^0.0.7

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^2.0.0
  flutter_launcher_icons: ^0.13.1

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/
    - assets/images/breakfast/
    - assets/images/lunch/
    - assets/images/express/
    - assets/images/payment/
    - assets/images/carousel/
    - assets/icons/
    - assets/animations/
    - assets/icons/Natural Ingredients.png
    - assets/icons/Designed by Child Nutritionists.png
    - assets/icons/Prepared by Chefs & Mothers.png
    - assets/images/carousel/Designed_by_Child_Nutritionists.png
    - assets/images/carousel/Natural_ingridents.png
    - assets/images/carousel/prepared_by_chefs&mothers.png
    - assets/images/breakfast/breakfast of the day (most recommended).png
    - assets/images/breakfast/Indian Breakfast.png
    - assets/images/breakfast/International Breakfast.png 
    - assets/images/breakfast/Jain Breakfast.png
    - assets/images/lunch/lunch of the day (most recommended).png
    - assets/images/lunch/Indian Lunch.png
    - assets/images/lunch/International Lunch.png
    - assets/images/lunch/Jain Lunch.png
    - assets/images/veg.png
    - assets/images/logo.png
    - assets/images/payment/wallet.png
    - assets/images/payment/phonepe.png
    - assets/images/payment/gpay.png
    - assets/images/payment/paytm.png
    - assets/images/background_footer.png
    # App logo

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages

# App icon configuration
flutter_icons:
  android: true
  ios: true
  image_path: "assets/images/app_icon.png"
