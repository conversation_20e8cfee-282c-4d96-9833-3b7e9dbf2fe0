PODS:
  - file_selector_macos (0.0.1):
    - FlutterMacOS
  - flutter_inappwebview_macos (0.0.1):
    - FlutterMacOS
    - OrderedSet (~> 6.0.3)
  - FlutterMacOS (1.0.0)
  - OrderedSet (6.0.3)
  - package_info_plus (0.0.1):
    - FlutterMacOS
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - sqflite_darwin (0.0.4):
    - Flutter
    - FlutterMacOS
  - url_launcher_macos (0.0.1):
    - FlutterMacOS

DEPENDENCIES:
  - file_selector_macos (from `Flutter/ephemeral/.symlinks/plugins/file_selector_macos/macos`)
  - flutter_inappwebview_macos (from `Flutter/ephemeral/.symlinks/plugins/flutter_inappwebview_macos/macos`)
  - FlutterMacOS (from `Flutter/ephemeral`)
  - package_info_plus (from `Flutter/ephemeral/.symlinks/plugins/package_info_plus/macos`)
  - path_provider_foundation (from `Flutter/ephemeral/.symlinks/plugins/path_provider_foundation/darwin`)
  - shared_preferences_foundation (from `Flutter/ephemeral/.symlinks/plugins/shared_preferences_foundation/darwin`)
  - sqflite_darwin (from `Flutter/ephemeral/.symlinks/plugins/sqflite_darwin/darwin`)
  - url_launcher_macos (from `Flutter/ephemeral/.symlinks/plugins/url_launcher_macos/macos`)

SPEC REPOS:
  trunk:
    - OrderedSet

EXTERNAL SOURCES:
  file_selector_macos:
    :path: Flutter/ephemeral/.symlinks/plugins/file_selector_macos/macos
  flutter_inappwebview_macos:
    :path: Flutter/ephemeral/.symlinks/plugins/flutter_inappwebview_macos/macos
  FlutterMacOS:
    :path: Flutter/ephemeral
  package_info_plus:
    :path: Flutter/ephemeral/.symlinks/plugins/package_info_plus/macos
  path_provider_foundation:
    :path: Flutter/ephemeral/.symlinks/plugins/path_provider_foundation/darwin
  shared_preferences_foundation:
    :path: Flutter/ephemeral/.symlinks/plugins/shared_preferences_foundation/darwin
  sqflite_darwin:
    :path: Flutter/ephemeral/.symlinks/plugins/sqflite_darwin/darwin
  url_launcher_macos:
    :path: Flutter/ephemeral/.symlinks/plugins/url_launcher_macos/macos

SPEC CHECKSUMS:
  file_selector_macos: 6280b52b459ae6c590af5d78fc35c7267a3c4b31
  flutter_inappwebview_macos: c2d68649f9f8f1831bfcd98d73fd6256366d9d1d
  FlutterMacOS: 8f6f14fa908a6fb3fba0cd85dbd81ec4b251fb24
  OrderedSet: e539b66b644ff081c73a262d24ad552a69be3a94
  package_info_plus: 122abb51244f66eead59ce7c9c200d6b53111779
  path_provider_foundation: 080d55be775b7414fd5a5ef3ac137b97b097e564
  shared_preferences_foundation: 9e1978ff2562383bd5676f64ec4e9aa8fa06a6f7
  sqflite_darwin: 20b2a3a3b70e43edae938624ce550a3cbf66a3d0
  url_launcher_macos: 0fba8ddabfc33ce0a9afe7c5fef5aab3d8d2d673

PODFILE CHECKSUM: 7eb978b976557c8c1cd717d8185ec483fd090a82

COCOAPODS: 1.16.2
