# PhonePe Standard Checkout SDK (v2) Integration Guide

## ✅ Complete Integration Status

The PhonePe SDK has been successfully integrated into the Flutter app with the following components:

### 📱 **Android Configuration**
- ✅ Created `android/app/libs/` directory for AAR file
- ✅ Updated `android/build.gradle.kts` with flatDir repository
- ✅ Updated `android/app/build.gradle.kts` with PhonePe dependency and viewBinding
- ✅ Enhanced `MainActivity.kt` with PhonePe MethodChannel

### 🚀 **Flutter Services**
- ✅ `PhonePeService` - MethodChannel communication
- ✅ `PhonePePaymentService` - Backend API integration  
- ✅ `PhonePeResponse` & `PhonePePaymentResponse` models
- ✅ Full payment flow integration in `PaymentMethodScreen`

---

## 🔧 **Setup Requirements**

### 1. **Download PhonePe AAR File**

**⚠️ IMPORTANT:** You need to download the PhonePe Android SDK AAR file:

1. Visit: [PhonePe Flutter SDK Documentation](https://developer.phonepe.com/v1/reference/flutter-sdk-integration-standard-2)
2. Download `phonepe-standard-checkout.aar`
3. Place it in: `android/app/libs/phonepe-standard-checkout.aar`

### 2. **Backend API Endpoints Required**

Your backend needs to implement these endpoints:

```
POST /api/v2/payment/phonepe/initiate
GET  /api/v2/payment/phonepe/status/{merchantTransactionId}
```

**Request Format for `/phonepe/initiate`:**
```json
{
  "orderId": "SW_1234567890",
  "amount": 50000,
  "currency": "INR", 
  "customerId": "123",
  "customerPhone": "9999999999",
  "customerName": "John Doe",
  "paymentGateway": "phonepe",
  "callbackUrl": "https://webhook.site/callback-url",
  "redirectUrl": "https://your-app.com/payment-success"
}
```

**Expected Response:**
```json
{
  "success": true,
  "data": {
    "merchantTransactionId": "MT123456789",
    "paymentUrl": "upi://pay?...",
    "checksum": "base64-encoded-checksum",
    "status": "PENDING"
  }
}
```

---

## 🔐 **Security Implementation**

### **Checksum Generation (Backend)**
```javascript
// Node.js example
const crypto = require('crypto');

function generateChecksum(payload, endpoint, saltKey) {
  const base64Payload = Buffer.from(JSON.stringify(payload)).toString('base64');
  const string = base64Payload + endpoint + saltKey;
  const sha256 = crypto.createHash('sha256').update(string).digest('hex');
  return sha256 + '###' + saltIndex;
}
```

### **Environment Configuration**
- **UAT**: For testing with PhonePe sandbox
- **PROD**: For production with live PhonePe

---

## 🏃‍♂️ **How It Works**

### **Payment Flow:**

1. **User selects PhonePe** in PaymentMethodScreen
2. **App checks** if PhonePe is installed (`PhonePeService.isPhonePeInstalled()`)
3. **Backend call** to initiate payment (`PhonePePaymentService.initiatePayment()`)
4. **PhonePe SDK launch** with payment URL and checksum
5. **Handle response** - Success/Failed/Pending
6. **Status verification** for pending payments

### **Code Example:**
```dart
// In your payment flow
await _handlePhonePePayment(finalAmount);

// This calls:
// 1. PhonePeService.isPhonePeInstalled()
// 2. PhonePePaymentService.initiatePayment()  
// 3. PhonePeService.startTransaction()
// 4. _handlePhonePeResponse()
```

---

## 📱 **User Experience**

### **Success Flow:**
1. ✅ PhonePe app opens automatically
2. ✅ User completes payment in PhonePe
3. ✅ Returns to app with success dialog
4. ✅ Shows transaction ID and order details
5. ✅ Navigates to home screen

### **Error Handling:**
- ❌ **PhonePe not installed** → Install dialog
- ❌ **Payment failed** → Error dialog with retry option
- ⏳ **Payment pending** → Status check with confirmation
- 🔄 **Network errors** → Graceful error messages

---

## 🎯 **Integration Points**

### **In PaymentMethodScreen:**
- When gateway type is `"phonepe"` or `"upi"`
- Calls `_handlePhonePePayment(finalAmount)`
- Shows loading states and result dialogs

### **MethodChannel Communication:**
```dart
// Flutter to Android
PhonePeService.startTransaction(
  url: paymentUrl,
  checksum: checksum,
  environment: 'UAT'
)

// Android to Flutter  
{
  "status": "PAYMENT_SUCCESS",
  "message": "Payment completed",
  "transactionId": "T123456789"
}
```

---

## 🚨 **Important Notes**

| Component | Details |
|-----------|---------|
| **AAR File** | Must be manually downloaded and placed in `android/app/libs/` |
| **Package Name** | Use `com.phonepe.app` for production |
| **Environment** | `UAT` for testing, `PROD` for production |
| **Security** | Never expose Salt Key in Flutter code |
| **Platform** | Android only (iOS not supported by PhonePe) |
| **Backend** | Required for checksum generation and payment initiation |

---

## 🔄 **Testing Checklist**

- [ ] Download and place PhonePe AAR file
- [ ] Install PhonePe app on test device
- [ ] Configure backend API endpoints
- [ ] Test with UAT environment
- [ ] Verify checksum generation
- [ ] Test all payment states (Success/Failed/Pending)
- [ ] Test PhonePe not installed scenario
- [ ] Verify payment status checking
- [ ] Test error handling and retry flows

---

## 📞 **API Integration Details**

### **Current Base URL:**
```dart
static const String baseUrl = 'http://192.168.1.167:8003/api/v2/payment';
```

### **Request Headers:**
```dart
headers: {
  'Content-Type': 'application/json',
  'Accept': 'application/json',
}
```

### **Order ID Format:**
```dart
final String orderId = 'SW_${DateTime.now().millisecondsSinceEpoch}';
```

---

## ✨ **Ready to Use**

The integration is **complete** and ready for testing once you:

1. ✅ Download the PhonePe AAR file
2. ✅ Implement backend API endpoints  
3. ✅ Configure proper checksum generation
4. ✅ Install PhonePe app for testing

The app will seamlessly handle PhonePe payments with proper error handling, status checking, and user feedback! 🎉 